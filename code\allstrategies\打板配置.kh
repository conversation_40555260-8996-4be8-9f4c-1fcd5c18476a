{"system": {"userdata_path": "D:\\国金证券QMT交易端\\userdata_mini", "init_data_enabled": true}, "run_mode": "backtest", "account": {"account_id": "**********", "account_type": "STOCK"}, "strategy_file": "I:/qmt5/code/strategies/打板策略_性能监控版.py", "data_mode": "single_quote", "backtest": {"start_time": "********", "end_time": "********", "init_capital": 1000000.0, "min_volume": 100, "benchmark": "sh.000300", "trade_cost": {"min_commission": 5.0, "commission_rate": 0.0001, "stamp_tax_rate": 0.001, "flow_fee": 0.0, "slippage": {"type": "ratio", "tick_size": 0.01, "tick_count": 2, "ratio": 0.005}}, "trigger": {"type": "1m", "custom_times": ["09:30:00"], "start_time": "09:30:00", "end_time": "15:00:00", "interval": 300}}, "data": {"kline_period": "1m", "dividend_type": "front", "fields": ["open", "high", "low", "close", "volume", "amount", "settelementPrice", "openInterest", "preClose", "suspendFlag"], "stock_list_file": "I:\\qmt5\\code\\data\\stock_list\\stock_list_1749869355.csv"}, "market_callback": {"pre_market_enabled": true, "pre_market_time": "08:30:00", "post_market_enabled": false, "post_market_time": "15:30:00"}, "risk": {"position_limit": 0.95, "order_limit": 100, "loss_limit": 0.1}}