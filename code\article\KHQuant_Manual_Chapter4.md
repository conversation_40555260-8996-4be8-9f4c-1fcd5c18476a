# 第四章：快速上手：运行第一个回测

本章将引导使用一个预先配置好的工程文件（`.kh`文件），来快速完整地运行一次回测。这个过程不仅能直观地了解软件的基本操作流程，也是一个检验当前运行环境是否配置妥当、能否与本系统完美兼容的有效方式。

## 4.1 加载并配置示例工程

软件中内置了一些经典的策略示例，以供快速学习和测试。下面将加载其中一个进行演示。

### 4.1.1 加载示例工程

1. 找到并点击主界面顶部工具栏上的 **"加载配置"** 按钮。
2. 在弹出的文件选择对话框中，找到软件安装目录下的`_internal\strategies`目录，例如'C:\Program Files\KhQuant\_internal\strategies'。
3. 选择示例工程文件 `双均线.kh`，然后点击"打开"。

### 4.1.2 策略文件路径设置

4. **检查并重设策略文件路径**：由于每个用户的软件安装路径可能不同，加载 `.kh` 文件后，可能需要手动重新指定策略文件的位置。点击"策略文件"输入框右侧的浏览按钮。

   > ⚠️ **重要提醒：策略文件保存位置**
   >
   > 为了避免在软件升级或重新安装时丢失您的策略文件，**强烈建议不要将策略文件保存在软件安装目录下**。推荐的保存位置：
   >
   > - **用户策略目录**（推荐）：软件会自动为您创建专用的策略目录
   >   - Windows: `C:\Users\<USER>\AppData\Local\KhQuant\strategies\`
   > - **自定义目录**：如 `D:\MyStrategies\` 或 `C:\Users\<USER>\Documents\KhQuant\`
   >
   > **如何使用用户策略目录：**
   >
   > 1. 在策略文件选择对话框中，默认会打开用户策略目录
   > 2. 首次使用时，系统会自动复制默认策略文件到此目录
   > 3. 您也可以手动导航到该目录进行文件管理
   >
5. 在策略文件选择对话框中，建议选择用户策略目录中的 `双均线.py` 文件。如果您是首次使用，系统已经自动将示例策略复制到了用户目录中。

### 4.1.3 保存配置

> 💡 **提示**：设置好正确的策略路径后，可以点击工具栏上的 **"配置另存为"** 按钮覆盖原有的 `双均线.kh` 文件。这样，下次加载时就不再需要重新选择了。同样建议将 `.kh` 配置文件也保存到用户目录或自定义目录中，而不是软件安装目录。

加载成功并设置好策略路径后，主界面参数会自动填充完毕。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/加载后的参数.png" />
</p>

<p align="center">加载并配置好工程文件后的主界面</p>

> 💡 **什么是 `.kh` 文件？**
>
> `.kh` 文件是"看海量化交易系统"的**工程配置文件**。它就像是工作台的快照，完整地保存了在图形界面上所做的所有设置，包括选择的策略脚本、回测时间、股票池、交易费率等等。
>
> **重点在于，`.kh` 文件本质上是一个可读的文本文件（使用JSON格式）。** 这意味着完全可以用任何文本编辑器（如记事本、VS Code等）打开它，来查看甚至直接修改其中的配置参数。

## 4.1.4 策略文件管理最佳实践

### 🛡️ 文件安全保护机制

软件采用了智能的文件保护机制：

1. **自动目录创建**：首次运行时，系统会自动创建用户策略目录
2. **默认策略复制**：将内置的示例策略复制到用户目录，但不会覆盖已存在的文件
3. **智能路径提示**：选择策略文件时，如果文件不在安全目录中，系统会提醒您
4. **升级保护**：用户目录中的文件在软件升级时完全不受影响

### 🔧 管理策略文件

1. **访问策略目录**：通过策略文件选择对话框导航到用户策略目录
2. **手动打开目录**：直接在文件管理器中导航到用户策略目录路径
3. **管理策略文件**：您可以在此目录中：
   - 查看和编辑现有策略文件
   - 添加新的策略文件
   - 创建策略文件的备份
   - 组织策略文件的目录结构

### ⚠️ 避免的操作

- **不要**将策略文件保存在软件安装目录（如 `C:\Program Files\KhQuant\`）
- **不要**直接修改软件安装目录中的示例文件
- **不要**将重要的策略文件只保存一份，建议定期备份

### 💡 迁移现有策略

如果您已经在软件安装目录中创建了策略文件，建议按以下步骤迁移：

1. 手动打开用户策略目录（Windows: `C:\Users\<USER>\AppData\Local\KhQuant\strategies\`）
2. 将您的策略文件复制到用户策略目录中
3. 重新加载配置文件，并重新选择策略文件路径
4. 保存配置文件到用户目录或其他安全位置

## 4.2 准备回测数据（数据补充）

在启动回测之前，需要确保本地数据库中包含了策略所需的全部历史数据。新版的看海量化交易系统提供了一个强大的 **本地数据管理器**，它不仅集成了数据补充功能，还允许您直接查看和检验本地的数据，确保万无一失。

> ⚠️ **重要提醒：数据补充检查**
>
> **请特别注意：** 即使本地数据库中没有足够的历史数据，回测程序依然能够启动并正常运行，但这可能导致策略计算结果不准确或产生错误的回测报告。因此，在编写和运行回测程序时，务必仔细检查数据是否已经被正确补充到本地。
>
> **对于本次演示的双均线策略，特别需要确保以下两种周期的数据都已完整补充：**
>
> - **分钟数据**：用于策略的主要交易逻辑计算
> - **日线数据**：用于移动平均参数计算
>
> 缺失任何一种周期的数据都可能影响策略的正确运行和回测结果的成功。

下面是使用新版 **本地数据管理器** 来补充和检验数据的步骤：

1. 在主界面顶部工具栏点击 **"本地数据管理"** 按钮，打开 **本地数据管理器** 模块。

   这个管理器界面由三个主要部分构成，从左到右依次是：

   * **左侧：数据补充工具**: 这是您进行数据下载和管理的核心区域。您可以在这里选择或导入股票列表、设定数据周期和日期范围，并发起数据补充任务。
   * **中间：数据结构树**: 该区域以树状结构清晰地展示了您本地数据库中所有已缓存的数据，按 `交易所 -> 数据周期` 的层级进行组织。
   * **右侧：数据展示区**: 当您在中间的树状结构中选择某一项时，这里会显示详细的数据内容。例如，点击一个周期，这里会列出该周期下的所有股票；点击具体的一只股票，这里则会以表格形式展示其详细的K线或Tick数据。
2. 在 **左侧的数据补充工具** 中，找到相关功能面板，设置如下：

   * **股票列表**: 左侧数据补充工具包含两个股票管理区域：
     * **股票代码列表文件区域**：您可以勾选预设的股票池（如"沪深A股"、"创业板"、"沪深300成分股"等）来快速批量选择股票。
     * **当前股票列表区域**：在这里您可以：
       - 点击 **"添加股票"** 按钮，在弹出的对话框中手动输入股票代码（如 `000001.SZ`）
       - 点击 **"导入文件"** 按钮，选择包含股票代码的 `csv` 或 `txt` 文件进行批量导入
     * 本次演示，我们推荐在"当前股票列表"区域中直接添加单只股票 `000001.SZ`，或勾选自选清单股票池。

   > ⚠️ **免责声明**
   >
   > 本教程中使用的股票（如 `000001.SZ,平安银行`）及其代码仅为功能演示目的，并非投资建议或推荐。市场有风险，投资需谨慎。请在实际使用中替换为您自己研究的投资标的。
   >

   * **周期类型**: 从下拉框中选择需要的数据周期，如 `1m`（1分钟）和 `1d`（日线）。注意这里一次只能选择一个周期，如需多个周期，需要分别进行补充。
   * **日期范围**: 设置开始日期为 `2025-01-01`，结束日期为 `2025-07-01`。
3. 分别补充两种周期的数据：

   * 首先在"周期类型"下拉框中选择 `1m`，然后点击 **"补充数据"** 按钮，等待1分钟数据下载完成。
   * 接下来在"周期类型"下拉框中选择 `1d`，再次点击 **"补充数据"** 按钮，下载日线数据。
4. **（新功能）检验数据**: 数据补充完成后，可以直接在 **中间的数据结构树** 中进行验证（注意，第一次打开）：

   * 展开 `深交所 (SZ)` 并点击 `1m数据`。此时，**右侧的数据展示区** 就会列出该周期下所有已下载数据的股票。
   * 在右侧列表中单击 `000001` 这只股票。
   * 此时，右侧的数据展示区会自动更新，以表格形式展示刚刚下载的1分钟K线数据。
   * 同样地，您也可以通过点击树状图中的`日线数据`来检验日线是否已成功下载。

   这个"所见即所得"的功能是新版数据管理器的核心优势，它让您在回测前就能对数据质量做到心中有数。


<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/07/bandicam-2025-07-06-22-53-43-804.gif" />
</p>


等待两个周期的数据都补充完成的提示出现后，就可以进行下一步的回测了。

### "数据补充"与"数据下载"的区别

> **"本地数据管理器"** 的设计目标是统一服务于 **系统内部** 的回测功能。它的核心任务是更新和完善 MiniQMT 内部所依赖的本地历史数据库，为策略回测引擎提供坚实、可靠且可被检验的数据基础。换句话说，回测系统中使用到的数据，都是通过"数据补充"提前下载到本地的。这样做不仅可以极大提升回测速度，还能通过内置的数据浏览器确保数据质量。技术上讲，"数据补充"调用的是xtquant的`download_history_data`函数，而回测过程中则通过`get_market_data_ex`来高速读取本地数据。

> 如果您需要将数据导出为独立的 **`.csv` 文件** 以便在外部（如Excel, Python, R）进行分析，请使用主界面的 **"CSV数据管理"** 功能。

## 4.3 开始回测与观察

当工程文件加载完毕，数据也准备就绪后，就可以启动回测了。

1. 点击工具栏上的 **"开始运行"** 按钮。
2. 此时，回测开始进行，可以将注意力转移到界面的以下两个区域：

   <p align="center">
       <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/bandicam-2025-06-12-01-28-29-193.gif" />
   </p>

   * **右侧的"系统日志"面板**：这里会实时滚动输出策略运行的详细信息，包括数据下载进度、策略初始化状态、交易信号的触发、订单的委托与成交等。如果出现任何问题，错误信息也会在这里以醒目的颜色显示。
   * **底部的状态栏**：这里会显示一个详细的进度条，告知当前回测进行到了哪一天，以及总体的完成百分比。

## 4.4 解读第一个回测报告

等待回测进度条走到100%，系统日志中也会提示"回测运行结束"。此时，回测报告将自动弹出。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/回测结果分析.png" />
</p>

<p align="center">回测报告窗口</p>

如果后续不小心关闭了报告窗口，可以随时点击日志面板上方的 **"打开回测指标"** 按钮来重新打开它。

这份报告浓缩了策略在历史数据中的全部表现，主要包含：

* **核心绩效指标**：如总收益率、年化收益、最大回撤、夏普比率等。
* **可视化图表**：包括策略净值与基准对比的资金曲线、回撤曲线等。
* **详细交易记录**：每一笔买入和卖出的明细。
* **每日持仓与资产快照**：方便复盘策略在任何一天的具体状态。

关于如何深入解读这份报告中的每一项数据，我们将在后续的【第九章：策略绩效复盘】中进行详尽的拆解。现在，只需要对它有一个初步的印象即可。

恭喜！已经成功完成了在KHQuant中的第一次策略回测。接下来，可以尝试更深入的策略研究了。

## 4.5 重要提醒：保护您的策略文件

在结束本章之前，再次强调策略文件安全的重要性：

> ⚠️ **策略文件安全检查清单**
>
> 在继续使用软件之前，请确认：
>
> - ✅ 已了解用户策略目录的位置
> - ✅ 已确认策略文件选择对话框默认打开用户策略目录
> - ✅ 重要的策略文件已保存在安全位置（非软件安装目录）
> - ✅ 已为重要策略文件创建备份
> - ✅ 配置文件（.kh文件）也保存在安全位置
>
> **记住：软件安装目录中的文件在升级时可能会被覆盖！**

通过遵循这些最佳实践，就可以安心地使用KHQuant进行策略开发，而不用担心重要文件的丢失。
