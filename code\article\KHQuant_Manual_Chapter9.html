<!DOCTYPE html>
<html>
<head>
<title>KHQuant_Manual_Chapter9.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E7%AC%AC%E4%B9%9D%E7%AB%A0%E7%AD%96%E7%95%A5%E5%A4%8D%E7%9B%98%E8%A7%A3%E8%AF%BB%E5%9B%9E%E6%B5%8B%E6%88%90%E7%BB%A9%E5%8D%95">第九章：策略复盘：解读回测“成绩单”</h1>
<p>0当.</p>
<p>回测的进度条走到100%，最激动人心的时刻便到来了。系统会自动弹出一个独立的回测报告窗口，这是对您策略在历史长河中表现的全面总结。如果说编写策略是“播种”，那么解读这份报告就是“收获”。本章将带您逐一拆解这份报告，学习如何从这份“成绩单”中洞察策略的优劣。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/回测结果分析.png" alt="回测报告窗口" />
</p>
<p align="center">回测报告窗口概览</p>
<hr>
<h2 id="91-%E6%A6%82%E8%A7%88%E9%83%A8%E5%88%86%E6%94%B6%E7%9B%8A%E6%9B%B2%E7%BA%BF%E4%B8%8E%E5%85%B3%E9%94%AE%E6%8C%87%E6%A0%87">9.1 概览部分：收益曲线与关键指标</h2>
<p>打开窗口，最先映入眼帘的就是策略的整体表现总结，它清晰地分为“基本信息”和“资金曲线”两部分，旨在让用户对策略表现快速建立量化认知。</p>
<h3 id="911-%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AF">9.1.1 基本信息</h3>
<p>该区域是策略回测的<strong>数字摘要</strong>，以表格形式清晰列出了超过20项关键绩效指标（KPIs），包括策略名称、回测周期、初始与最终资金、总收益率、年化收益率、最大回撤、夏普比率等。这些精确计算的指标，如同策略的“体检报告”，让您可以快速抓住策略的亮点与短板，为初步评估提供客观依据。</p>
<h3 id="912-%E8%B5%84%E9%87%91%E6%9B%B2%E7%BA%BF">9.1.2 资金曲线</h3>
<p>资金曲线是报告中最重要的<strong>可视化图表</strong>，它将策略的净值变化过程以“心电图”的形式直观呈现。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/交互式图表.gif" alt="回测报告窗口交互" />
</p>
<p>这张图表集成了丰富的信息维度：</p>
<ul>
<li><strong>多曲线对比</strong>：不仅包含策略本身的净值曲线（蓝色），还绘制了基准指数（如沪深300，橙色）的对比曲线，策略是否跑赢市场一目了然。</li>
<li><strong>一体化子图</strong>：下方附带了时间轴完全对齐的多个子图，包括描绘风险深度的“回撤图”（红色区域）、展示每日盈亏的“日收益图”（红绿柱）以及标记具体操作的“买卖点图”（红蓝点）。</li>
<li><strong>交互式数据探查</strong>：鼠标在图表区域悬停，即可激活一条跟随光标的垂直标尺，并弹出一个信息框。该信息框会即时显示所指日期的详细数据，包括策略/基准收益、回撤、当日盈亏、成交量等，极大地提升了复盘效率。</li>
<li><strong>清晰的视觉标记</strong>：图表中的涨跌、盈亏、买卖点和最大回撤点都采用了不同的颜色进行标记，便于快速识别。</li>
</ul>
<p>这种将收益、风险、盈亏和交易行为整合在同一视图下的设计，有助于用户更全面、立体地理解策略的动态表现，而无需在不同表格和图表间来回切换。</p>
<hr>
<h2 id="92-%E4%BA%A4%E6%98%93%E8%AE%B0%E5%BD%95">9.2 交易记录</h2>
<p>窗口下方的“交易记录”标签页，提供了一份详尽的<strong>交易流水账</strong>。它以表格形式清晰记录了策略在回测期间完成的每一笔买入（红色）和卖出（蓝色）操作。内容包括成交时间、股票代码、方向、价格、数量、成交金额及手续费等。这份记录是分析策略具体行为、验证交易逻辑是否按预期执行的重要依据。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/交易记录.png" />
</p>
<hr>
<h2 id="93-%E6%97%A5%E6%94%B6%E7%9B%8A%E8%A1%A8">9.3 日收益表</h2>
<p>“日收益”标签页提供的是账户的<strong>每日快照</strong>。它以表格形式逐日记录收盘后的总资产、持仓市值、剩余现金及当日收益率。其中，日收益率根据盈亏（红/绿）进行颜色区分，便于快速浏览每日的盈亏波动情况。这个表格从数据层面补充了资金曲线的宏观视角，让用户能更细致地观察策略的每日资金变化。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/每日收益.png" />
</p>
<hr>
<h2 id="94-%E7%BB%A9%E6%95%88%E5%88%86%E6%9E%90">9.4 绩效分析</h2>
<p>“绩效分析”标签页提供了两张高级统计图表，旨在帮助用户<strong>深挖策略的内在“性格”和风险特征</strong>。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/绩效分析.png" />
</p>
<ul>
<li><strong>收益分布图</strong>：这张直方图统计了策略所有交易周期的收益率分布情况。通过观察图形的形态（如是否接近正态分布、是否存在“长尾”），可以判断策略收益的稳定性，并评估其发生极端盈利或亏损的隐藏风险。</li>
<li><strong>月度收益热力图</strong>：这张图表形如日历，用颜色的冷暖与深浅（绿赚红亏）直观展示策略在不同年份和月份的收益表现。通过它可以快速发现策略是否存在周期性或市场依赖性（例如，是否只在牛市有效，或在特定月份表现不佳）。</li>
</ul>
<p>这两张图表让用户能从统计学和周期的角度审视策略，发现一些仅从资金曲线上无法察觉的深层信息，从而对策略的风险收益特征有更全面的认知。</p>
<p>至此，您已经掌握了分析回测报告的关键方法。通过这份详尽的“成绩单”，您可以不断迭代、优化您的策略，在量化的道路上走得更远。</p>

</body>
</html>
