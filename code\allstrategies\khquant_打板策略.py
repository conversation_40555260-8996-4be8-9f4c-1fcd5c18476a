# coding: utf-8
from typing import Dict, List
import numpy as np
from xtquant import xtdata
import datetime

# 全局变量
position = {}
params = {}
risk_params = {}
trade_time_ranges = []  # 添加交易时间范围列表
g_today_bought = set()     # 今日买入的股票
g_yesterday_bought = set() # 昨日买入的股票，今日开盘卖出
g_trigger_prices = {}      # 储存触发价格
g_is_first_bar_of_day = True  # 每日第一个bar的标志

def init():
    """策略初始化"""
    global position, params, risk_params, trade_time_ranges
    
    # 初始化持仓记录
    position = {}
    
    # 初始化策略参数
    params = {
        "benchmark": "000905.SH",  # 中证500指数作为基准
        "max_positions": 5,        # 最大持仓数量
        "trigger_percent": 0.09,   # 触发买入的涨幅阈值（9%）
        "sample_size": 100         # 当股票池过大时的随机抽样数量
    }
    
    # 初始化风控参数
    risk_params = {
        "max_single_position_ratio": 0.2,  # 单个股票最大持仓比例（总资产的20%）
        "min_order_value": 10000          # 最小下单金额
    }
    
    # 初始化交易时间范围
    trade_time_ranges = [
        {"start": "09:30:00", "end": "11:30:00"},
        {"start": "13:00:00", "end": "14:57:00"}  # 稍早收盘，避免尾盘风险
    ]
    
    # 获取中证500成分股
    try:
        stocks = xtdata.get_stock_list_in_sector("000905.SH")
        print(f"获取中证500成分股 {len(stocks)} 只")
    except Exception as e:
        print(f"获取中证500成分股失败: {str(e)}, 使用默认股票池")
        # 使用一些大盘股作为默认池
        stocks = ["600519.SH", "000858.SZ", "601318.SH", "600036.SH", "000333.SZ",
                 "600276.SH", "601888.SH", "601398.SH", "000651.SZ", "600030.SH"]
    
    # 订阅股票行情
    for stock in stocks:
        xtdata.subscribe_quote(stock, period="1m")
    
    print(f"策略初始化完成，监控 {len(stocks)} 只股票")
    
    return stocks

def khPreMarket(data: Dict) -> List[Dict]:
    """盘前回调函数：计算每只股票的触发价格，准备当天交易"""
    global g_today_bought, g_yesterday_bought, g_trigger_prices, g_is_first_bar_of_day
    signals = []
    
    # 获取当前日期信息
    current_time = data.get("__current_time__", {})
    current_date_str = current_time.get("date", "")
    
    print(f"=== 交易日 {current_date_str} 盘前运行 ===")
    
    # 清空今日购买记录
    g_today_bought = set()
    
    # 重置第一个bar标志
    g_is_first_bar_of_day = True
    
    # 清空并重新计算触发价格
    g_trigger_prices = {}
    
    # 获取所有订阅的股票
    stocks = xtdata.get_subscribtion()
    total_stocks = len(stocks)
    print(f"开始计算 {total_stocks} 只股票的触发价格...")
    
    # 批量获取前一交易日的收盘价和今日开盘价
    for stock in stocks:
        try:
            # 获取今日开盘价
            bars = xtdata.get_market_data(field_list=["open"], 
                                         stock_list=[stock], 
                                         period="1d", 
                                         count=1)
            
            if bars and stock in bars and len(bars[stock]) > 0:
                open_price = bars[stock][0]["open"]
                
                # 计算触发价格（涨幅9%）
                if open_price > 0:
                    trigger_price = open_price * (1 + params["trigger_percent"])
                    g_trigger_prices[stock] = trigger_price
        except Exception as e:
            print(f"计算 {stock} 触发价格时出错: {str(e)}")
    
    print(f"已计算 {len(g_trigger_prices)} 只股票的9%涨幅触发价格")
    print("盘前处理结束")
    
    return signals

def khPostMarket(data: Dict) -> List[Dict]:
    """盘后回调函数：记录今日买入的股票，用于明天卖出"""
    global g_yesterday_bought, g_today_bought
    signals = []
    
    # 获取当前日期信息
    current_time = data.get("__current_time__", {})
    current_date_str = current_time.get("date", "")
    
    print(f"=== 交易日 {current_date_str} 盘后运行 ===")
    
    # 更新昨日买入记录，用于明天开盘卖出
    g_yesterday_bought = g_today_bought.copy()
    
    if g_today_bought:
        print(f"今日买入股票: {list(g_today_bought)}")
        print(f"明日开盘将卖出股票: {list(g_yesterday_bought)}")
    else:
        print("今日未买入股票")
    
    # 获取当前账户信息
    account_info = xtdata.get_account_info()
    if account_info:
        cash = account_info.get("cash", 0)
        total_value = account_info.get("total_value", 0)
        print(f"当前资金: {cash:.2f}, 总资产: {total_value:.2f}")
    
    print("====================")
    print("一天结束")
    
    return signals

def khHandlebar(data: Dict) -> List[Dict]:
    """策略主逻辑：在开盘时卖出昨日股票，检测股票涨幅超过9%则买入"""
    global g_today_bought, g_yesterday_bought, g_trigger_prices, g_is_first_bar_of_day
    signals = []
    
    # 获取当前时间信息
    current_time = data.get("__current_time__", {})
    current_time_str = current_time.get("time", "")
    current_date_str = current_time.get("date", "")
    current_datetime_str = current_time.get("datetime", "")
    
    print(f"-----------------------------------------------------")
    print(f"执行 khHandlebar @ {current_datetime_str}")
    
    # 检查是否是交易时间
    is_trading_time = False
    for time_range in trade_time_ranges:
        if time_range["start"] <= current_time_str <= time_range["end"]:
            is_trading_time = True
            break
    
    if not is_trading_time:
        print(f"当前时间 {current_time_str} 不在交易时间内，跳过")
        return signals
    
    # 如果是每天的第一个bar，先卖出昨日买入的股票
    if g_is_first_bar_of_day and current_time_str >= "09:30:00":
        print("执行每日第一个bar的操作，卖出昨日买入的股票")
        
        # 获取当前持仓
        account_positions = xtdata.get_positions()
        
        # 卖出昨日买入的股票
        for stock in g_yesterday_bought:
            if account_positions and stock in account_positions:
                position_info = account_positions[stock]
                volume = position_info.get("volume", 0)
                
                if volume > 0:
                    # 获取当前价格
                    quote = data.get(stock, {})
                    price = quote.get("close", 0) or quote.get("last", 0)
                    
                    if price > 0:
                        print(f"卖出昨日买入的股票: {stock}, 数量: {volume}, 价格: {price}")
                        
                        # 生成卖出信号
                        signals.append({
                            "code": stock,
                            "action": "sell",
                            "price": price,
                            "volume": volume,
                            "reason": "卖出昨日买入的股票",
                            "order_type": "limit",
                            "position_type": "long"
                        })
        
        g_is_first_bar_of_day = False
    
    # 检查当前持仓数量
    account_positions = xtdata.get_positions()
    current_positions = len(account_positions) if account_positions else 0
    
    if current_positions >= params["max_positions"]:
        print(f"当前持仓数量 ({current_positions}) 已达或超过最大持仓限制 ({params['max_positions']}), 不执行买入操作")
        return signals
    
    # 计算可用于买入的资金
    account_info = xtdata.get_account_info()
    if not account_info:
        print("无法获取账户信息，跳过买入操作")
        return signals
    
    available_cash = account_info.get("cash", 0)
    position_value = available_cash / params["max_positions"]
    print(f"当前可用资金: {available_cash:.2f}, 单股可用资金: {position_value:.2f}")
    
    # 当天不重复买入同一只股票
    account_positions = xtdata.get_positions() or {}
    stocks_to_check = [stock for stock in g_trigger_prices.keys() 
                     if stock not in account_positions
                     and stock not in g_today_bought]
    
    print(f"今日待检查的股票: {len(stocks_to_check)} 只")
    
    # 如果待检查股票太多，随机抽取一部分
    if len(stocks_to_check) > params["sample_size"]:
        import random
        sampled_stocks = random.sample(stocks_to_check, params["sample_size"])
        print(f"股票数量过多，随机抽取{params['sample_size']}只进行检查")
    else:
        sampled_stocks = stocks_to_check
    
    # 记录符合条件的股票
    potential_stocks = []
    
    # 检查每只股票
    for stock in sampled_stocks:
        if stock not in data:
            continue
            
        try:
            # 获取股票当前价格
            quote = data[stock]
            current_price = quote.get("close", 0) or quote.get("last", 0)
            
            if current_price <= 0:
                continue
                
            # 获取股票名称
            stock_info = xtdata.get_stock_info(stock)
            stock_name = stock_info.get("name", stock) if stock_info else stock
            
            # 获取触发价格
            trigger_price = g_trigger_prices.get(stock, 0)
            
            if trigger_price > 0:
                # 输出比较信息
                print(f"检查股票 {stock}({stock_name}): 当前价 {current_price:.2f} vs 触发价 {trigger_price:.2f}, 结果: {'符合条件' if current_price >= trigger_price else '不符合条件'}")
                
                # 如果当前价格超过触发价格，记录下来
                if current_price >= trigger_price:
                    potential_stocks.append((stock, stock_name, current_price, trigger_price))
        except Exception as e:
            print(f"处理股票 {stock} 时出错: {str(e)}")
    
    # 买入股票
    stocks_bought = 0
    for stock, stock_name, current_price, trigger_price in potential_stocks:
        # 计算可买股数（整数手）
        shares_to_buy = int(position_value / current_price / 100) * 100
        
        print(f"准备买入 {stock}({stock_name}): 单股资金 {position_value:.2f}, 当前价 {current_price:.2f}, 可买股数 {shares_to_buy}")
        
        if shares_to_buy >= 100:  # 至少买一手
            print(f"执行买入: {stock}({stock_name}), 价格: {current_price:.2f} > 触发价: {trigger_price:.2f}, 数量: {shares_to_buy}股")
            
            # 生成买入信号
            signals.append({
                "code": stock,
                "action": "buy",
                "price": current_price,
                "volume": shares_to_buy,
                "reason": "价格突破9%涨幅",
                "order_type": "limit",
                "position_type": "long"
            })
            
            # 记录买入
            g_today_bought.add(stock)
            stocks_bought += 1
            
            # 如果已达到最大持仓数，直接退出
            if stocks_bought + current_positions >= params["max_positions"]:
                print(f"已达到最大持仓数({stocks_bought + current_positions}/{params['max_positions']})，停止买入")
                break
        else:
            print(f"资金不足买入一手 {stock}({stock_name}), 当前价: {current_price:.2f}, 需要资金: {current_price*100:.2f}")
    
    print(f"khHandlebar执行完毕，本次生成 {len(signals)} 个交易信号")
    print(f"-----------------------------------------------------")
    
    return signals 