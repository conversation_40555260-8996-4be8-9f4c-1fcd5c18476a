在编写手册的过程中，需要以我是这个软件编写者的口吻来写，但是行文不要有过多的"我"字，写的内容要兼具专业性和通俗易通，不要太啰嗦，要有干货，要保证内容的正确性。要有一定的风趣幽默，不要使用不合时宜的比喻来实现这种幽默，也不要幽默的太尴尬，要自然且高级的幽默。

# 《看海量化交易系统使用手册》大纲

大家好，我是Mr.看海。这份手册旨在帮助大家快速上手并充分利用"看海量化交易系统"（KHQuant）。下面是本手册的主要章节和内容概览。

## 第一章：写在前面 (已完成)

1.1 关于我：Mr.看海
1.2 我为什么要做这样一款量化交易系统？
1.3 看海量化系统：特点与比较 (表格形式)
1.4 使用"看海量化交易平台"的背景知识清单
1.5 "看海量化交易系统"适合做什么？
1.6 "看海量化交易系统"不适合做什么？

## 第二章：权责说明

2.1 与MiniQMT的关系
    - 本系统的数据获取和交易执行均通过国金证券的MiniQMT客户端实现，KHQuant本身不直接处理行情和交易指令。
    - 对于因MiniQMT客户端本身产生的任何错误、故障、延迟或数据偏差，以及由此可能导致的任何损失，本系统的作者不承担任何责任。

2.2 开源与维护责任
    - "看海量化交易系统"是一款免费且开源的软件，旨在为量化爱好者提供便利。
    - 作者会尽力维护系统的稳定性并进行功能更新，但无法保证能即时响应每一位用户的特定需求。
    - Bug修复会根据其紧要程度和影响范围进行排序并尽快处理。对于新功能的开发请求，只能逐步规划与完善。
    - 对于有急切或特殊功能需求的用户，建议利用本系统的开源代码自行修改和实现。
    - 由于精力所限，作者不对开源代码提供任何个人的、详细的讲解或教学。

2.3 开源许可协议
    - 本系统的源代码遵循 **CC BY-NC 4.0** 许可协议。正文核心条款如下：
      > **CC BY-NC 4.0 许可**
      >
      > 你可以自由地：
      >
      > **分享** - 复制并以任何格式重新分发材料
      >
      > **改编** - 混合、转换并从材料中构建
      >
      > 只要你遵守许可条款，许可方不能撤销这些自由。
      >
      > **在以下条款下：**
      >
      > **署名** - 你必须适当注明出处，提供许可链接，并说明是否进行了任何更改。你可以以任何合理的方式这样做，但不得以暗示你或你的使用得到许可方认可的方式。
      >
      > **非商业性** - 你不得将材料用于商业目的。
      >
      > **没有附加限制** - 你不得适用法律术语或技术措施，在法律上限制他人进行许可允许的任何使用。
    - **严正声明**：任何个人或实体均可以使用本系统代码进行自用和修改，但**严禁用于任何形式的商业目的**。如私自将本系统或其衍生代码用于商业用途，所产生的一切法律后果和经济风险由使用者自行承担，同时作者保留追究其侵权责任的权利。

2.4 内部交流群说明
    - 通过作者提供的推荐渠道开通国金证券MiniQMT账户的用户，可以联系作者加入内部交流群。
    - 内部群成员可以享受以下权益：
        - 优先体验软件的内测版本。
        - 在软件正式发布后，可以比公开发布渠道更早地获得新版本和新功能。
        - 在使用过程中遇到的问题，能得到更优先的响应和支持。
        - 群内会不定期分享一些策略思路或内部策略代码。

## 第三章：准备工作：安装与初见

3.1 我的系统需要什么环境？
- 操作系统：Windows 是我的主要测试和使用环境。
- Python环境：如果您想从源码运行，需要Python 3.x 及一些常用库 (PyQt5, xtquant, psutil, Matplotlib等)。
- **关键：MiniQMT客户端**：您必须先安装国金证券的QMT系统，这是本系统进行行情获取和交易的核心依赖。
3.2 如何安装本系统？
- (如果提供安装包) 安装包的获取方式与一步步的安装指引。
- (如果从源码运行) 如何配置Python环境和安装必要的依赖库。
3.3 第一次启动：关键设置要点
- 启动软件后，第一件事：在"设置"中正确配置您的MiniQMT `userdata_mini` 路径。
- 观察主界面工具栏右上角的MiniQMT状态指示灯，绿色代表一切就绪。

## 第四章：快速上手：运行您的第一个回测

4.1 加载示例工程与`.kh`文件说明
- 指导用户通过"加载配置"按钮加载一个内置的示例工程文件 (`.kh`结尾)。
- 解释`.kh`文件是保存所有UI参数的工程配置文件。
- 强调`.kh`是JSON格式的文本文件，可用任何文本编辑器打开查看和修改。
4.2 开始回测与观察
- 点击"开始运行"按钮。
- 关注右侧"系统日志"输出和底部回测进度条。
4.3 解读您的第一个回测报告
- 回测完成后，点击"打开回测指标"按钮。
- 对报告中的关键区域（指标、图表、交易记录）进行初步介绍。

## 第五章：主界面巡览：功能区详解

5.1 整体布局一览
- 左侧：核心参数配置区。
- 中间：触发机制与账户信息区。
- 右侧：系统日志与运行监控区。
- 顶部：常用操作工具栏。
- 底部：实时状态栏。
5.2 工具栏按钮说明
- **配置管理**：加载、保存、另存为 `.kh` 格式的配置文件 (它是JSON格式，保存了您的所有界面设置)。
- **运行控制**：开始、停止策略。
- **数据模块**：启动独立的数据可视化分析工具。
- **设置**：打开软件的全局参数设置。
- **MiniQMT指示灯**：绿色表示已连接，红色表示未连接或异常。
- **帮助按钮 (?)**：快速访问在线教程。
5.3 状态栏信息解读
- 显示当前软件的操作状态。
- 回测模式下，会显示详细的进度百分比。

## 第六章：核心配置项：左侧面板精解

6.1 "策略配置"组
- **策略文件**：选择您的Python策略脚本 (`.py`结尾)。
- **运行模式**：
- **回测**：用历史数据评估策略表现。
- **模拟**：连接MiniQMT模拟账户，用实时行情进行模拟交易。
- **实盘**：连接MiniQMT实盘账户，进行真实交易 (请务必谨慎)。
6.2 "回测参数"组
- **基准合约**：用于计算Alpha、Beta等相对指标，例如 `sh.000300`。
- **交易成本设置**：
- 最低佣金、佣金比例、印花税率、流量费。
- 滑点设置：可选"按最小变动价跳数"或"按成交金额比例"，并设定相应数值。
- **回测时间设置**：设定回测的开始和结束日期。
- **数据设置**：
- 复权方式：如不复权、前复权、等比前复权等。
- 周期类型：如tick、1m、5m、1d。
- 数据字段：根据周期勾选您策略需要的数据字段。
- **股票池设置**：
- 常用指数成分股：如上证50、沪深300，一键勾选。
- 自选清单：编辑 `data/otheridx.csv` 文件，然后勾选使用。
- 手动管理列表：直接在表格中添加/删除股票，或导入 `.csv`/`.txt` 文件。

## 第七章：运行驱动力：中间面板详解

7.1 "触发方式设置"组
- **触发类型**：
- Tick触发：每个Tick数据到达时驱动策略。
- K线触发：1分钟或5分钟K线形成时驱动策略。
- 自定义定时触发：按您设定的特定时间点列表驱动策略。
- **自定义定时触发配置** (仅当选择此类型时)：
- 时间点列表：手动输入 `HH:MM:SS` 格式的时间点。
- 时间点生成器：辅助您快速生成均匀分布、整点分布或按自定义间隔的时间点序列。
7.2 "实盘数据获取"组 (仅"模拟"或"实盘"模式下可见)
- 订阅全推行情：订阅交易所所有合约的实时行情 (对网络和权限有要求)。
- 单股订阅：仅订阅策略中关注的合约行情。
- *特别注意*：若选择"自定义定时触发"，本系统默认您会在策略代码中自行处理数据订阅逻辑。
7.3 "账户信息"组
- **虚拟账户** (回测/模拟模式)：设置初始资金、最小交易单位。
- **真实账户** (实盘模式，连接QMT后)：显示账户资产、资金、市值及详细持仓。
7.4 "盘前盘后触发设置"组
- 勾选并设置时间，可以在每日开盘前或收盘后，自动执行策略中相应的 `khPreMarket` 或 `khPostMarket` 函数。

## 第八章：洞察运行：右侧日志面板

8.1 "系统日志"区
- 这里会显示软件的运行状态、策略打印的日志、交易委托和成交的详细回报、以及任何错误或警告信息。
- 不同级别的日志会用不同颜色标记，方便识别。
8.2 日志操作功能
- **日志类型过滤**：按级别筛选您想看的日志。
- **清空日志**：清除当前显示。
- **保存日志**：将日志导出到文本文件。
- **测试日志**：生成一些测试日志，检查显示是否正常。
- **打开回测指标**：回测结束后，此按钮会激活，点击查看详细报告。

## 第九章：策略绩效复盘：回测结果解读

9.1 如何查看回测报告？
- 在主界面右侧日志面板，当回测完成后，点击"打开回测指标"按钮。
9.2 报告窗口结构
- 顶部：回测的基本信息概览。
- 左侧：各项关键绩效指标的数值展示。
- 右侧：核心图表，如资金曲线、回撤曲线等。
- 底部：详细的交易记录和每日账户状态表格。
9.3 核心指标释义
- 总收益率、年化收益率。
- 基准收益率、Alpha、Beta。
- 夏普比率、索提诺比率。
- 最大回撤。
- 年化波动率。
- 胜率、盈亏比、日均交易次数等。
9.4 图表分析技巧
- **资金曲线图**：直观对比策略净值与基准指数的走势。
- **回撤曲线图**：观察策略净值从前期高点回落的幅度。
- **盈亏分析图**：展示每日的盈利或亏损金额。
- **成交记录图**：以柱状图形式展示每日的买入和卖出量。
- **收益分布图** (在"绩效分析"标签页)：了解策略收益的统计分布特征。
- **月度收益热力图** (在"绩效分析"标签页)：快速识别策略在不同月份的表现模式。
- **每日收益**：回测期内每一天的账户资产、市值、资金和当日收益率。

## 第十章：辅助工具：数据模块简介

10.1 如何启动数据模块？
- 在主界面顶部工具栏点击"数据模块"按钮。它会打开一个新的窗口。
10.2 主要功能介绍 (基于 `GUIplotLoadData.py`)
- **本地数据可视化**：
- 选择包含 `.csv` 行情文件的本地文件夹。
- 系统会自动分析文件，并在下拉框中列出可供选择的股票。
- 对于分钟级或Tick数据，可以选择特定日期进行查看。
- **图表展示**：
- 绘制所选股票和时间段的K线图或分时图。
- 可能包含成交量等副图指标。
- **交互功能**：
- 鼠标悬停显示详细数据。
- 通过拖拽进行缩放或平移。

## 第十一章：个性化配置：软件设置详解

11.1 打开"设置"对话框
- 主界面顶部工具栏 -> "设置"。
11.2 "基本参数设置"
- **无风险收益率**：用于计算夏普比率等指标，默认为0.03 (即3%)。
- **延迟显示日志**：勾选后，策略运行期间的日志会先缓存，结束后统一显示，可提升界面流畅度。
- **初始化行情数据**：是否在启动时自动连接行情。
- **账户设置**：配置您的QMT资金账号和账户类型。
11.3 "股票列表管理"
- **更新成分股列表**：点击可从网络更新本地的指数成分股等列表文件 (存放于 `data` 目录)。请耐心等待，无需频繁操作。
11.4 "客户端设置"
- **miniQMT客户端路径**：指向您的MiniQMT安装目录下的 `XtMiniQmt.exe` 文件。
- **QMT路径**：指向MiniQMT的 `userdata_mini` 文件夹。
11.5 "版本信息"
- 显示当前软件版本、构建日期和更新通道。
11.6 "反馈问题"按钮
- 点击会跳转到我的在线反馈页面 (https://khsci.com/khQuant/feedback)。

## 第十二章：核心驱动：策略编写指南

12.1 策略文件的基本样貌
- 您的策略应该是一个标准的Python (`.py`) 文件。
- 框架会通过文件名动态加载您的策略模块。
12.2 我设计的几个关键回调函数
- `init(context)`: 策略启动时执行一次，用于初始化参数、订阅行情等全局设置。`stock_codes` 和 `init_data` 会作为参数传入。
- `khHandlebar(data)`: 这是策略的核心处理函数，根据您配置的"触发方式"，在每个Tick或K线周期到达时被调用。`data` 参数是包含当前市场行情和账户信息的字典。
- `khPreMarket(data)`: (可选) 如果您在中间面板启用了"盘前回调"，此函数会在每日开盘前（按您设置的时间）被调用。
- `khPostMarket(data)`: (可选) 如果您在中间面板启用了"盘后回调"，此函数会在每日收盘后（按您设置的时间）被调用。
12.3 `data` 字典：策略的"眼睛"和"耳朵"
- `data['__current_time__']`: 一个包含当前时间戳、日期时间字符串等信息的字典。
- `data['__account__']`: 当前账户的资产信息字典 (结构类似 `trade_mgr.assets`)。
- `data['__positions__']`: 当前持仓信息字典 (结构类似 `trade_mgr.positions`)。
- `data['__framework__']`: `KhQuantFramework` 实例自身的引用，您可以用来调用框架提供的工具函数，例如 `data['__framework__'].tools.is_trade_day(date_str)`。
- `data[stock_code]`: 对于已订阅的股票，这里会包含其当前的行情数据 (例如 `pd.Series` 或字典，具体结构取决于数据周期和获取方式)。
12.4 如何在策略中获取数据？
- **订阅行情时**：`khHandlebar` 中的 `data` 参数会传入最新的行情。
- **历史数据**：您可以使用 `xtquant.xtdata` 模块的函数，如 `xtdata.get_market_data_ex(...)` 或 `xtdata.get_local_data(...)` 来获取特定周期的历史K线或Tick数据。框架在初始化时也会根据您的配置下载部分数据。
12.5 交易指令的发送
- 框架通过 `KhTradeManager` (在 `khTrade.py` 中定义) 来处理交易信号。
- 在 `khHandlebar` 函数中，您需要构建一个包含交易指令的列表并返回。每个指令是一个字典，例如：
```python signals = [] if some_condition: signals.append({ 'code': 'sh.600036',      # 股票代码 'action': 'buy',           # 'buy' 或 'sell' 'price_type': 'limit',     # 'limit'(限价) 或 'market'(市价) 'price': 10.5,             # 目标价格 (市价单可忽略或设为0) 'volume': 100,             # 目标数量 'order_remark': 'MA_cross_buy' # 订单备注 (可选) }) return signals ```
- 框架会自动处理这些信号，并调用QMT接口执行交易。
12.6 与QMT的直接交互 (高级)
- 虽然框架封装了大部分交易逻辑，但如果您有特殊需求，也可以在策略中通过 `data['__framework__'].trader` 直接访问 `XtQuantTrader` 实例，调用其原生API。但请注意，这需要您对xtquant的API有深入了解。
12.7 一个简单的策略示例
- (此处可以放置一个完整的、能在本系统中运行的简单均线策略或布林带策略代码作为演示)

## 第十三章：答疑解惑 (FAQ)

- Q: MiniQMT连接不上，状态灯是红的，怎么办？
  - A: 首先确认您的MiniQMT客户端已经正常启动并登录。然后检查本软件"设置"里的"QMT路径"是否正确指向了QMT的 `userdata_mini` 文件夹。最后，查看右侧"系统日志"面板，通常会有更详细的错误提示。
- Q: 回测跑起来特别慢，怎么回事？
  - A: 可以尝试以下几点：1. 缩小股票池范围和回测时间段；2. 优化策略代码，避免在 `khHandlebar` 中进行过于复杂的计算；3. 在"设置"里勾选"延迟显示日志"；4. 确保您的CPU和内存资源充足。
- Q: 我该如何编写自己的交易策略？
  - A: 请仔细阅读本手册的第十二章"核心驱动：策略编写指南"，里面有详细的说明和示例。
- Q: 配置文件 (`.kh` 文件) 是做什么用的？
  - A: 它是一个JSON格式的文本文件，用来保存您在图形界面上做的所有参数配置。您可以"保存配置"，下次通过"加载配置"就能快速恢复之前的设置，非常方便。
- Q: 用这个系统做实盘交易，资金安全吗？
  - A: 本系统本身不直接处理您的资金。所有的交易指令都是通过国金证券的MiniQMT客户端执行的，您的实际资金安全由券商和QMT系统保障。请务必确保您的QMT账户安全，不要泄露密码。本系统仅作为策略研究和辅助交易的工具。

## 附录

A. 关键术语解释
- Tick数据、K线数据、复权、滑点、夏普比率、最大回撤、Alpha、Beta等。
B. (可选) `KhQuantFramework` 常用工具函数参考
- 例如 `tools.is_trade_day()` 等。
----------------------------------

希望这份大纲能帮助您更好地组织手册内容！
