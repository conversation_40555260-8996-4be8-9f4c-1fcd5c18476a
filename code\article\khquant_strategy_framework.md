> 我是Mr.看海，我在尝试用信号处理的知识积累和思考方式做量化交易，应用深度学习和AI实现股票自动交易，目的是实现财务自由\~
> 目前我正在开发基于miniQMT的量化交易系统——看海量化交易系统。

量化交易策略的成功，关键在于其核心逻辑的有效性。然而，从一个交易思想到一个能在市场中稳定运行的自动化程序，中间往往隔着数据处理、接口对接、订单管理、异常处理等一系列繁杂的工程细节。这不仅耗费开发者大量精力，也容易引入错误，从而**模糊了策略研究与工程实现的界限**。

KhQuant 量化交易系统深刻理解策略开发者的这一痛点，其核心目标之一便是**提供一个高度封装、标准化的策略框架**。这个框架旨在将策略逻辑与底层执行细节解耦，让开发者可以将 90% 以上的精力投入到策略本身的研发与优化上，而不是迷失在重复性的"轮子制造"中。

此外，这种标准化的策略框架接口，也为利用大型语言模型（如 DeepSeek）快速生成符合规范、能够直接接入系统进行回测的策略代码提供了极大的便利。

本文将带你深入 KhQuant 策略框架的内部，详细剖析其核心事件处理函数、数据传递机制、标准化输出以及配套的辅助工具，助你全面掌握框架精髓，告别繁琐，高效构建你的专属交易策略。

## 一、 框架的基石：标准化的事件处理函数

KhQuant 框架采用**事件驱动**模式。这意味着策略代码并不是一个从头跑到尾的脚本，而是一个包含了特定"回调函数"的 Python 模块。框架在运行时，会监听各种市场事件（如新的行情数据到达、交易时段开始/结束等），并在特定事件发生时，调用你策略模块中对应的函数。你所要做的，就是在这些预定义的函数"坑"里，填入你的策略代码。

这种设计极大地简化了策略结构。你不再需要自己编写复杂的事件循环、数据订阅和时间管理逻辑。

KhQuant 的策略本质上是一个 Python 文件，其中主要的预定义函数有四个：

### **1.1 init(stocks, data)：万事开头 - 策略初始化 (启动时执行一次）**

**调用时机:** 在整个策略生命周期中，仅在**点击"启动策略"按钮，框架加载策略文件后，实际运行策略逻辑之前**被调用一次。

**核心职责:** 执行所有只需要进行一次的设置和准备工作。这包括：

* **设置策略参数:** 定义如移动平均线周期、交易阈值、止损比例等策略运行所需的固定参数。这些参数可以直接硬编码，或更灵活地设计成从外部文件读取（尽管 KhQuant 的 GUI 配置是更推荐的方式）。
* **初始化状态变量:** 如果策略需要在不同时间点之间传递状态（例如，跟踪上一次开仓的价格、记录连续亏损次数等），需要在这里初始化用于存储这些状态的变量（通常是全局字典或类的成员变量）。
* **加载一次性数据:** 如果策略需要某些启动时就确定的、不随时间变化的基础数据（例如，某些股票的基本面信息），可以在这里加载。注意，对于历史行情数据，框架通常会自动处理加载和缓存，策略层面一般无需在此处手动下载。

**为何需要** **init?** 它将一次性的设置代码与需要反复执行的策略逻辑代码分离开来。这不仅让代码结构更清晰、易于维护，也避免了在每次行情更新时都重复执行这些不必要的设置，提高了运行效率。

**数据持久化:** init 函数本身不返回任何值。它设置的参数和状态变量需要通过 Python 的作用域规则（如使用 global 关键字声明全局变量，或将策略逻辑封装在类中使用 self 属性）来让后续的 khHandlebar 等函数能够访问和修改。

```python
def init(stocks=None, data=None):
    """策略初始化
  
    Args:
        stocks: 股票代码列表，例如['000001.SZ', '600000.SH']
        data: 包含时间、账户、持仓等信息的数据字典，结构如下：
            {
                "__current_time__": {
                    "timestamp": 时间戳(int),
                    "datetime": "YYYY-MM-DD HH:MM:SS"格式的日期时间(str),
                    "date": "YYYY-MM-DD"格式的日期(str),
                    "time": "HH:MM:SS"格式的时间(str)
                },
                "__account__": {
                    "cash": 可用资金(float),
                    "total_asset": 总资产(float),
                    "market_value": 持仓市值(float),
                    "frozen_cash": 冻结资金(float)
                },
                "__positions__": {
                    "股票代码1": {
                        "volume": 持仓数量(int),
                        "avg_price": 平均持仓成本(float),
                        "current_price": 当前价格(float),
                        "market_value": 持仓市值(float),
                        "profit": 持仓盈亏(float)
                    },
                    "股票代码2": {...}
                }
            }
    """
```

### **1.2 khHandlebar(data)：决策核心 - 响应市场变化 (事件触发，高频调用)**

**调用时机:** 这是策略**最核心、被调用最频繁**的函数。根据用户在 GUI 中选择的**触发模式**（Tick 触发、1 分钟 K 线触发、5 分钟 K 线触发或自定义时间点触发），每当接收到新的符合条件的行情数据时，框架就会调用 khHandlebar。对于 K 线触发，通常是在一根 K 线**走完形成**的那一刻触发。

**核心职责:** 接收当前的市场快照和账户状态，执行策略的核心计算和判断，并决定是否需要进行交易。典型的执行流程包括：

1. **数据提取:** 从传入的 data 字典中获取当前时间、所需股票的最新行情（收盘价、最高/最低价等）、账户的可用资金、当前的实际持仓情况等。
2. **指标计算:** 基于获取的行情数据（以及可能需要的历史数据，通过 xtdata 接口获取），计算技术指标（如 MA, MACD, RSI 等）或执行其他模型运算。
3. **条件判断:** 根据计算出的指标和当前的持仓状态，判断是否满足开仓、平仓或其他交易条件。
4. **信号生成:** 如果判断结果需要交易，则调用辅助函数（如 generate\_signal）或手动构建标准格式的交易信号字典。

**为何需要** **khHandlebar?** 它构成了策略对市场实时变化的响应机制。框架负责在正确的时间点，将所需的信息打包喂给这个函数，开发者只需专注于"输入->计算->判断->输出信号"这一核心流程。

**数据流:** 接收框架传入的 data 字典，返回一个包含零个或多个交易信号字典的列表（后边详解）。

```python
def khHandlebar(data: Dict) -> List[Dict]:
    """策略主逻辑，在每个K线或Tick数据到来时执行
  
    Args:
        data: 包含时间、账户、持仓和行情数据的字典，结构如下：
            {
                "__current_time__": {
                    "timestamp": 时间戳(int),
                    "datetime": "YYYY-MM-DD HH:MM:SS"格式的日期时间(str),
                    "date": "YYYY-MM-DD"格式的日期(str),
                    "time": "HH:MM:SS"格式的时间(str)
                },
                "__account__": {
                    "cash": 可用资金(float),
                    "total_asset": 总资产(float),
                    "market_value": 持仓市值(float),
                    "frozen_cash": 冻结资金(float)
                },
                "__positions__": {
                    "股票代码1": {
                        "volume": 持仓数量(int),
                        "avg_price": 平均持仓成本(float),
                        "current_price": 当前价格(float),
                        "market_value": 持仓市值(float),
                        "profit": 持仓盈亏(float)
                    },
                    "股票代码2": {...}
                },
                "股票代码1": {
                    "close": 收盘价/最新价(float),
                    "open": 开盘价(float),
                    "high": 最高价(float),
                    "low": 最低价(float),
                    "volume": 成交量(int/float),
                    "amount": 成交额(float),
                    "其他字段": 根据订阅的数据字段而定
                },
                "股票代码2": {...}
            }
  
    Returns:
        List[Dict]: 交易信号列表，每个信号的格式为：
            {
                "code": 股票代码(str),
                "action": 交易行为，"buy"或"sell"(str),
                "price": 交易价格(float),
                "volume": 交易数量(int),
                "reason": 交易原因(str),
                "timestamp": 时间戳(int，可选)
            }
    """
```

### **1.3 khPreMarket(data)** **/** **khPostMarket(data)：常规任务处理 (每日开盘前/收盘后执行，可选)**

**调用时机:** 分别在**每个 A 股交易日**（由框架判断，会跳过周末和法定节假日）的**开盘前**（具体时间可在 GUI 配置，如 08:30）和**收盘后**（具体时间可在 GUI 配置，如 15:30）被调用一次。

**核心职责:**

**khPreMarket** **(盘前):** 适合执行每日开始时的准备工作。例如：

* 获取当天需要关注的宏观信息或新闻。
* 重新加载可能每日更新的配置文件或模型参数。
* 基于隔夜数据或盘前分析，调整当日交易参数或风险限制。
* 如果策略需要在集合竞价或开盘时立即下单，可以在此生成交易信号。

**khPostMarket** **(盘后):** 适合执行每日结束后的总结和维护工作。例如：

* 统计当日交易表现，计算盈亏、胜率等指标。
* 进行当日的风险评估和持仓分析。
* 保存重要的运行时数据或交易记录到外部文件。
* 基于当日市场表现，更新机器学习模型或调整策略参数供次日使用。
* 生成交易总结报告并发送给用户。

**为何需要** **khPreMarket/khPostMarket?** 它们提供了一种机制，将那些不需要在交易时间内高频执行的、与交易日相关的例行任务，从核心的 khHandlebar 中剥离出来。这使得 khHandlebar 可以更专注于对实时行情做出快速反应，同时也让整个策略的运作流程更加结构化和自动化。

**数据流:** 输入的 data 字典主要包含调用时刻的时间、账户和持仓信息（注意，此时通常没有实时的个股行情数据）。输出同样是标准格式的交易信号列表（允许在盘前/盘后生成交易指令，例如计划在第二天开盘执行的指令）。

**可选性:** 如果你的策略非常简单，不需要进行任何盘前盘后的特殊处理，那么完全可以不在策略文件中定义这两个函数，框架会跳过调用。

```python
def khPreMarket(data: Dict) -> List[Dict]:
    """盘前回调函数，在每个交易日开盘前执行
  
    Args:
        data: 包含时间、账户、持仓等信息的数据字典，结构如下：
            {
                "__current_time__": {
                    "timestamp": 时间戳(int),
                    "datetime": "YYYY-MM-DD HH:MM:SS"格式的日期时间(str),
                    "date": "YYYY-MM-DD"格式的日期(str),
                    "time": "HH:MM:SS"格式的时间(str)
                },
                "__account__": {
                    "cash": 可用资金(float),
                    "total_asset": 总资产(float),
                    "market_value": 持仓市值(float),
                    "frozen_cash": 冻结资金(float)
                },
                "__positions__": {
                    "股票代码1": {
                        "volume": 持仓数量(int),
                        "avg_price": 平均持仓成本(float),
                        "current_price": 当前价格(float),
                        "market_value": 持仓市值(float),
                        "profit": 持仓盈亏(float)
                    },
                    "股票代码2": {...}
                }
            }
  
    """
    signals = []
    # 此处可以添加盘前处理逻辑
    return signals

def khPostMarket(data: Dict) -> List[Dict]:
    """盘后回调函数，在每个交易日收盘后执行
  
    Args:
        data: 包含时间、账户、持仓等信息的数据字典，结构如下：
            {
                "__current_time__": {
                    "timestamp": 时间戳(int),
                    "datetime": "YYYY-MM-DD HH:MM:SS"格式的日期时间(str),
                    "date": "YYYY-MM-DD"格式的日期(str),
                    "time": "HH:MM:SS"格式的时间(str)
                },
                "__account__": {
                    "cash": 可用资金(float),
                    "total_asset": 总资产(float),
                    "market_value": 持仓市值(float),
                    "frozen_cash": 冻结资金(float)
                },
                "__positions__": {
                    "股票代码1": {
                        "volume": 持仓数量(int),
                        "avg_price": 平均持仓成本(float),
                        "current_price": 当前价格(float),
                        "market_value": 持仓市值(float),
                        "profit": 持仓盈亏(float)
                    },
                    "股票代码2": {...}
                },
                "股票代码1": {
                    "close": 收盘价(float),
                    "其他字段": 收盘时的行情数据
                },
                "股票代码2": {...}
            }
  
    """
    signals = []
    # 此处可以添加盘后处理逻辑
    return signals
```

## 二、 信息高速公路：详解 data 字典

data 字典是 KhQuant 框架与策略之间信息传递的**核心枢纽**。它的设计目标是解决策略开发中数据来源分散、需要调用多个 API 获取状态的痛点，将**当前时间点**策略决策所需的关键信息**集中打包**提供。理解 data 字典的结构至关重要：

**\_\_current\_time\_\_** **(字典):** 提供精确的时间参考：

> timestamp (整数): 标准 Unix 时间戳（秒级或毫秒级，取决于框架实现精度），便于进行时间相关的计算和比较。
> datetime (字符串): 'YYYY-MM-DD HH:MM:SS' 格式，方便阅读和日志记录。
> date (字符串): 'YYYY-MM-DD' 格式，常用于获取日级别数据或按天处理逻辑。
> time (字符串): 'HH:MM:SS' 格式，用于判断具体交易时段。

**\_\_account\_\_** **(字典):** 反映当前的资金账户状态：

> cash (浮点数): **核心字段**，指当前**可用于交易的现金余额**。策略计算买入量时应主要基于此值。
> total\_asset (浮点数): 总资产，通常等于 cash + market\_value。反映账户整体规模。
> market\_value (浮点数): 所有持仓按当前市价计算的总市值。
> frozen\_cash (浮点数): 因挂单等原因被冻结的资金。cash 通常是总现金减去 frozen\_cash 后的结果。

**\_\_positions\_\_** **(字典):** 提供详细的**当前实际持仓**信息。键是股票代码 (如 '000001.SZ')，值是包含该股票持仓详情的字典：

> *对于每支持仓股票* *stock\_code* (字典):
> volume (整数): **当前总共持有**该股票的数量。
> can\_use\_volume (整数): **核心字段**，指当前**可以卖出**的数量。它考虑了 T+1 规则（当天买入的股票当天不能卖出）以及可能的冻结（例如已挂卖单但未成交的部分）。策略计算卖出量时应基于此值。
> avg\_price (浮点数): 持仓成本价。
> current\_price (浮点数): 框架会尽力在调用策略前更新该股票的最新市场价格，方便计算盈亏和市值。
> market\_value (浮点数): 该持仓的当前市值 (current\_price \* volume)。
> profit (浮点数): 该持仓的浮动盈亏。

**股票代码** **(字典):** （主要在 khHandlebar 中出现）包含**当前触发事件**的市场行情数据。键是具体的股票代码。

> close (浮点数): 对于 K 线触发，这是一般是 K 线收盘价；对于 Tick 触发，这是最新成交价。**这是策略中最常用的价格字段**。
> open, high, low (浮点数): K 线的开盘、最高、最低价。
> volume, amount (整数/浮点数): K 线或 Tick 的成交量和成交额。
> *(其他字段)*: 用户在 GUI "数据设置"中勾选订阅的其他行情字段（如 pre\_close, turnover\_rate 等）也会在这里提供。

这种设计的好处在于，策略开发者在 khHandlebar 中几乎可以通过简单直观的方式获取所需的核心信息，极大地降低了与底层交互的复杂度。

## 三、 指令的规范：标准交易信号格式

当策略判断需要执行交易时，它需要"告知"框架执行意图。为了让框架的交易管理器 (TradeManager) 能够准确无误地理解并执行，KhQuant 定义了**标准化的交易信号格式**。任何需要下单的函数 (khHandlebar, khPreMarket, khPostMarket) 都必须返回一个包含零个或多个这种标准信号字典的列表。

标准信号格式如下：

```python
{
    "code": str,         # 股票代码 (必须是 '代码.市场' 格式, e.g., "000001.SZ", "600000.SH")
    "action": str,       # 交易动作 (必须是 "buy" 或 "sell")
    "price": float,      # 下单价格 (框架会以此价格下限价单 Limit Order)
    "volume": int,       # 下单数量 (必须是整数, 且通常是 100 的倍数)
    "reason": str,       # 交易原因 (建议填写，用于日志记录和分析)
    "timestamp": int     # 时间戳 (可选, 框架若发现缺失会自动补充当前时间戳)
}
```

**字段详解:**

* code: 必须是精确的股票代码，包含交易所后缀（.SZ 或 .SH）。错误的代码会导致下单失败。
* action: 明确指定是买入 (buy) 还是卖出 (sell)。
* price: 这是你期望成交的价格。框架会提交一个**限价单 (Limit Order)** 到交易所。如果当前市场价格优于（买单时更低，卖单时更高）或等于你的限价，订单可能会立即成交；否则，订单会进入挂单簿等待对手方撮合。**注意：价格精度很重要**，不正确的价格（如偏离市价过多、不符合价格笼子规则）可能导致废单。generate\_signal 函数会自动处理价格精度（通常保留两位小数）。
* volume: **必须是整数**，且对于 A 股，通常必须是 **100 的整数倍**（一手等于 100 股），除非是卖出时处理不足一手的零股。数量错误是导致下单失败的常见原因。generate\_signal 函数会自动计算并确保数量是 100 的倍数。
* reason: 虽然不是强制字段，但强烈建议填写。它会被记录在交易日志中，极大地帮助后续进行策略调试、绩效分析和理解交易行为。一个好的 reason 应简洁说明触发该交易的条件。
* timestamp: 主要用于回测时精确对齐交易发生的时间点。实盘中如果省略，框架会使用当前系统时间戳。

遵循这个标准信号格式，可以确保策略的交易意图被框架准确理解和执行，有效解决了策略开发者**"如何将策略判断转化为实际下单指令"**以及**"下单参数容易出错"**的痛点。

## 四、 框架设计的核心优势

总结来说，KhQuant 策略框架的设计旨在提供以下核心优势，解决策略开发中的常见难题：

1. **高度聚焦策略逻辑:** 通过将底层细节（数据连接、API 调用、事件处理、订单执行）封装在框架内部，开发者可以将几乎全部精力投入到交易策略本身的设计、实现和优化上，**告别繁琐的工程事务**。
2. **清晰的事件驱动模型:** 框架自动处理时间的流逝和市场数据的更新，在恰当的时机调用对应的策略函数 (init, khHandlebar 等)，使得策略代码能够自然地对市场事件做出反应，**无需开发者手动管理复杂的事件循环**。
3. **标准化的开发范式:** 预定义的函数接口 (init, khHandlebar...)、统一的数据输入 (data 字典) 和标准的信号输出格式，**降低了学习曲线**，使得不同开发者编写的策略具有相似的结构，易于理解、维护和交流。
4. **集中的信息获取:** data 字典将决策所需的关键信息（时间、行情、账户、持仓）集中提供，**避免了在策略代码中分散调用各种查询接口**，简化了策略逻辑。
5. **灵活性与可扩展性:** 虽然核心接口是标准化的，但策略文件本身是纯粹的 Python 模块。开发者可以在其中自由导入其他库、定义自己的辅助函数、创建类来组织复杂逻辑，**框架不限制策略内部的实现方式**。
6. **回测与实盘的一致性:** 设计良好的策略代码，理论上可以**无需修改或只需少量修改**就能同时运行在回测、模拟和实盘模式下。因为框架在不同模式下会模拟或调用真实的底层接口，但提供给策略的接口 (init, khHandlebar 等) 保持一致。

## 五、 事半功倍：khQTTools 策略辅助工具箱

为了让策略开发更加高效和不易出错，KhQuant 在 khQTTools.py 中提供了一系列精心设计的辅助函数。这些函数封装了常见的、易错的计算和操作，建议在策略中优先使用它们。

### **5.1 generate\_signal函数**

这是**生成交易信号的首选函数**。它完美解决了策略开发者在构建信号字典时**"买卖数量计算复杂且易错"、"信号格式容易遗漏或错误"**的核心痛点。它可以实现：

* **买入 (action='buy')**: 调用 calculate\_max\_buy\_volume 函数，根据传入的 ratio (资金使用比例) 和当前账户可用资金 (data['\_\_account\_\_']['cash'])，以及考虑了交易成本后，计算出实际能买入的最大股数 (已向下取整至 100 的倍数)。
* **卖出 (action='sell')**: 获取当前该股票的**可用持仓** (data['\_\_positions\_\_'][stock\_code]['can\_use\_volume'])，根据传入的 ratio (持仓使用比例) 计算出要卖出的目标股数，并向下取整至 100 的倍数。
* **自动格式化:** 自动填充 code, action, price (处理精度), volume (确保整数和手数)。如果未提供 reason，会生成一个默认的原因字符串。自动添加 timestamp。
* **健壮性:** 如果计算出的可买/可卖数量为 0（资金不足、持仓不足、比例过低等），则返回空列表 []，并记录警告日志，避免生成无效信号。
* **使用建议:** 在 khHandlebar 中，一旦判断需要交易，直接调用此函数获取信号列表，然后返回即可。
* **示例:** signals = generate\_signal(data, '000001.SZ', 10.5, 0.5, 'buy', '价格突破，半仓买入')

### **5.2 其他工具**

khQTTools.py 还包含如 is\_trade\_day(date\_str)（判断是否为交易日）、is\_trade\_time()（判断是否在交易时间段内）等实用函数，进一步简化策略中的常见判断。

## 六、 总结：简单、高效、专注

KhQuant 量化交易系统的策略框架，通过其标准化的事件处理函数、集中的数据传递机制、规范化的信号输出以及强大的辅助工具箱，为策略开发者铺就了一条通往高效开发的捷径。它旨在让你摆脱底层实现的泥潭，将宝贵的智力资源聚焦于策略的创新与优化。

KhQuant 的目标是成为策略开发者强大而易用的伙伴。我们相信，通过持续的迭代和对新技术的积极融合，KhQuant 框架将能更好地服务于广大宽客，让量化交易变得更加简单、高效和专注。

## 七、 未来展望与持续发展

KhQuant 策略框架的设计是一个持续演进的过程，旨在不断提升策略开发的效率和体验。

*   **khQTTools 工具箱的持续丰富:** 未来，`khQTTools` 策略辅助工具箱还会不断补充和完善新的函数，例如更复杂的订单类型封装、常用的数据处理工具、风险管理组件等，为策略编写提供更多便利，进一步减少重复劳动。
*   **框架的迭代与更新:** 当前您所了解的是 KhQuant 策略框架的 1.0 版本。随着实践经验的积累和用户反馈的收集，框架的定义和接口后续也有可能会有所调整和优化。所有重要的更新和版本迭代信息，我们都会在 KhQuant 的官方网站或相关文档库中进行实时更新和详细说明，敬请关注。
*   **拥抱 AI，赋能策略生成:** 为了进一步降低量化交易的门槛，我们认识到标准化框架对于 AI 赋能的重要性。后续计划设计一套与 KhQuant 框架紧密结合的提示词工程（Prompt Engineering）体系。这套体系旨在让开发者能够通过自然语言描述交易思想，借助 DeepSeek 等先进的大型语言模型，快速生成符合 KhQuant 框架规范、可直接运行和回测的策略代码雏形，极大地加速从想法到验证的过程。

KhQuant 的目标是成为策略开发者强大而易用的伙伴。我们相信，通过持续的迭代和对新技术的积极融合，KhQuant 框架将能更好地服务于广大宽客，让量化交易变得更加简单、高效和专注。
