<!DOCTYPE html>
<html>
<head>
<title>KHQuant_Manual_Chapter5.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E7%AC%AC%E4%BA%94%E7%AB%A0%E4%B8%BB%E7%95%8C%E9%9D%A2%E5%B7%A1%E8%A7%88%E5%8A%9F%E8%83%BD%E5%8C%BA%E8%AF%A6%E8%A7%A3">第五章：主界面巡览：功能区详解</h1>
<p>欢迎来到&quot;看海量化交易系统&quot;（KHQuant）的主控界面。本章将对主界面进行一次全面的巡览，概要介绍各个功能区的布局与作用。这是一个整体性的介绍，对于左侧的核心配置区、中间的运行驱动区以及右侧的信息反馈区，我们将在随后的第六、七、八章中进行更深入的拆解说明。熟悉主界面的整体布局，是高效施展策略的第一步。</p>
<blockquote>
<p><strong>重要提示：关于运行模式</strong></p>
<p>当前版本的&quot;看海量化交易系统&quot;专注于提供强大、易用的 <strong>回测 (Backtesting)</strong> 功能，旨在帮助用户在历史数据上验证和优化自己的交易策略。因此，软件界面和功能均围绕回测模式进行设计。</p>
<p><strong>实盘交易 (Live Trading) 功能目前暂不支持。</strong></p>
</blockquote>
<hr>
<h2 id="51-%E6%95%B4%E4%BD%93%E5%B8%83%E5%B1%80%E4%B8%80%E8%A7%88">5.1 整体布局一览</h2>
<p>首次打开软件，会看到一个精心组织的三栏式布局界面。这种设计的目的是将策略配置、运行监控和状态反馈等核心操作流程清晰地分隔开，从而一目了然地找到所需功能。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/主界面概览.png" alt="主界面概览" width="100%" />
</p>
<p>主界面主要由以下几个部分构成：</p>
<ul>
<li><strong>顶部工具栏</strong>: 位于界面最上方，集成了最高频使用的全局操作，如配置文件的加载/保存、策略的启停、以及打开辅助工具等。</li>
<li><strong>左侧面板 (核心配置区)</strong>: 这里是定义策略行为的核心区域。可在此指定策略文件、设置回测参数、管理股票池等。</li>
<li><strong>中间面板 (运行驱动区)</strong>: 该区域负责定义策略的&quot;心跳&quot;—即由什么事件来驱动策略逻辑的执行。可设置触发方式、配置盘前盘后任务，并查看账户的资金与持仓状况。</li>
<li><strong>右侧面板 (信息反馈区)</strong>: 这是观察系统运行状态的窗口。系统日志、策略中打印的信息、交易委托与成交回报、错误警报等都会在这里显示。</li>
<li><strong>底部状态栏</strong>: 位于界面最下方，提供实时的操作状态反馈和回测进度。</li>
</ul>
<hr>
<h2 id="52-%E5%B7%A5%E5%85%B7%E6%A0%8F%E6%8C%89%E9%92%AE%E8%AF%B4%E6%98%8E">5.2 工具栏按钮说明</h2>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/工具栏.png" alt="工具栏" width="100%" />
</p>
<p>顶部工具栏是进行快速操作的捷径，以下是每个按钮的功能详解：</p>
<ul>
<li><strong>加载配置</strong>: 点击后加载一个之前保存的 <code>.kh</code> 配置文件，快速恢复所有参数设置。</li>
<li><strong>保存配置</strong>: 将当前所有参数设置保存到当前加载的 <code>.kh</code> 文件中。</li>
<li><strong>配置另存为</strong>: 将当前所有设置保存为一个新的 <code>.kh</code> 文件。</li>
<li><strong>开始运行</strong>: 根据当前配置，启动策略回测。</li>
<li><strong>停止运行</strong>: 手动停止当前正在运行的策略。</li>
<li><strong>数据</strong>: 打开数据中心，用于补充和管理回测所需的历史数据。</li>
<li><strong>设置</strong>: 打开&quot;软件设置&quot;对话框，配置MiniQMT路径等全局参数。</li>
<li><strong>MiniQMT状态指示灯</strong>: 显示与MiniQMT的连接状态（🟢已连接 / 🔴未连接）。</li>
<li><strong>帮助 (?)</strong>: 打开在线教程文档。</li>
</ul>
<hr>
<h2 id="53-%E5%B7%A6%E4%BE%A7%E6%A0%B8%E5%BF%83%E9%85%8D%E7%BD%AE%E5%8C%BA">5.3 左侧核心配置区</h2>
<p>这是进行策略回测设置的起点，所有参数都在此集中配置。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/左侧面板.png" alt="左侧面板" width="50%" />
</p>
<ul>
<li>
<p><strong>策略配置</strong>:</p>
<ul>
<li><strong>策略文件</strong>: 点击&quot;选择策略文件&quot;按钮，选择编写的Python策略脚本 (<code>.py</code>文件)。</li>
<li><strong>运行模式</strong>: 当前版本专注于 <strong>回测</strong> 功能。此选项固定为回测模式，用于在历史数据上验证策略表现。</li>
</ul>
</li>
<li>
<p><strong>回测参数</strong>:</p>
<ul>
<li><strong>基准合约</strong>: 设置用于计算Alpha、Beta等相对表现指标的业绩基准，例如 <code>sh.000300</code>。</li>
<li><strong>交易成本设置</strong>: 精确模拟真实交易成本。
<ul>
<li><strong>最低佣金(元)</strong>: 单笔交易佣金的最低收费。</li>
<li><strong>佣金比例</strong>: 按成交金额计算的佣金费率。</li>
<li><strong>卖出印花税</strong>: 按卖出金额计算的印花税率。</li>
<li><strong>流量费(元/笔)</strong>: 部分券商收取的额外通讯费用。</li>
<li><strong>滑点类型/滑点值</strong>: 设置买卖时价格的滑点，可选&quot;按最小变动价位数&quot;或&quot;按成交额比例&quot;。</li>
</ul>
</li>
<li><strong>回测时间设置</strong>: 通过&quot;开始日期&quot;和&quot;结束日期&quot;选择器，设定回测的时间区间。</li>
</ul>
</li>
<li>
<p><strong>数据设置</strong>:</p>
<ul>
<li><strong>复权方式</strong>: 选择K线数据的复权类型，如&quot;不复权&quot;、&quot;前复权&quot;、&quot;等比前复权&quot;。</li>
<li><strong>周期类型</strong>: 选择策略运行所依赖的数据周期，如 <code>tick</code>, <code>1m</code>, <code>5m</code>, <code>1d</code>。</li>
<li><strong>数据字段</strong>: 根据所选周期，勾选策略在 <code>handle_bar</code> 函数中需要用到的具体数据字段（如开盘价、收盘价、成交量等）。</li>
</ul>
</li>
<li>
<p><strong>股票池设置</strong>:</p>
<ul>
<li><strong>常用指数</strong>: 快速勾选A股主要指数成分股作为股票池。</li>
<li><strong>自选清单</strong>: 使用自选股列表。</li>
<li><strong>手动管理</strong>: 在下方的表格中直接添加或删除股票代码，或通过右键菜单导入/清空列表。</li>
</ul>
</li>
</ul>
<hr>
<h2 id="54-%E4%B8%AD%E9%97%B4%E8%BF%90%E8%A1%8C%E9%A9%B1%E5%8A%A8%E5%8C%BA">5.4 中间运行驱动区</h2>
<p>本区域负责定义策略的触发机制和账户信息。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/中间面板.png" alt="中间面板" width="50%" />
</p>
<ul>
<li>
<p><strong>触发方式设置</strong>:</p>
<ul>
<li><strong>触发类型</strong>: 定义策略核心逻辑 (<code>handle_bar</code>函数) 的执行频率。
<ul>
<li><strong>Tick触发</strong>: 每个Tick数据到达时都执行一次策略。</li>
<li><strong>K线触发</strong>: 在每个K线周期（如1分钟、5分钟）形成时执行一次策略。</li>
<li><strong>自定义定时触发</strong>: 按设定的特定时间点列表来执行策略。</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>账户信息</strong>:</p>
<ul>
<li><strong>虚拟账户</strong>: 在回测模式下，可在此设置策略的&quot;初始资金&quot;和&quot;最小交易量&quot;。</li>
</ul>
</li>
<li>
<p><strong>盘前盘后触发设置</strong>:</p>
<ul>
<li>勾选并设置时间，可以在每日开盘前或收盘后，自动执行策略中相应的 <code>khPreMarket</code> 或 <code>khPostMarket</code> 函数，用于执行盘前准备或盘后复盘等任务。</li>
</ul>
</li>
</ul>
<hr>
<h2 id="55-%E5%8F%B3%E4%BE%A7%E4%BF%A1%E6%81%AF%E5%8F%8D%E9%A6%88%E5%8C%BA">5.5 右侧信息反馈区</h2>
<p>这里是观察策略运行过程和结果的主要窗口。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/右侧面板.png" alt="右侧面板" width="50%" />
</p>
<ul>
<li>
<p><strong>系统日志</strong>:</p>
<ul>
<li>一个实时滚动的文本框，显示软件的运行状态、策略中 <code>print()</code> 的内容、交易委托和成交的详细回报、以及任何错误或警告信息。不同级别的日志会用不同颜色标记，方便快速识别。</li>
</ul>
</li>
<li>
<p><strong>日志操作</strong>:</p>
<ul>
<li><strong>日志类型过滤</strong>: 通过勾选 <code>DEBUG</code>, <code>INFO</code>, <code>WARNING</code>, <code>ERROR</code>, <code>TRADE</code> 等复选框，可以筛选想看的日志级别。</li>
<li><strong>清空日志</strong>: 清除当前日志显示。</li>
<li><strong>保存日志</strong>: 将当前显示的日志内容导出为文本文件。</li>
<li><strong>测试日志</strong>: 点击后会生成一些各种级别的测试日志，用于检查显示是否正常。</li>
<li><strong>打开回测指标</strong>: <strong>回测结束后，此按钮会变为可用状态</strong>。点击它，即可打开详细的回测报告窗口，对策略绩效进行全面复盘。</li>
</ul>
</li>
</ul>
<hr>
<h2 id="56-%E5%BA%95%E9%83%A8%E7%8A%B6%E6%80%81%E6%A0%8F">5.6 底部状态栏</h2>
<p>界面最底部的状态栏提供实时的上下文信息。</p>
<ul>
<li><strong>左侧：当前状态文本</strong>: 用简短文字描述软件正在进行的操作（如&quot;准备就绪&quot;, &quot;策略运行中...&quot;）。</li>
<li><strong>右侧：进度条</strong>: 在回测进行时，会激活并直观地展示回测的完成进度。</li>
</ul>
<p>至此，对&quot;看海量化交易系统&quot;主界面的概览就完成了。在下一章，我们将深入探索左侧的核心配置面板，学习如何为策略配置各项参数。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/底部状态栏.png" alt="底部状态栏" width="100%" />
</p>

</body>
</html>
