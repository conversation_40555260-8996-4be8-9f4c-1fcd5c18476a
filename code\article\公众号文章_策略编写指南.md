# 告别重复造轮子：手把手教你用khQuant框架构建专业级量化策略

每一位投身于量化交易的探索者，心中都有一幅宏伟的蓝图——一个能将自己独特交易思想转化为稳定盈利的自动化系统。然而，理想与现实之间，往往隔着一条布满荆棘的鸿沟。数据获取、事件驱动、订单管理、风险控制……从零开始构建一套完整的交易系统，不仅耗时耗力，更会将我们的大部分精力从核心的"策略研发"上抽离出来。

难道就没有一条更高效的路径吗？

当然有。专业的量化开发者从不重复造轮子，他们善于利用**策略框架**。今天，我们就将为您揭开看海量化(`khQuant`)策略框架的神秘面纱，带您一步步构建起属于自己的、专业级的交易策略。

---

## 什么是策略框架？——你的智能交易合伙人

想象一下，您是一位经验丰富的交易员，但不懂编程。现在，您雇佣了一位全能的、24小时待命的操盘手助理。这位助理已经为您解决了所有技术难题：他能实时盯盘、管理账户、处理复杂的交易指令，甚至还能在盘后帮您写好交易日志。

您唯一需要做的，就是在特定时间点，用清晰的指令告诉他"该做什么"。比如：

*   **每天开盘前**：告诉他今天要重点关注哪些股票。
*   **盘中**：当某个条件满足时，立刻告诉他"买入"或"卖出"。
*   **收盘后**：让他整理今天的交易记录，为明天做准备。

**khQuant策略框架，扮演的正是这位"智能交易合伙人"的角色。**

它将复杂的交易流程抽象为四个核心的"沟通节点"（我们称之为**回调函数**），您只需要在这些节点里填入您的交易逻辑即可：

*   `init()`：**策略初始化**。任务开始时，您对助理进行一次性岗前培训，告诉他一些全局规则。
*   `khPreMarket()`：**盘前准备**。每个交易日开盘前，您给他一份今天的"作战计划"。
*   `khHandlebar()`：**盘中决策**。盘中，他会根据您设定的频率（比如每分钟）不断地向您汇报最新情况，并等待您的实时指令。这是策略最核心的部分。
*   `khPostMarket()`：**盘后复盘**。收盘后，您和他一起复盘，总结经验。

---

## `context` - 决策所需的一切信息

既然是沟通，助理在向您汇报时，必须提供全面、准确的信息，否则您将无法做出正确决策。在`khQuant`中，所有这些信息都被打包在一个名为 `context` 的"公文包"（一个Python字典）里，并在每次沟通时递交给您。

打开这个`context`公文包，里面有几份关键文件：

1.  `__current_time__`：一份**精准的时间报告**，告诉您当前是何年何月何日何时何分何秒。
2.  `__account__`：一份**实时资金报告**，详细列出了您的可用现金、持仓市值、总资产等。
3.  `__positions__`：一份**详细的持仓清单**，每一笔持仓的成本价、持仓数量、当前浮动盈亏都一目了然。
4.  `[股票代码]`：您所关注的每一只股票的**实时行情切片**（一个Pandas Series），包含了最新的开高低收价格和成交量。

有了`context`，您就如同坐在一个信息高度集成的"交易驾驶舱"中，运筹帷幄。

---

## 从想法到代码——构建一个完整的MACD策略

理论讲了这么多，让我们来一次真枪实弹的演练。我们将一起从零开始，构建一个全市场最经典的——**MACD金叉买入、死叉卖出**策略。

**策略逻辑：**

*   当某只股票的MACD指标发生"金叉"（DIF线从下向上穿过DEA线）时，买入。
*   当其发生"死叉"（DIF线从上向下穿过DEA线）时，卖出。

让我们看看如何用`khQuant`框架实现它。

### 第一步：导入"兵器库"

`khQuant`不仅有框架，还提供了一个强大的工具箱 `khQTTools` 和专业的指标计算库 `talib`。我们需要先把它们请进来。

```python
# 导入Python基础工具和类型提示
from typing import Dict, List
import logging
import talib

# 导入khQuant的强大工具箱
from khQTTools import khHistory, generate_signal
```

### 第二步：`init()` - 全局初始化

在策略开始时，我们需要做一些一次性的准备工作。对于MACD策略，我们主要需要一个地方来存储每只股票的MACD历史值，以便判断金叉/死叉的穿越行为。我们用一个全局字典 `g` 来完成这个任务。

```python
# g 作为全局变量存储器，用于在不同函数间传递数据
g = {
    'stock_last_dif': {}, # 存储每只股票上一根K线的DIF值
    'stock_last_dea': {}  # 存储每只股票上一根K线的DEA值
}

def init(stock_list, context):
    """
    初始化函数：获取所有股票的历史数据，并计算初始的MACD值。
    """
    logging.info("策略开始初始化，正在计算初始MACD值...")

    # 1. 一次性获取所有股票过去100天的历史数据，为计算MACD做准备
    history_data = khHistory(
        symbol_list=stock_list,
        fields=['time', 'open', 'close'],
        bar_count=100,
        fre_step='1d' # 我们基于日线级别计算
    )

    # 2. 遍历每只股票，计算并存储其初始的DIF和DEA值
    for stock in stock_list:
        df = history_data.get(stock)
        if df is not None and not df.empty and len(df) > 34: # MACD计算需要一定数据量
            # 使用talib库计算MACD
            dif, dea, _ = talib.MACD(df['close'], fastperiod=12, slowperiod=26, signalperiod=9)

            # 我们只关心最近一个交易日（即初始状态）的数值
            g['stock_last_dif'][stock] = dif.iloc[-1]
            g['stock_last_dea'][stock] = dea.iloc[-1]
            logging.info(f"已计算 {stock} 的初始MACD值：DIF={dif.iloc[-1]:.2f}, DEA={dea.iloc[-1]:.2f}")
```

### 第三步：`khHandlebar()` - 核心决策逻辑

这是策略的心脏。每当新的K线数据推送过来，`khHandlebar`就会被调用。我们需要在这里计算最新的MACD值，并与上一根K线的值进行比较，以判断是否形成金叉或死叉。

```python
def khHandlebar(context: Dict) -> List[Dict]:
    """
    策略主逻辑：在每个交易日被调用，判断金叉死叉并生成信号。
    """
    signals = []
    stock_list = [code for code in context.keys() if code.endswith(('.SH', '.SZ'))] # 从context获取股票池

    # 获取最新的历史数据来计算当前MACD
    # 注意：这里bar_count=100是为了确保talib有足够数据，实际计算只基于最新数据
    history_data_new = khHistory(
        symbol_list=stock_list,
        fields=['time', 'open', 'close'],
        bar_count=100,
        fre_step='1d'
    )

    for stock in stock_list:
        df = history_data_new.get(stock)
        if df is None or df.empty or len(df) <= 34:
            continue # 数据不足，跳过

        # 1. 计算最新的MACD值
        current_dif, current_dea, _ = talib.MACD(df['close'], fastperiod=12, slowperiod=26, signalperiod=9)
        
        # 获取最新的有效值
        current_dif_val = current_dif.iloc[-1]
        current_dea_val = current_dea.iloc[-1]

        # 2. 从全局变量g中获取上一根K线的MACD值
        last_dif = g['stock_last_dif'].get(stock)
        last_dea = g['stock_last_dea'].get(stock)

        # 3. 判断金叉/死叉
        if last_dif is not None and last_dea is not None:
            # 金叉判断：上一刻DIF < DEA，这一刻DIF > DEA
            if last_dif < last_dea and current_dif_val > current_dea_val:
                logging.info(f"【金叉信号】{stock}: DIF({current_dif_val:.2f}) 上穿 DEA({current_dea_val:.2f})，生成买入信号。")
                # 使用工具函数生成买入信号，假设我们每次用20%的资金买入
                buy_signal = generate_signal(context, stock, price=df['close'].iloc[-1], ratio=0.2, action='buy', reason='MACD金叉')
                signals.extend(buy_signal)

            # 死叉判断：上一刻DIF > DEA，这一刻DIF < DEA
            elif last_dif > last_dea and current_dif_val < current_dea_val:
                # 检查是否持仓
                if stock in context['__positions__']:
                    logging.info(f"【死叉信号】{stock}: DIF({current_dif_val:.2f}) 下穿 DEA({current_dea_val:.2f})，生成卖出信号。")
                    # 使用工具函数生成卖出信号，卖出该股票所有持仓
                    sell_signal = generate_signal(context, stock, price=df['close'].iloc[-1], ratio=1.0, action='sell', reason='MACD死叉')
                    signals.extend(sell_signal)

        # 4. 更新g中的值为当前值，为下一次判断做准备
        g['stock_last_dif'][stock] = current_dif_val
        g['stock_last_dea'][stock] = current_dea_val

    return signals
```

### 第四步：收尾与展望

至此，一个逻辑清晰、结构完整的MACD交易策略就完成了！您只需将这段代码保存为一个`.py`文件，在`khQuant`主界面加载它，设置好回测参数，即可看到它在历史数据中自动执行金叉买入、死叉卖出的每一个决策。

这就是框架的力量。它将您从繁琐的底层细节中解放出来，让您可以像今天这样，将100%的精力投入到策略逻辑本身。

本文只是一个开始。基于这个框架，您可以轻松地扩展出更复杂的策略：结合RSI、布林带等多指标进行过滤；引入复杂的仓位管理模块；甚至对接您自己训练的机器学习模型。

告别重复造轮子，拥抱策略框架，开启您的专业量化之旅吧！ 