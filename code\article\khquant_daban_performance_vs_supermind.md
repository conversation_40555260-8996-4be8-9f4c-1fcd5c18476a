> 我是Mr.看海，我在尝试用信号处理的知识积累和思考方式做量化交易，应用深度学习和AI实现股票自动交易，目的是实现财务自由\~
> 目前我正在开发基于miniQMT的量化交易系统——看海量化交易系统（KhQuant 是其核心框架）。

## 一、引言：效率是复杂策略的生命线

继上一篇我们深入对比KhQuant与Supermind在打板策略交易细节上的差异后，本文将揭晓一个更为令人振奋的对比结果——**执行效率**。

当策略逻辑的准确性得到保障之后，回测速度便成为决定研发迭代效率的生命线。对于"打板"这类需要海量计算和快速验证的策略，每一秒的节省都至关重要。

高效的回测意味着更快的策略迭代速度，能让策略开发者在有限的时间内测试更多的参数组合、验证更多的市场假设。在KhQuant的设计与优化过程中，性能始终是我关注的核心焦点之一。

本文将聚焦于"打板策略"在两个平台上的运行时间，通过真实的运行数据，展现KhQuant在执行效率方面的表现和优化思路。

## 二、Supermind的执行时间表现

我们沿用前文所述的"打板策略"，这个打板策略是按照1m数据触发运行的，每次触发要遍历300支股票数据，是需要一定的运算资源，因此比较适合做策略运行效率的对比，又不至于等待时间过长。

为了确保对比的公平性，在相同的回测条件下（回测区间：2024-09-10至2024-12-10，基准：沪深300，K线类型：分钟线，初始资金：1,000,000）进行测试。

在Supermind平台上运行此打板策略，根据其回测报告，**总运行总时长为 1233秒**。

![](https://pic1.zhimg.com/80/v2-a32eeacd0cf2286fa25eff88b1eccfc7_720w.webp?source=d16d100b)

图1: Supermind运行打板策略时间记录

这个时间为我们提供了一个参照基准，接下来我们将看看KhQuant的表现如何。

## 三、KhQuant的性能优化之旅：从实时到高效

KhQuant在开发过程中经历了一个性能不断优化的过程。尤其对于日志系统这类辅助功能，如何在提供充分信息与保证系统性能之间取得平衡，是一个需要审慎考虑的问题。

### 3.1 初始状态：实时日志的挑战

KhQuant在最初的版本中，为了最大化策略调试的便利性和过程的透明度，默认启用了非常详尽的实时日志输出机制。这意味着策略在运行过程中的每一个关键步骤、每一条信息都会被即时记录，并同步更新到GUI的日志窗口中。

![动图封面](https://picx.zhimg.com/v2-a11d45b1bf741c87b9555f0a5aab7431_720w.jpg?source=d16d100b)

快速刷新的日志内容

在这种设置下，运行与Supermind相同的打板策略，KhQuant的日志记录显示：

* **策略主要逻辑执行耗时：4799.10秒** (约80分钟)
* 策略总运行时间：4800.02秒

![](https://picx.zhimg.com/80/v2-03c32d3fe496b5567e3f890bdae8768e_720w.webp?source=d16d100b)

图2: KhQuant开启实时日志显示时运行打板策略的时间记录

可以看到，尽管实时日志为策略执行提供了"上帝视角"，但频繁的日志写入和GUI刷新操作，在高并发、计算密集型的分钟级回测中，无疑成为了一个显著的性能瓶颈。

### 3.2 优化之道：巧用"延迟日志"，释放回测潜能

意识到实时日志的性能掣肘后，我迅速找到了突破口。毕竟，对于多数回测而言，我们并非需要紧盯屏幕上的每一行日志输出，一个能实时反馈进度的进度条往往就已足够。真正的日志价值更多体现在策略跑完后的复盘分析，或是出现问题时的精准定位。

基于此，KhQuant引入了简洁而高效的**"延迟日志显示"**机制。其核心思想非常直观：在策略回测过程中，系统会将产生的日志信息高速缓存（暂存于内存或临时文件），待整个回测任务结束后，再一次性、快速地将所有日志加载并呈现在GUI界面中。这极大地减少了运行时的I/O阻塞和GUI刷新压力。

**如何在KhQuant中启用？** 非常简单，在我的KhQuant软件的"**软件设置**"对话框中，您只需轻轻一点，勾选"**延迟显示日志**"复选框即可。

![](https://pic1.zhimg.com/80/v2-f46d7bced7a92cbad4cffdc8cb79e101_720w.webp?source=d16d100b)

### 3.3 优化成果：运行效率的飞跃

这一小小的改动，带来的好处却是立竿见影的：

**1.性能飙升**：回测速度因此获得了数倍甚至数十倍的提升，让策略迭代如虎添翼。

我们再次运行完全相同的打板策略。这一次，性能表现令人振奋：

* **策略主要逻辑执行耗时：215.01秒** (约3.6分钟)
* 策略总运行总时长：3分35.76秒 (即 **215.76秒**)

![](https://pic1.zhimg.com/80/v2-834a022618a83e84dcf894c5d8ea6b51_720w.webp?source=d16d100b)

图3: KhQuant采用延迟显示日志后运行打板策略的时间记录

从约4800秒到约216秒，**KhQuant的执行效率提升了超过22倍** (计算依据：4799.10 / 215.01 ≈ 22.32)。这充分证明了针对日志系统优化的正确性和有效性。

**2.不影响检测回测进度**：主界面依然会显示回测进度条，不会在没有日志输出的时候茫然无措。

![动图封面](https://picx.zhimg.com/v2-61a5e2fbca74f07566e415f0af16167e_720w.jpg?source=d16d100b)

**3.信息不丢失**："延迟"不等于"丢失"。所有日志都会被完整保留，方便在回测结束后细致查阅，精准复盘。

当然，如果在策略调试的特定阶段，确实需要实时观察某些关键变量或信号的输出，我依然建议在策略代码中使用精简的 print语句进行定向输出，而不是依赖系统层面的全量实时日志。

### 3.4 深入剖析：优化后KhQuant的耗时结构

为了更深入地理解KhQuant在优化后的高效表现，我们可以查看其详细的内部耗时日志。这不仅展现了系统的性能构成，也体现了KhQuant在设计上对透明度的追求。

根据启用"延迟日志显示"并优化后的运行日志（对应图3的运行实例），"回测各部分执行时间统计"揭示了主要耗时分布（基于其内部统计的该部分总执行时间约203.26秒）：

* **构造数据**: 133.3872秒 (占约 65.62%) - 这是回测中最主要的时间开销，负责根据策略需求准备和加载历史行情数据。
* **检查新日期**: 40.2841秒 (占约 19.82%) - 用于在回测过程中判断和切换交易日期。
* **盘前回调**: 36.8022秒 (占约 18.11%) - 执行策略中定义的 `khPreMarket` 函数，通常用于每日开盘前的数据准备和状态重置。
* **策略处理**: 25.7967秒 (占约 12.69%) - 即策略的核心逻辑 `khHandlebar` 函数的执行时间，包含了根据行情判断和生成交易信号的过程。
* **记录结果**: 2.0584秒 (占约 1.01%) - 将回测过程中的交易、持仓、资金等状态记录下来。
* **其他环节耗时极短**：
  * 构造时间信息: 0.5108秒 (0.25%)
  * 交易指令生成与处理: 0.4665秒 (0.23%)
  * 触发器检查: 0.0758秒 (0.04%)
  * 风控检查: 0.0244秒 (0.01%)
  * 信号处理: 0.0060秒 (0.00%)
  * 盘后回调 (`khPostMarket`): 几乎不耗时 (0.00%)

![](https://pic1.zhimg.com/80/v2-834a022618a83e84dcf894c5d8ea6b51_720w.webp?source=d16d100b)

图4: KhQuant优化后运行打板策略的详细耗时结构 (同图3)

从这个耗时结构中，我们可以看到：

1. **数据准备是主要部分**："构造数据"占据了大部分时间，这在数据密集型的回测中是常见现象，尤其是在处理分钟级别数据时。KhQuant 在此环节已经做了包括批量加载在内的诸多优化。
2. **核心逻辑高效**：真正的策略判断（策略处理）、交易指令的生成与传递、风控检查等核心交易循环相关的部分耗时非常低，表明KhQuant的事件处理和交易引擎具有很高的执行效率。
3. **透明度与可优化性**：这种详细的耗时分解，不仅证明了系统的性能，也为未来可能的进一步极致优化指明了方向。如果特定策略在"盘前回调"或"策略处理"等环节耗时过高，开发者可以针对性地审视和优化自己的策略代码。

综合来看，这个耗时结构是健康的，它确保了KhQuant在处理复杂策略时，能够将主要的计算资源有效地用于核心的策略逻辑和必要的数据处理上，从而为整体约215秒的"策略主要逻辑耗时"和最终约215.76秒的"策略总运行总时长"提供了坚实的性能基础。

## 四、效率对比：KhQuant后来居上

现在，我们可以将优化后的KhQuant与Supermind的执行时间进行直接对比了：

* **Supermind 执行时间**：1233 秒
* **KhQuant (优化后) 执行时间**：215.76 秒

显而易见，经过优化，**KhQuant在运行此打板策略时的速度比Supermind快了约 5.71 倍！**

这一显著的效率优势，得益于KhQuant在底层架构上的精心设计，例如高效的事件驱动模型、批量数据预加载和处理机制，以及对日志系统这类辅助功能可能产生的性能影响的审慎评估和持续优化。

不过需要说明的是，khQuant的运行效率是依赖于你所使用的硬件的，我的硬件测试环境为：

* **CPU**: Intel Core i7-12700K
* **内存**: 64GB
* **操作系统**: Windows 11
* **硬盘**: SSD
* **显示器分辨率**: 3840\*2160

（注：同花顺 Supermind 为云平台，其硬件由服务商提供，我们无法直接控制或了解其细节。）

我的测试环境中，内存是较大的，CPU则中规中矩，总的来说硬件成本不高。本地运行给了土豪用户在运行效率上极高的上限空间。至于硬件下限需求，后续我再找机会测试。

## 五、下一步工作

软件内测发布前的测试工作已经基本完成了，剩下的是一些收尾工作，包括使用手册撰写，软件封装等等。不会让大家等太久了，内测很快就要开始！

**内测计划说明**:

在完成上述更充分的验证测试之后，我计划启动"看海量化交易系统 (KhQuant)"的 Beta 内测阶段。

* **优先体验**: 为了感谢大家的支持，通过**我推荐的渠道开通 MiniQMT 账户的朋友**，在内测开始后将获得优先体验 Beta 版本软件，可以加入内部讨论群第一时间得到作者的问题解答，后续的一些策略也在内部讨论群小范围分享。
* **公开与开源**: 请已经开通账户或暂时不方便通过推荐渠道开户的朋友放心，**内测结束后，软件将会公开发布，核心代码也计划进行开源**，届时所有人都可以使用和参与改进。

## **六、 关于开通 MiniQMT**

**什么是 MiniQMT？**

MiniQMT 是迅投（QMT）系统提供的一个程序化交易接口（API）。QMT 是目前国内许多券商采用的主流柜台系统之一，而 MiniQMT 允许用户通过编程方式（主要是 Python）连接到证券公司的交易服务器，进行行情获取、策略计算、下单交易等操作。它通常由支持 QMT 的券商**免费**提供给客户使用（可能需要满足一定的资产要求），以其稳定性和执行效率受到不少量化交易者的青睐。

**看海量化交易系统 (KhQuant) 与 MiniQMT**

我正在开发的"看海量化交易系统 (KhQuant)"正是**基于 MiniQMT 接口**进行构建的。这意味着，使用该软件需要一个 MiniQMT 账户作为底层支持。

**推荐开通渠道**

如果您还没有 MiniQMT 账户，并希望未来能够顺利使用"看海量化交易系统 (KhQuant)"进行策略回测，或者希望支持我的开发工作，请大家关注一下我的公众号“看海的城堡”，在公众号页面下方点击相应标签即可获取开通方式。

![](https://picx.zhimg.com/80/v2-c2880bcbeb3d920d87366a501b738a09_720w.webp?source=d16d100b)

选择推荐渠道并非强制要求，但这样做一方面能确保您开通的账户类型与 KhQuant 兼容，另一方面也能对我正在进行的开发工作提供支持和肯定。再次感谢大家的关注！

## **七、 免责声明**

本文所有内容仅供学习和技术交流使用，不构成任何投资建议。所述策略及回测结果仅为历史数据模拟，不代表未来实际表现。投资者据此操作，风险自担。

## **相关文章**
