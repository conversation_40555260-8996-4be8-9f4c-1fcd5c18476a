<!DOCTYPE html>
<html>
<head>
<title>KHQuant_Manual_Chapter7.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E7%AC%AC%E4%B8%83%E7%AB%A0%E4%B8%AD%E9%97%B4%E8%BF%90%E8%A1%8C%E9%A9%B1%E5%8A%A8%E5%8C%BA%E5%AE%9A%E4%B9%89%E7%AD%96%E7%95%A5%E7%9A%84%E5%BF%83%E8%B7%B3">第七章：中间运行驱动区：定义策略的心跳</h1>
<p>中间面板是策略的&quot;运行驱动区&quot;，其核心任务是定义策略逻辑（即 <code>khHandlebar</code> 函数）被调用的频率和时机。可以说，这里决定了策略的&quot;心跳&quot;。本章将详细介绍三种不同的触发方式，并说明如何配置账户信息及盘前盘后任务。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/中间面板.png" alt="中间面板" width="50%" />
</p>
<hr>
<h2 id="71-%E8%A7%A6%E5%8F%91%E6%96%B9%E5%BC%8F%E8%AE%BE%E7%BD%AE">7.1 触发方式设置</h2>
<p>触发方式定义了策略的执行频率。KHQuant提供三种模式，以适应不同类型策略的需求。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/触发方式列表.png" alt="中间面板" width="50%" />
</p>
<h3 id="%E4%B8%80tick%E8%A7%A6%E5%8F%91%E7%81%B5%E6%B4%BB%E4%BD%86%E8%B4%9F%E6%8B%85%E9%87%8D">一、Tick触发：灵活但负担重</h3>
<p>Tick触发在理论上最为简单，它监听每一笔市场成交数据(在MiniQMT中实际是3秒一次快照)，并在数据到达时执行策略。这为策略的编写带来了很大的空间，在策略中可以灵活地添加过滤机制，或者说第二层触发判断，当真正达到触发条件时，再执行策略。第二层触发判断用户在编写时就有了极大的发挥空间。</p>
<p>这种方式的缺点是回测数据量大，因为需要下载所有tick数据——即使有第二层触发条件，这使策略执行频率不会太高。</p>
<h3 id="%E4%BA%8Ck%E7%BA%BF%E8%A7%A6%E5%8F%91%E8%B6%8B%E5%8A%BF%E7%AD%96%E7%95%A5%E7%9A%84%E5%8F%AF%E9%9D%A0%E9%80%89%E6%8B%A9">二、K线触发：趋势策略的可靠选择</h3>
<p>K线触发基于固定时间周期的数据，在回测环境中实现简单，只需补充对应周期的K线数据即可。</p>
<p>在看海回测系统中，支持1分钟和5分钟这两种K线周期。这并非功能限制，而是当前依赖的MiniQMT非投研版仅支持这两种分钟级别的实时数据订阅。</p>
<blockquote>
<p>💡 <strong>实盘与回测的触发差异</strong></p>
<p>值得注意的是，在实盘环境中，即便是订阅<code>1m</code>或<code>5m</code>的K线数据，行情接口的推送频率通常也是3秒一次，每次推送的都是当前时间点最新的完整K线数据。因此，若想在实盘中严格实现&quot;每1分钟或5分钟K线走完后才触发一次&quot;，反而需要框架层或策略层编写额外的逻辑来支持。回测模式下则严格在K线结束后执行。</p>
</blockquote>
<p>K线触发由于数据量较小，系统资源消耗低，回测速度快，特别适合中长期趋势策略和技术分析策略，但这取决于您的具体策略设计。</p>
<h3 id="%E4%B8%89%E8%87%AA%E5%AE%9A%E4%B9%89%E6%97%B6%E9%97%B4%E8%A7%A6%E5%8F%91%E5%A2%9E%E5%8A%A0%E8%87%AA%E7%94%B1%E5%BA%A6">三、自定义时间触发：增加自由度</h3>
<p>自定义时间触发是KHQuant框架的关键功能，它允许用户指定精确的触发时间点，系统会在这些时间点到达时执行策略。</p>
<p>自定义时间触发特别适合定时交易策略，如开盘集合竞价策略、收盘前交易策略等，也适用于系统预设K线周期之外的场景(比如每10分钟，每小时等)。它为策略开发者提供了精确控制交易时机的能力。</p>
<blockquote>
<p>⚠️ <strong>注意：自定义触发的数据处理</strong></p>
<p>需要注意，自定义时间触发仅仅是&quot;触发&quot;策略的运行，<strong>它不会像Tick或K线触发那样，自动向策略的 <code>khHandlebar</code> 函数中传入当时的数据</strong>。策略需要在函数内部自行调用数据获取接口（如<code>get_market_data</code>）来获取所需数据。</p>
<p>这样设计是因为自定义时间点的前置数据需求多种多样，难以统一输入标准。同时，使用此类型触发的策略，其需求也往往比简单的K线数据更为复杂。</p>
</blockquote>
<h4 id="%E6%99%BA%E8%83%BD%E6%95%B0%E6%8D%AE%E9%80%82%E9%85%8D%E4%B8%8E%E6%97%B6%E9%97%B4%E7%82%B9%E7%94%9F%E6%88%90">智能数据适配与时间点生成</h4>
<p>为了优化性能，系统会自动分析用户设定的时间点特性：</p>
<ul>
<li>当所有触发时间点都是<strong>整分钟</strong>时（如09:30:00, 10:00:00），系统自动使用1分钟K线数据作为基础。</li>
<li>当存在<strong>非整分钟</strong>时间点时（如09:30:15, 10:05:45），系统则会切换到底层的Tick数据来确保精度。</li>
</ul>
<p>这种智能适配在保证策略执行精度的同时，显著优化了系统资源使用和回测效率。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/触发方式.png" />
</p>
<p>为了方便使用，我专门设计了一个<strong>自定义时间生成模块</strong>。通过设定起始、结束时间以及时间间隔，可以一键生成规范的触发时间列表。当然，也可以根据自己的需求在文本框中逐个手动编辑。</p>
<blockquote>
<p>💡 <strong>时间点精度提示</strong></p>
<p>由于MiniQMT的数据快照特性，所有时间点需设置为3的整数秒，以确保触发的稳定性和精确性。时间点生成工具已自动处理此逻辑。</p>
</blockquote>
<hr>
<h2 id="72-%E8%B4%A6%E6%88%B7%E4%BF%A1%E6%81%AF">7.2 账户信息</h2>
<p>由于当前版本专注于<strong>回测</strong>功能，此区域的功能也相应简化：</p>
<ul>
<li><strong>初始资金</strong>: 在此设置回测开始时策略所拥有的虚拟资金总额。</li>
<li><strong>最小交易量</strong>: 此项设置保留，但在当前的回测逻辑中并未实际启用限制。</li>
</ul>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/账户信息.png" />
</p>
<hr>
<h2 id="73-%E7%9B%98%E5%89%8D%E7%9B%98%E5%90%8E%E8%A7%A6%E5%8F%91%E8%AE%BE%E7%BD%AE">7.3 盘前盘后触发设置</h2>
<p>本功能允许策略在每日的特定时间点执行一些常规的、非核心交易逻辑的任务。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/盘前盘后触发设置.png" />
</p>
<ul>
<li><strong>盘前任务 <code>khPreMarket</code></strong>: 勾选并设置时间（如 <code>09:25:00</code>），系统会在每个交易日的指定时间，自动调用策略代码中的 <code>khPreMarket</code> 函数。这通常用于执行开盘前的准备工作，例如：
<ul>
<li>获取当日的股票池。</li>
<li>取消所有昨日未成交的挂单。</li>
<li>重置当日的状态变量。</li>
</ul>
</li>
<li><strong>盘后任务 <code>khPostMarket</code></strong>: 勾选并设置时间（如 <code>15:05:00</code>），系统会在每个交易日的指定时间，自动调用策略代码中的 <code>khPostMarket</code> 函数。这通常用于执行收盘后的复盘和清理工作，例如：
<ul>
<li>统计当日交易情况。</li>
<li>记录当日的持仓和资产快照。</li>
<li>为第二天的交易进行数据预处理。</li>
</ul>
</li>
</ul>
<p>在下一章，我们将讲解右侧的信息反馈区，学习如何通过日志和回测报告来观察和分析我们的策略。</p>

</body>
</html>
