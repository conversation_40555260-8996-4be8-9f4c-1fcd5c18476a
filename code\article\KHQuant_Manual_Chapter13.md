# 第十三章：答疑解惑 (FAQ)

在使用任何新软件的过程中，遇到一些磕磕绊绊是难免的。本章汇集了"看海量化交易系统"用户最常遇到的问题及其解决方案，旨在为您提供一本可以快速查阅的"急救手册"。

---

### Q: MiniQMT连接不上，主界面右上角的状态指示灯是红的，怎么办？

**A:** 这是最常见的问题之一，通常按以下步骤排查即可解决：

1.  **检查MiniQMT客户端状态**：确保您的国金证券MiniQMT客户端已经**正常启动并成功登录**。如果MiniQMT本身没有登录或处于离线状态，本系统自然无法连接。
2.  **核对关键路径设置**：
    *   进入本软件的"**设置**" -> "**客户端设置**"。
    *   仔细检查"**QMT路径**"是否正确指向了MiniQMT的 `userdata_mini` 文件夹。这是一个文件夹路径，不是文件路径！例如：`C:\国金证券QMT\userdata_mini`。
    *   同时，也请检查"**miniQMT客户端路径**"是否正确指向了 `XtMiniQmt.exe` 这个**可执行文件**。
3.  **查看系统日志**：如果上述两步都确认无误，请回到主界面，仔细查看右侧"**系统日志**"面板。通常，在软件尝试连接时，那里会打印出更详细的错误信息（如"连接超时"、"路径不正确"等），这些信息是定位问题的关键线索。
4.  **重启软件**：在极少数情况下，可能是由于某些未知状态导致连接异常。可以尝试先关闭本系统，再关闭MiniQMT，然后重新按顺序（先启动并登录MiniQMT，再启动本系统）打开。

---

### Q: 回测跑起来感觉特别慢，有什么优化建议吗？

**A:** 回测速度受多种因素影响，您可以尝试从以下几个方面进行优化：

1.  **缩小回测范围**：
    *   **时间范围**：非必要情况下，避免选择长达数年的回测周期。可以先用一两年的数据进行快速验证，待策略基本成型后再进行更长时间的压力测试。
    *   **股票池**：尽量避免一次性在数千只股票上进行回测。股票池越大，每个时间点需要处理的数据量就越大，速度自然越慢。可以先在特定的指数成分股（如沪深300）或小范围的自选股中测试。
2.  **优化策略代码**：
    *   **避免在`handle_bar`中进行耗时操作**：策略的"心跳"函数 `handle_bar` 会被频繁调用，应保持其逻辑尽可能高效。避免在该函数内部进行频繁的文件读写、大量的历史数据查询（如前文所述的 `xtdata.get_market_data_ex`）或复杂的循环计算。
    *   **使用高效的数据结构和算法**：活用Pandas和NumPy的向量化计算能力，通常比手写的Python循环快得多。
3.  **利用软件设置**：
    *   在主界面的"**设置**" -> "**基本参数设置**"中，勾选"**延迟显示日志**"。这样，在策略运行期间，日志会先缓存起来，待回测结束后再一次性显示。这能有效减少因UI频繁刷新带来的性能开销，对速度提升有明显帮助。
4.  **检查硬件资源**：量化回测本质上是计算密集型和数据密集型任务。如果您的计算机CPU性能较弱、内存（RAM）较小或硬盘读写速度慢，都可能成为瓶颈。

---

### Q: 我该如何编写自己的交易策略？

**A:** 恭喜您问到点子上了！这正是本软件的核心价值所在。请您暂时放下焦急的心情，返回阅读本手册的 **第十二章《核心驱动：策略编写指南》**。那一章就是专门为您准备的，里面系统地讲解了从策略结构、数据获取到交易信号发送的每一个细节，并附有可以直接运行的完整代码示例。"磨刀不误砍柴工"，仔细阅读它，您将事半功倍。

---

### Q: 配置文件 (`.kh` 文件) 是做什么用的？

**A:** `.kh` 文件是本软件的**工程配置文件**。它以人类可读的JSON格式，保存了您在图形界面上所做的**所有参数设置**。

这包括：
*   您选择的策略文件路径、运行模式。
*   回测的起止时间、手续费、滑点。
*   选择的数据周期、复权方式。
*   股票池列表。
*   触发方式配置... 等等。

它的主要用途是**方便您保存和恢复工作场景**。当您调试好一套复杂的参数配置后，可以通过顶部工具栏的"**保存配置**"或"**配置另存为**"将其存为一个 `.kh` 文件。下次需要使用这套配置时，只需通过"**加载配置**"按钮选择对应的 `.kh` 文件，所有UI界面上的设置就会瞬间恢复到您保存时的状态，无需再手动一个个重新设置，极大提高了工作效率。

---

### Q: 用这个系统做实盘交易，资金安全吗？

**A:** 这是一个非常重要的问题。请您放心，**您的资金安全由券商和其提供的MiniQMT系统来保障**。

可以这样理解它们之间的关系：
*   **资金和账户**: 完全由您开户的券商（如国金证券）负责管理。
*   **交易执行**: 所有真实的交易指令，最终都由券商的 **MiniQMT客户端** 来执行。
*   **看海量化系统 (KHQuant)**: 本系统扮演的是一个"**智能遥控器**"的角色。它根据您的策略逻辑，向MiniQMT客户端"按下"买入或卖出的按钮。本系统**本身不直接触碰、处理或保管您的任何资金和账户密码**。

因此，只要您确保自己的计算机环境安全，不泄露QMT的账户和交易密码，您的资金安全就是有保障的。本系统作为一款开源的、运行在您本地的策略研究和辅助交易工具，其代码逻辑是公开透明的，您可以审查每一行代码，确保它只在做分内之事。 