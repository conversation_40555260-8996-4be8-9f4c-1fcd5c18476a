# coding: utf-8
from typing import Dict, List
import numpy as np
from xtquant import xtdata
import datetime
import logging
import os
import json
# 从 khQTTools 导入信号生成等辅助函数
from khQTTools import generate_signal, calculate_max_buy_volume

# 全局变量
position = {}  # 仅用于记录操作
params = {}
risk_params = {}
stock_list = []

def init(stocks=None, data=None):
    """策略初始化"""
    global position, params, risk_params
  
    # 初始化持仓记录
    position = {}
    stock_code = stocks[0] if stocks else '002945.SZ'
  
    # 初始化策略参数
    params = {
        "short_ma_period": 5,  # 短期均线周期
        "long_ma_period": 20,  # 长期均线周期
        "stock_code": stock_code  # 交易标的
    }
  
    # 初始化风控参数
    risk_params = {
        "max_position": 1.0  # 最大持仓比例（全仓）
    }
  
    print(f"策略初始化完成，交易标的：{stock_code}")

def calculate_ma(code: str, short_period: int, long_period: int, current_date: str = None) -> tuple:
    """计算移动平均线"""
    try:
        # 获取历史收盘价数据，以当前日期为结束日期
        history_data = xtdata.get_market_data(
            field_list=["close"],
            stock_list=[code],
            period="1d",
            start_time="20240101",  # 使用较早的起始日期
            end_time=current_date,  # 使用当前回测日期作为结束日期
            dividend_type='front'
        )
      
        # 计算均线
        closes = history_data['close'].values[0]
  
        # 排除最后一个数据点（当日数据），只使用之前的数据
        closes = closes[:-1]
  
        # 计算均线，使用最后需要的几个数据点
        ma_long = round(np.mean(closes[-long_period:]), 2)
        ma_short = round(np.mean(closes[-short_period:]), 2)
          
        return ma_short, ma_long
  
    except Exception as e:
        logging.error(f"计算均线时发生错误: {str(e)}")
        return None, None

def khHandlebar(data: Dict) -> List[Dict]:
    """策略主逻辑，在每个K线或Tick数据到来时执行"""
    signals = []
  
    # 获取当前时间信息
    current_time = data.get("__current_time__", {})
    current_date_str = current_time.get("date", "")  
  
    # 获取股票代码
    stock_code = params["stock_code"]
  
    # 获取当前价格
    stock_data = data.get(stock_code, {})
    current_price = stock_data.get("close", 0)
  
    # 格式化日期用于计算均线
    date_parts = current_date_str.split("-")
    current_date_formatted = f"{date_parts[0]}{date_parts[1]}{date_parts[2]}"
  
    # 计算均线
    ma_short, ma_long = calculate_ma(
        stock_code, 
        params["short_ma_period"], 
        params["long_ma_period"],
        current_date_formatted
    )
  
    logging.info(f"计算结果 - 短期均线: {ma_short:.2f}, 长期均线: {ma_long:.2f}")
  
    # 获取持仓信息
    positions_info = data.get("__positions__", {})
    has_position = stock_code in positions_info and positions_info[stock_code].get("volume", 0) > 0
  
    # 交易逻辑: 使用 generate_signal 生成信号
    if ma_short > ma_long and not has_position:
        # 金叉且无持仓，生成全仓买入信号
        buy_reason = f"5日线({ma_short:.2f}) 上穿 20日线({ma_long:.2f})，全仓买入"
        signals = generate_signal(data, stock_code, current_price, 1.0, 'buy', buy_reason)

    elif ma_short < ma_long and has_position:
        # 死叉且有持仓，生成全仓卖出信号
        sell_reason = f"5日线({ma_short:.2f}) 下穿 20日线({ma_long:.2f})，全仓卖出"
        signals = generate_signal(data, stock_code, current_price, 1.0, 'sell', sell_reason)

    return signals

# khPreMarket 和 khPostMarket 函数省略，本次策略未使用