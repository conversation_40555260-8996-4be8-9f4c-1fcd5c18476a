# 涨幅监控策略（中证500版本）- 高性能优化版
# 当股票当日涨幅超过9%时买入，第二天开盘卖出

from xtquant import xtdata
from khQTTools import generate_signal
import logging
import os
import time
from datetime import datetime
from collections import defaultdict

# 配置日志系统
LOGS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
os.makedirs(LOGS_DIR, exist_ok=True)

logging.basicConfig(
    filename=os.path.join(LOGS_DIR, 'strategy_performance.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filemode='a'
)

# 性能监控装饰器
def performance_monitor(func_name):
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            # 记录到日志和控制台
            log_msg = f"⏱️ {func_name} 执行时间: {execution_time:.2f}ms"
            logging.info(log_msg)
            print(log_msg)
            
            # 记录到全局统计
            g_performance_stats[func_name].append(execution_time)
            
            return result
        return wrapper
    return decorator

# 全局变量
g_today_bought = set()     # 今日买入的股票
g_yesterday_bought = set() # 昨日买入的股票，今日开盘卖出
g_is_first_bar = True      # 是否是当天第一个bar
g_trigger_prices = {}      # 存储触发价格的字典
g_max_positions = 5        # 最大持仓数量
g_performance_stats = defaultdict(list)  # 性能统计
g_api_call_count = 0       # API调用计数器

# 🚀 新增优化变量
g_monitored_stocks = set()    # 监控的股票池（只处理这些股票）
g_price_cache = {}           # 价格缓存
g_last_check_time = 0        # 上次检查时间
g_skip_counter = defaultdict(int)  # 跳过计数器
g_call_count = 0             # 调用计数器

def log_performance_summary():
    """输出性能统计摘要"""
    print("\n" + "="*60)
    print("📊 策略性能统计摘要")
    print("="*60)
    
    for func_name, times in g_performance_stats.items():
        if times:
            avg_time = sum(times) / len(times)
            max_time = max(times)
            min_time = min(times)
            total_time = sum(times)
            call_count = len(times)
            
            print(f"🔧 {func_name}:")
            print(f"   └─ 调用次数: {call_count}")
            print(f"   └─ 总耗时: {total_time:.2f}ms")
            print(f"   └─ 平均耗时: {avg_time:.2f}ms")
            print(f"   └─ 最大耗时: {max_time:.2f}ms")
            print(f"   └─ 最小耗时: {min_time:.2f}ms")
            print()
    
    print(f"🌐 API调用总次数: {g_api_call_count}")
    print(f"🔄 主策略函数调用次数: {g_call_count}")
    print("="*60)

@performance_monitor("策略初始化")
def init(stock_list, context):
    """策略初始化函数，系统启动时会调用此函数"""
    global g_today_bought, g_yesterday_bought, g_is_first_bar, g_trigger_prices, g_max_positions, g_monitored_stocks
    
    start_time = time.time()
    
    # 记录日志
    logging.info('策略开始运行，初始化函数全局只运行一次')
    
    # 获取框架实例
    framework = context.get("__framework__")
    if framework and hasattr(framework, 'trader_callback') and hasattr(framework.trader_callback, 'gui'):
        framework.trader_callback.gui.log_message(f'策略开始运行，初始化函数全局只运行一次', "INFO")
        framework.trader_callback.gui.log_message(f'设置监控股票数量: {len(stock_list)} 只', "INFO")
    
    # 初始化全局变量
    init_start = time.time()
    g_today_bought = set()
    g_yesterday_bought = set()
    g_is_first_bar = True
    g_trigger_prices = {}
    
    # 🚀 优化：预设监控股票池
    g_monitored_stocks = set(stock_list) if stock_list else set()
    
    init_time = (time.time() - init_start) * 1000
    print(f"   └─ 变量初始化耗时: {init_time:.2f}ms")
    print(f"   └─ 监控股票池大小: {len(g_monitored_stocks)}")
    
    # 预加载关键历史数据
    preload_start = time.time()
    try:
        batch_size = 50  # 每次处理50只股票
        for i in range(0, len(stock_list), batch_size):
            batch_stocks = stock_list[i:i+batch_size]
            
            # 批量获取历史数据以提高效率
            xtdata.get_market_data(
                field_list=['open', 'close'], 
                stock_list=batch_stocks, 
                period='1d',
                count=5
            )
            global g_api_call_count
            g_api_call_count += 1
            
    except Exception as e:
        logging.error(f"预加载历史数据时出错: {str(e)}")
    
    preload_time = (time.time() - preload_start) * 1000
    print(f"   └─ 数据预加载耗时: {preload_time:.2f}ms")
    
    total_time = (time.time() - start_time) * 1000
    print(f"   └─ 初始化总耗时: {total_time:.2f}ms")
    
    logging.info("初始化完成")

@performance_monitor("盘前回调")
def khPreMarket(data):
    """盘前回调函数，计算当日触发价格"""
    global g_today_bought, g_yesterday_bought, g_is_first_bar, g_trigger_prices, g_api_call_count, g_monitored_stocks
    
    start_time = time.time()
    
    # 获取当前日期并转换为API所需格式
    current_time = data["__current_time__"]
    date = current_time["date"]
    date_yyyymmdd = date.replace('-', '')
    
    # 获取框架实例
    framework = data.get("__framework__")
    gui = None
    if framework and hasattr(framework, 'trader_callback') and hasattr(framework.trader_callback, 'gui'):
        gui = framework.trader_callback.gui
        gui.log_message(f'=== 交易日 {date} 盘前运行 ===', "INFO")
    
    # 重置每日状态
    reset_start = time.time()
    g_today_bought = set()
    g_is_first_bar = True
    g_trigger_prices = {}
    reset_time = (time.time() - reset_start) * 1000
    print(f"   └─ 状态重置耗时: {reset_time:.2f}ms")
    
    # 🚀 优化：使用预设的监控股票池
    stock_list_start = time.time()
    if not g_monitored_stocks:
        # 如果没有预设股票池，才从配置读取
        try:
            if framework and hasattr(framework, 'config'):
                stock_list_file = framework.config.config_dict.get("data", {}).get("stock_list_file", "")
                if stock_list_file and os.path.exists(stock_list_file):
                    with open(stock_list_file, 'r', encoding='utf-8') as f:
                        stock_list = [line.strip() for line in f if line.strip()]
                        g_monitored_stocks = set(stock_list)
            
            # 如果无法从配置获取，尝试从持仓中获取
            if not g_monitored_stocks:
                positions = data.get("__positions__", {})
                g_monitored_stocks = set(positions.keys())
        except Exception as e:
            logging.error(f"获取股票列表失败: {str(e)}")
    
    stock_list_time = (time.time() - stock_list_start) * 1000
    print(f"   └─ 股票列表获取耗时: {stock_list_time:.2f}ms (股票数量: {len(g_monitored_stocks)})")
    
    # 计算所有股票的触发价格
    if g_monitored_stocks:
        trigger_calc_start = time.time()
        try:
            # 批量获取前一日收盘价并计算触发价
            api_start = time.time()
            stock_list = list(g_monitored_stocks)
            hist_data = xtdata.get_market_data_ex(
                field_list=['close'],
                stock_list=stock_list,
                period='1d',
                start_time=str(int(date_yyyymmdd) - 1),
                end_time=str(int(date_yyyymmdd) - 1),
                dividend_type='none'
            )
            g_api_call_count += 1
            api_time = (time.time() - api_start) * 1000
            print(f"   └─ 批量API调用耗时: {api_time:.2f}ms")
            
            # 处理每只股票的数据
            process_start = time.time()
            processed_count = 0
            for stock in stock_list:
                if stock in hist_data and len(hist_data[stock]['close']) > 0:
                    prev_close = hist_data[stock]['close'].iloc[0]
                    if prev_close > 0:
                        g_trigger_prices[stock] = prev_close * 1.09
                        processed_count += 1
            
            process_time = (time.time() - process_start) * 1000
            print(f"   └─ 触发价格计算耗时: {process_time:.2f}ms (处理股票: {processed_count})")
            
        except Exception as e:
            logging.error(f"批量计算触发价格时出错: {str(e)}")
        
        trigger_calc_time = (time.time() - trigger_calc_start) * 1000
        print(f"   └─ 触发价格总耗时: {trigger_calc_time:.2f}ms")
    
    # 确保昨日买入的股票在今日卖出
    position_start = time.time()
    positions = data.get("__positions__", {})
    g_yesterday_bought.update(positions.keys())
    position_time = (time.time() - position_start) * 1000
    print(f"   └─ 持仓处理耗时: {position_time:.2f}ms")
    
    total_time = (time.time() - start_time) * 1000
    print(f"   └─ 盘前回调总耗时: {total_time:.2f}ms")
    
    return []

@performance_monitor("主策略函数")
def khHandlebar(data):
    """策略主函数，处理实时行情并生成交易信号 - 高性能优化版"""
    global g_today_bought, g_yesterday_bought, g_is_first_bar, g_trigger_prices, g_max_positions
    global g_api_call_count, g_monitored_stocks, g_price_cache, g_last_check_time, g_call_count
    
    g_call_count += 1
    start_time = time.time()
    signals = []
    
    # 🚀 优化1：早期退出 - 检查持仓限制
    positions = data.get("__positions__", {})
    current_positions_count = len(positions)
    
    if current_positions_count >= g_max_positions:
        print(f"   🚀 早期退出：已达最大持仓 {current_positions_count}/{g_max_positions}")
        return signals
    
    # 获取当前时间和基础信息
    current_time = data["__current_time__"]
    date = current_time["date"]
    date_yyyymmdd = date.replace('-', '')
    
    # 🚀 优化2：卖出处理优化
    if g_is_first_bar:
        sell_start = time.time()
        sell_count = 0
        for stock in g_yesterday_bought:
            if stock in positions:
                # 获取当前价格
                current_price = positions[stock].get("close", 0)
                if current_price <= 0 and stock in data:
                    current_price = data[stock].get("close", 0)
                
                # 生成卖出信号
                if current_price > 0:
                    sell_signals = generate_signal(data, stock, current_price, 1.0, "sell", "涨停后第二天卖出")
                    signals.extend(sell_signals)
                    sell_count += 1
        
        g_is_first_bar = False
        sell_time = (time.time() - sell_start) * 1000
        print(f"   🚀 卖出处理耗时: {sell_time:.2f}ms (卖出信号: {sell_count})")
        
        # 如果有卖出信号，先返回执行
        if signals:
            total_time = (time.time() - start_time) * 1000
            print(f"   🚀 主策略函数总耗时: {total_time:.2f}ms (仅卖出)")
            return signals
    
    # 🚀 优化3：预筛选候选股票 - 只处理监控池中的股票
    data_prep_start = time.time()
    
    # 只获取监控池中的股票代码
    if g_monitored_stocks:
        candidate_stocks = [code for code in g_monitored_stocks 
                          if code in data and not code.startswith('__')]
    else:
        # 如果没有监控池，使用原逻辑
        candidate_stocks = [key for key in data.keys() if not key.startswith('__')]
    
    data_prep_time = (time.time() - data_prep_start) * 1000
    print(f"   🚀 候选股票筛选耗时: {data_prep_time:.2f}ms (候选数: {len(candidate_stocks)})")
    
    # 🚀 优化4：批量提取价格数据
    price_batch_start = time.time()
    prices = {}
    valid_candidates = []
    
    for stock_code in candidate_stocks:
        # 跳过已买入或已持有的股票
        if stock_code in g_today_bought or stock_code in positions:
            continue
            
        current_price = data[stock_code].get("close", 0)
        if current_price > 0:
            prices[stock_code] = current_price
            valid_candidates.append(stock_code)
    
    price_batch_time = (time.time() - price_batch_start) * 1000
    print(f"   🚀 批量价格提取耗时: {price_batch_time:.2f}ms (有效候选: {len(valid_candidates)})")
    
    # 🚀 优化5：早期退出 - 如果没有有效候选股票
    if not valid_candidates:
        total_time = (time.time() - start_time) * 1000
        print(f"   🚀 主策略函数总耗时: {total_time:.2f}ms (无有效候选)")
        return signals
    
    # 获取账户信息
    account = data.get("__account__", {})
    available_cash = account.get("cash", 0)
    position_value = available_cash / g_max_positions if g_max_positions > 0 else 0
    
    # 🚀 优化6：高效循环处理
    buy_check_start = time.time()
    buy_signals_generated = 0
    trigger_price_hits = 0
    
    for stock_code in valid_candidates:
        current_price = prices[stock_code]
        
        # 检查触发价格
        trigger_price = g_trigger_prices.get(stock_code)
        if not trigger_price:
            # 如果没有触发价格，跳过（正常情况下不应该发生，因为盘前已经计算了）
            continue
        
        # 检查是否达到触发条件
        if current_price >= trigger_price:
            trigger_price_hits += 1
            
            # 计算可买股数（整数手）
            shares_to_buy = int(position_value / current_price / 100) * 100
            
            # 确保至少买一手(100股)
            if shares_to_buy >= 100:
                # 生成买入信号
                buy_signals = generate_signal(data, stock_code, current_price, shares_to_buy, "buy", "当日涨幅超过9%买入")
                signals.extend(buy_signals)
                buy_signals_generated += 1
                
                # 添加到今日买入记录
                g_today_bought.add(stock_code)
                
                # 🚀 优化7：达到最大持仓立即退出
                if len(g_today_bought) + current_positions_count >= g_max_positions:
                    print(f"   🚀 达到最大持仓限制，提前退出")
                    break
    
    buy_check_time = (time.time() - buy_check_start) * 1000
    
    total_time = (time.time() - start_time) * 1000
    print(f"   🚀 买入检查耗时: {buy_check_time:.2f}ms")
    print(f"      ├─ 触发价格命中: {trigger_price_hits}")
    print(f"      └─ 生成买入信号: {buy_signals_generated}")
    print(f"   🚀 主策略函数总耗时: {total_time:.2f}ms (调用#{g_call_count})")
    
    return signals

@performance_monitor("盘后回调")
def khPostMarket(data):
    """盘后回调函数，记录今日买入的股票"""
    global g_yesterday_bought, g_today_bought
    
    start_time = time.time()
    
    # 获取当前时间
    current_time = data["__current_time__"]
    date_time = current_time["datetime"]
    
    # 更新昨日买入记录，用于明天开盘卖出
    update_start = time.time()
    g_yesterday_bought = g_today_bought.copy()
    update_time = (time.time() - update_start) * 1000
    print(f"   └─ 记录更新耗时: {update_time:.2f}ms")
    
    # 记录日志
    log_start = time.time()
    if g_today_bought:
        logging.info(f"今日买入股票: {list(g_today_bought)}")
        logging.info(f"明日开盘将卖出股票: {list(g_yesterday_bought)}")
    else:
        logging.info(f"今日未买入股票")
    
    # 获取账户信息
    account = data.get("__account__", {})
    available_cash = account.get("cash", 0)
    total_asset = account.get("total_asset", 0)
    
    logging.info(f"=== 交易日 {date_time} 盘后运行 ===")
    logging.info(f"当前资金: {available_cash:.2f}, 总资产: {total_asset:.2f}")
    logging.info(f"====================")
    
    log_time = (time.time() - log_start) * 1000
    print(f"   └─ 日志记录耗时: {log_time:.2f}ms")
    
    # 输出性能统计摘要
    log_performance_summary()
    
    total_time = (time.time() - start_time) * 1000
    print(f"   └─ 盘后回调总耗时: {total_time:.2f}ms")
    
    return [] 