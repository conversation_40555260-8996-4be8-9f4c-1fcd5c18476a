# 第五章：主界面巡览：功能区详解

欢迎来到"看海量化交易系统"（KHQuant）的主控界面。本章将对主界面进行一次全面的巡览，概要介绍各个功能区的布局与作用。这是一个整体性的介绍，对于左侧的核心配置区、中间的运行驱动区以及右侧的信息反馈区，我们将在随后的第六、七、八章中进行更深入的拆解说明。熟悉主界面的整体布局，是高效施展策略的第一步。

> **重要提示：关于运行模式**
>
> 当前版本的"看海量化交易系统"专注于提供强大、易用的 **回测 (Backtesting)** 功能，旨在帮助用户在历史数据上验证和优化自己的交易策略。因此，软件界面和功能均围绕回测模式进行设计。
>
> **实盘交易 (Live Trading) 功能目前暂不支持。**

---

## 5.1 整体布局一览

首次打开软件，会看到一个精心组织的三栏式布局界面。这种设计的目的是将策略配置、运行监控和状态反馈等核心操作流程清晰地分隔开，从而一目了然地找到所需功能。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/主界面概览.png" alt="主界面概览" width="100%" />
</p>

主界面主要由以下几个部分构成：

* **顶部工具栏**: 位于界面最上方，集成了最高频使用的全局操作，如配置文件的加载/保存、策略的启停、以及打开辅助工具等。
* **左侧面板 (核心配置区)**: 这里是定义策略行为的核心区域。可在此指定策略文件、设置回测参数、管理股票池等。
* **中间面板 (运行驱动区)**: 该区域负责定义策略的"心跳"—即由什么事件来驱动策略逻辑的执行。可设置触发方式、配置盘前盘后任务，并查看账户的资金与持仓状况。
* **右侧面板 (信息反馈区)**: 这是观察系统运行状态的窗口。系统日志、策略中打印的信息、交易委托与成交回报、错误警报等都会在这里显示。
* **底部状态栏**: 位于界面最下方，提供实时的操作状态反馈和回测进度。

---

## 5.2 工具栏按钮说明

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/07/工具栏.png" alt="工具栏" width="100%" />
</p>

顶部工具栏是进行快速操作的捷径，以下是每个按钮的功能详解：

* **加载配置**: 点击后加载一个之前保存的 `.kh` 配置文件，快速恢复所有参数设置。
* **保存配置**: 将当前所有参数设置保存到当前加载的 `.kh` 文件中。
* **配置另存为**: 将当前所有设置保存为一个新的 `.kh` 文件。
* **开始运行**: 根据当前配置，启动策略回测。
* **停止运行**: 手动停止当前正在运行的策略。
* **本地数据管理**: 打开本地数据管理器，用于补充、查看和管理本地存储的股票数据。
* **定时补充数据**: 打开定时数据补充任务管理器，可以设置自动化的数据更新计划。
* **CSV数据管理**: 打开CSV数据下载、清洗和管理界面，用于导出数据供外部分析使用。
* **设置**: 打开"软件设置"对话框，配置MiniQMT路径等全局参数。
* **MiniQMT状态指示灯**: 显示与MiniQMT的连接状态（🟢已连接 / 🔴未连接）。
* **帮助 (?)**: 打开在线教程文档。

---

## 5.3 左侧核心配置区

这是进行策略回测设置的起点，所有参数都在此集中配置。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/左侧面板.png" alt="左侧面板" width="50%" />
</p>

* **策略配置**:

  * **策略文件**: 点击"选择策略文件"按钮，选择编写的Python策略脚本 (`.py`文件)。
  * **运行模式**: 当前版本专注于 **回测** 功能。此选项固定为回测模式，用于在历史数据上验证策略表现。
* **回测参数**:

  * **基准合约**: 设置用于计算Alpha、Beta等相对表现指标的业绩基准，例如 `sh.000300`。
  * **交易成本设置**: 精确模拟真实交易成本。
    * **最低佣金(元)**: 单笔交易佣金的最低收费。
    * **佣金比例**: 按成交金额计算的佣金费率。
    * **卖出印花税**: 按卖出金额计算的印花税率。
    * **流量费(元/笔)**: 部分券商收取的额外通讯费用。
    * **滑点类型/滑点值**: 设置买卖时价格的滑点，可选"按最小变动价位数"或"按成交额比例"。
  * **回测时间设置**: 通过"开始日期"和"结束日期"选择器，设定回测的时间区间。
* **数据设置**:

  * **复权方式**: 选择K线数据的复权类型，如"不复权"、"前复权"、"等比前复权"。
  * **周期类型**: 选择策略运行所依赖的数据周期，如 `tick`, `1m`, `5m`, `1d`。
  * **数据字段**: 根据所选周期，勾选策略在 `handle_bar` 函数中需要用到的具体数据字段（如开盘价、收盘价、成交量等）。
* **股票池设置**:

  * **常用指数**: 快速勾选A股主要指数成分股作为股票池。
  * **自选清单**: 使用自选股列表。
  * **手动管理**: 在下方的表格中直接添加或删除股票代码，或通过右键菜单导入/清空列表。

---

## 5.4 中间运行驱动区

本区域负责定义策略的触发机制和账户信息。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/中间面板.png" alt="中间面板" width="50%" />
</p>

* **触发方式设置**:

  * **触发类型**: 定义策略核心逻辑 (`handle_bar`函数) 的执行频率。
    * **Tick触发**: 每个Tick数据到达时都执行一次策略。
    * **K线触发**: 在每个K线周期（如1分钟、5分钟）形成时执行一次策略。
    * **自定义定时触发**: 按设定的特定时间点列表来执行策略。
* **账户信息**:

  * **虚拟账户**: 在回测模式下，可在此设置策略的"初始资金"和"最小交易量"。
* **盘前盘后触发设置**:

  * 勾选并设置时间，可以在每日开盘前或收盘后，自动执行策略中相应的 `khPreMarket` 或 `khPostMarket` 函数，用于执行盘前准备或盘后复盘等任务。

---

## 5.5 右侧信息反馈区

这里是观察策略运行过程和结果的主要窗口。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/右侧面板.png" alt="右侧面板" width="50%" />
</p>

* **系统日志**:

  * 一个实时滚动的文本框，显示软件的运行状态、策略中 `print()` 的内容、交易委托和成交的详细回报、以及任何错误或警告信息。不同级别的日志会用不同颜色标记，方便快速识别。
* **日志操作**:

  * **日志类型过滤**: 通过勾选 `DEBUG`, `INFO`, `WARNING`, `ERROR`, `TRADE` 等复选框，可以筛选想看的日志级别。
  * **清空日志**: 清除当前日志显示。
  * **保存日志**: 将当前显示的日志内容导出为文本文件。
  * **测试日志**: 点击后会生成一些各种级别的测试日志，用于检查显示是否正常。
  * **打开回测指标**: **回测结束后，此按钮会变为可用状态**。点击它，即可打开详细的回测报告窗口，对策略绩效进行全面复盘。

---

## 5.6 底部状态栏

界面最底部的状态栏提供实时的上下文信息。

* **左侧：当前状态文本**: 用简短文字描述软件正在进行的操作（如"准备就绪", "策略运行中..."）。
* **右侧：进度条**: 在回测进行时，会激活并直观地展示回测的完成进度。

至此，对"看海量化交易系统"主界面的概览就完成了。在下一章，我们将深入探索左侧的核心配置面板，学习如何为策略配置各项参数。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/底部状态栏.png" alt="底部状态栏" width="100%" />
</p>
