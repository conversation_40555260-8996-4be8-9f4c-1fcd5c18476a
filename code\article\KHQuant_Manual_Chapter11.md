# 第十一章：个性化配置：软件设置详解

"看海量化交易系统"提供了一个独立的"软件设置"对话框，用于管理那些独立于具体策略、具有全局性或不常变动的参数。理解并正确配置这些选项，将有助于提升使用体验，并确保软件与关键依赖（如MiniQMT）的顺畅通信。

---

## 11.1 如何打开"设置"对话框？

在主界面顶部工具栏，点击 **设置** 按钮（齿轮图标⚙️），即可打开"软件设置"对话框。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/软件设置.png" alt="软件设置对话框" />
</p>

---

## 11.2 "基本参数设置"与"账户设置"

* **无风险收益率 (Risk-Free Rate)**:

  * **功能**: 设定一个年化的无风险收益率，主要用于计算夏普比率、索提诺比率等风险调整后收益指标。
  * **如何设置**:在实际研究中，通常使用十年期国债收益率作为无风险利率的代理。您可以根据当前市场情况进行调整，可点击此处查看：[<ins>中国十年期国债收益率行情</ins>](https://cn.investing.com/rates-bonds/china-10-year-bond-yield)。
* **延迟显示日志 (Delay Log Display)**:

  * **功能**: 勾选此项后，在策略高速回测期间，系统会将所有日志先缓存起来，待回测结束后再一次性显示在右侧日志面板。
  * **优势**: 对于运行时间长、日志输出量大的回测，开启此功能可以避免因界面频繁刷新而导致的UI卡顿，从而显著提升回测性能和界面流畅度。
* **初始化行情数据 (Initialize Market Data)**:

  * **功能**: 用于控制每次**启动回测时**，是否自动检查并补充本地缺失的历史数据。
  * **工作原理**: 
    > - **勾选时**: 回测启动前会自动检测策略所需的历史数据，如发现缺失会自动从服务器下载补充
    > - **不勾选时**: 直接使用本地已有数据进行回测，不会自动下载缺失数据
  * **性能影响**: 
    > - **优点**: 能够自动补充缺失的数据，确保回测数据完整性
    > - **缺点**: 会显著拖慢回测启动速度，特别是在数据缺失较多时
  * **使用建议**: 
    > - **推荐做法**: 保持此选项**不勾选**，在回测前先通过"数据中心"的"数据补充"功能手动补充所需数据
    > - **适用场景**: 仅在偶尔需要快速测试且不介意等待时间的情况下临时勾选
    > - **最佳实践**: 养成定期使用数据中心补充数据的习惯，这样既能保证数据完整，又能获得最佳的回测性能
* **账户设置 (Account Settings)**:

  * **功能**: 此为实盘/模拟盘功能预用选项，用于配置交易账户信息。
  * **说明**: **由于当前版本专注于回测功能，此处的账户设置不会对回测产生影响，暂时无需理会。**

---

## 11.3 "股票列表管理"

* **更新成分股列表 (Update Constituent Stocks)**:
  * **功能**: 点击此按钮，系统会连接到网络，下载并更新本地存储的A股主要指数（如沪深300、中证500等）的最新成分股列表文件。这些文件保存在软件根目录下的 `data` 文件夹中。
  * **使用建议**: 当您发现指数成分股发生较大调整，或者长时间未更新时，可以执行此操作。过程需要一定时间，请耐心等待，无需频繁点击。

---

## 11.4 "客户端设置"

这是整个设置对话框中**至关重要**的部分，它负责建立本软件与MiniQMT之间的连接。

* **miniQMT客户端路径 (miniQMT Client Path)**:

  * **功能**: 指向您电脑上MiniQMT客户端的主程序文件 (`XtItClient.exe`)。
  * **如何设置**: 点击 **浏览...** 按钮，找到您的MiniQMT安装目录，并进入 `bin.x64` 文件夹，选择 `XtItClient.exe` 文件。
* **QMT路径 (QMT Path)**:

  * **功能**: 指向MiniQMT的用户数据文件夹 (`userdata_mini`)。框架需要从此文件夹读取账户配置、日志等信息。
  * **如何设置**: 点击 **浏览...** 按钮，找到您的MiniQMT安装目录，并选择 `userdata_mini` 文件夹。

---

## 11.5 "版本信息"与"问题反馈"

* **版本信息 (Version Info)**:

  * **功能**: 此区域会清晰地展示您当前使用的"看海量化交易系统"的**版本号**、**构建日期**和**更新通道**（如`stable`稳定版）。当您需要反馈问题或寻求帮助时，提供这些版本信息将非常有帮助。
* **反馈问题 (Feedback)**:

  * **功能**: 点击此按钮，会直接跳转到作者的官方在线反馈页面。如果您在使用中遇到任何Bug、有功能建议或想进行交流，欢迎通过此渠道联系。

---

完成所有设置后，请务必点击右下角的 **保存设置** 按钮，您的所有更改才能生效。
