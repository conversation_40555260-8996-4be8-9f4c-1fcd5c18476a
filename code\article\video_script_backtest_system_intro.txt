普通散户如何零门槛研究量化？

核心在于两点：一是稳定、免费的数据和交易接口，二就是一个真正属于我们自己、开源且强大的回测系统。

大家好，我是Mr.看海。

上一期视频，我们介绍了使用miniQMT作为免费数据和交易接口的方式，并基于此构建了底层回测框架khQuant。

今天，我将介绍我基于自研khQuant搭建的看海量化回测平台，看一看它是如何将复杂的量化回测过程，变得简单直观、触手可及的。

那回测到底是什么呢？简单来说，就是让你的交易策略，在过去的历史数据中完整地跑一遍，看看它在当时的市场环境下，究竟会有怎样的表现。回测的关键作用，就是在投入真金白银之前，对策略进行一次严格的压力测试。它能帮我们量化收益风险，发现策略优劣，是迭代优化的必经之路。

整个系统，主要由四个精心设计的核心部分组成：包括进行所有操作的主界面，负责准备历史数据的数据工具，用于分析结果的回测报告，以及进行全局配置的设置中心。

首先是主界面，我们所有操作的指挥中心。它的设计高度集成且流程化，你可以在这里完成从回测参数、股票池到策略触发方式的全部配置，可以定义盘前盘后的触发任务。这背后是我"以配置为中心"的设计思路。复杂的量化研究常会陷入脚本混乱、参数不明、结果无法复现的困境。为此，我将每一次回测都定义为一个严肃的工程，其核心便是 .kh 配置文件。它将策略、参数、触发规则、本金等所有回测要素固化于一处，确保每一次实验都有序、严谨且精确可复现。

第二个是数据工具，我们回测所需数据的来源。它的特点是双重机制，兼顾效率与灵活。我把它设计成了"数据补充"和"数据下载"两种模式。数据补充，是专门为本平台内部回测服务的，它将数据写入MiniQMT的本地数据库，追求的是极致的回测验证效率。而数据下载，则是将数据保存为通用的CSV文件。这种方式，为你进行跨平台研究，或者开展需要外部模型训练的策略，提供了最大的灵活性。

第三个是回测报告，我们策略表现的量化总结。它的特点是信息全面且高度交互。它不仅提供超过二十项专业绩效指标，更把资金曲线、回撤、每日盈亏和买卖点都整合到一张图里。你可以深入探索图表，从宏观到微观，全方位地审视策略的表现。

最后，是设置界面，负责系统的全局配置。它的特点是全局掌控。这里管理着那些不常变动但至关重要的参数，比如连接MiniQMT的关键路径，确保了整个系统的稳定和专业。

好了，对整体架构有了了解之后，我们直接进入实战演练，通过一个MACD策略，完整地走一遍回测流程。

点击加载配置按钮，选择我们准备好的名为demoMACD的kh工程文件。

大家看，一瞬间，界面上所有的参数，从策略文件、回测周期到股票池，再到触发时机、初始资金，都自动填充好了。这种kh工程文件，就是我们整个工作台的一键存档，方便随时调用和分享。

有了策略，还需要准备历史数据。点击数据按钮，打开数据中心。

回测时，我们用的是数据补充功能。它的作用，是把我们需要的数据，下载并存入MiniQMT内部高效的二进制数据库，目的只有一个，就是快，让回测的读取速度达到极致。

我们的MACD策略，既需要分钟线来判断买卖点，也需要日线来计算长期参数。所以，我们分别勾选1分钟和1日周期，设置好股票和时间，点击补充数据。

等进度条走完，数据就准备好了。这个过程，只需要做一次。之后只要回测时间范围不变，就可以一直复用这些本地数据，大大提升效率。

好，万事俱备。现在，让我们按下开始运行按钮。

看右边的日志区，系统开始工作了。每一条日志，都代表着程序的一次运行记录：初始化策略、订阅数据、触发交易信号、委托下单、成交回报等等，所有细节都清清楚楚。

同时，看底部的进度条，它告诉我们回测完成了百分之几，整个过程，尽在掌握。

进度条走到百分之百，回测结束，一份详尽的报告会自动弹出。这就是对我们策略表现的最终检验。

这份报告，浓缩了策略的全部信息。

最上方，是核心的资金曲线。蓝线是我们的策略，橙线是基准指数。策略有没有跑赢大盘，一目了然。鼠标悬浮在上面，还能看到每一天的详细收益和回撤数据。

下面是更详细的分析。

在基本信息里，有二十多个关键绩效指标：年化收益、最大回撤、夏普比率等等，这些都是评价一个策略好坏的专业量尺。

交易记录，则列出了每一笔买卖的流水，方便我们复盘，检查策略的行为是否符合预期。

日收益表，是账户的每日快照，记录了每天的资产变化。

还有绩效分析，它通过收益分布图和月度热力图，能帮助我们深挖策略的内在性格——它是在所有月份都稳定表现，还是只在某些特定行情下有效？

从加载配置、到准备数据，再到执行回测、分析报告，整个流程下来，大家可以看到，我努力的方向，就是把复杂的东西留在幕后，把简洁的操作呈现在台前。

你不需要成为一个编程高手，通过这样一套图形化的工具，就可以把自己的交易想法，快速地进行量化和系统性的验证。

当然，一个好的回测系统，只是量化交易的第一步。如何写出一个真正有效的策略，才是我们最终的追求。

在下一期视频中，我将带领大家深入策略文件的内部，详细讲解策略编写的规范和API，真正开始我们自己的创造之旅。

我开发的这套看海量化交易系统将免费开源。系统已进入内测，感兴趣的朋友请一键三连，点个关注，第一时间获取项目进展。你的支持是我最大的动力！