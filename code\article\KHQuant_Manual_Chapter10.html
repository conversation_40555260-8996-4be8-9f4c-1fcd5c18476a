<!DOCTYPE html>
<html>
<head>
<title>KHQuant_Manual_Chapter10.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E7%AC%AC%E5%8D%81%E7%AB%A0%E6%95%B0%E6%8D%AE%E6%A8%A1%E5%9D%97%E5%9B%9E%E6%B5%8B%E7%9A%84%22%E7%87%83%E6%96%99%E5%BA%93%22">第十章：数据模块：回测的&quot;燃料库&quot;</h1>
<p>&quot;兵马未动，粮草先行&quot;。在量化交易中，数据就是策略的&quot;粮草&quot;。看海量化交易系统提供了一个强大的数据中心模块，用于管理回测所需的所有历史数据。本章将首先深入介绍数据中心的设计理念、两大核心机制及其技术实现，随后提供一份详尽的图形化操作指南。</p>
<hr>
<h2 id="101-%E6%95%B0%E6%8D%AE%E8%8E%B7%E5%8F%96%E7%9A%84%E5%8F%8C%E9%87%8D%E6%A0%B8%E5%BF%83">10.1 数据获取的双重核心</h2>
<p>数据中心提供了两种关键的数据获取机制，它们虽然都旨在从市场获取数据，但其设计理念、数据流向及最终应用场景存在显著差异：</p>
<ul>
<li><strong>数据下载</strong>：将数据作为独立的 <code>.csv</code> 文件显式存储，主要用于<strong>系统外部</strong>的量化研究。</li>
<li><strong>数据补充</strong>：更新和完善MiniQMT<strong>系统内部</strong>的历史数据库，专门服务于内部回测功能。</li>
</ul>
<hr>
<h2 id="102-%E6%95%B0%E6%8D%AE%E4%B8%8B%E8%BD%BD%E6%98%BE%E5%BC%8F%E5%AD%98%E5%82%A8%E8%B5%8B%E8%83%BD%E9%87%8F%E5%8C%96%E7%A0%94%E7%A9%B6">10.2 数据下载：显式存储，赋能量化研究</h2>
<p>&quot;数据下载&quot;功能的核心在于将金融数据以独立、可见、可直接操作的 <code>.csv</code> 文件格式提供给用户。这种设计使得数据变得&quot;触手可及&quot;，为后续的分析研究铺平了道路。</p>
<ul>
<li><strong>应用场景</strong>:
<ul>
<li><strong>直接检视数据</strong>：在Excel或文本编辑器中检查数据质量、格式及具体数值。</li>
<li><strong>利用外部工具分析</strong>：将数据导入Python（使用Pandas库）、R等进行复杂的统计建模或策略回测原型开发。</li>
<li><strong>与其他系统集成</strong>：作为标准数据格式，方便导入其他研究平台。</li>
</ul>
</li>
<li><strong>复权处理</strong>: 该功能支持在下载时直接选择复权方式，允许用户获取基于特定复权逻辑（如前复权、后复权）的CSV数据。</li>
</ul>
<hr>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/csv数据文件.png" alt="csv数据文件" />
</p>
<p align="center">csv数据文件</p>
<h2 id="103-%E6%95%B0%E6%8D%AE%E8%A1%A5%E5%85%85%E6%97%A0%E7%BC%9D%E9%9B%86%E6%88%90%E9%A9%B1%E5%8A%A8%E5%86%85%E9%83%A8%E5%9B%9E%E6%B5%8B">10.3 数据补充：无缝集成，驱动内部回测</h2>
<p>与&quot;数据下载&quot;不同，&quot;数据补充&quot;功能的设计目标是服务于<strong>看海量化交易系统与MiniQMT本身</strong>。它的核心任务是更新和完善MiniQMT内部所依赖的历史数据库，为策略回测引擎提供坚实的数据基础。</p>
<blockquote>
<p>💡 <strong>数据调用链路</strong></p>
<p>换句话说，回测系统中使用到的数据，并非临时从网络下载，而是通过&quot;数据补充&quot;提前下载到本地的。更具体地说，&quot;数据补充&quot;调用的是xtquant的<code>download_history_data</code>函数将数据写入本地；而在回测过程中，策略通过<code>get_market_data_ex</code>等函数高速地读取这些本地数据。</p>
</blockquote>
<p>执行&quot;数据补充&quot;时，获取的数据并不会以独立文件的形式存储在用户指定的通用目录，而是直接写入<strong>MiniQMT系统自身的数据文件夹</strong>。该路径通常位于QMT安装目录下的 <code>userdata_mini\datadir</code> 中。数据的存储格式是内部优化的二进制格式（<code>.dat</code>文件），目的是确保最高效地读取和利用。</p>
<p>是在QMT软件安装路径的userdata_mini\datadir当中可以看到：</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/datadir文件夹.png" alt="datadir文件夹" />
</p>
<p align="center">datadir文件夹</p>
<p>其中包括了上交所、深交所股票、权重信息、成分股信息等等。打开“SH”文件夹可以看到有这几个子文件夹：</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/datadir.png" alt="SH文件夹内数据" />
</p>
<p align="center">SH文件夹内数据</p>
其中文件夹的数字代表的是秒，0文件夹中存的是tick数据，60存的是1m数据，300存的是5m数据，86400则是1d数据。再进一步打开则是DAT文件，这就是QMT的二进制数据了。
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/DAT数据.png" alt="DAT数据" />
</p>
<p align="center">DAT数据</p>
<hr>
<h2 id="104-%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96%E5%A4%9A%E7%BA%BF%E7%A8%8B%E6%8A%80%E6%9C%AF%E5%8C%96%E8%A7%A3%E7%95%8C%E9%9D%A2%E9%98%BB%E5%A1%9E">10.4 性能优化：多线程技术化解界面阻塞</h2>
<p>无论是下载外部CSV文件还是补充内部数据，这两种操作均涉及大量的网络请求和磁盘I/O，属于典型的耗时任务。为解决早期版本中因此导致的界面长时间冻结（&quot;未响应&quot;）问题，当前数据模块采用了<strong>多线程优化策略</strong>。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/保持响应.png" alt="数据下载时界面保持响应" />
</p>
<p align="center">数据下载时界面保持响应，且可随时停止</p>
<ul>
<li><strong>任务分离</strong>：将耗时的数据处理逻辑移至独立的后台工作线程执行。</li>
<li><strong>异步通信</strong>：主界面线程仅负责启动和接收反馈。后台线程通过PyQt的信号将执行进度、结果或错误信息实时发送回主线程，安全地更新进度条、日志等UI元素。</li>
<li><strong>中断控制</strong>：用户可在任务执行期间随时点击&quot;停止&quot;按钮，安全地终止后台操作。</li>
</ul>
<p>这项优化显著提升了用户体验，确保了界面的流畅响应和任务的稳定性。</p>
<hr>
<h2 id="105-%E6%A8%A1%E5%9D%97%E6%BC%94%E8%BF%9B%E4%BB%8E%E7%8B%AC%E7%AB%8B%E5%B7%A5%E5%85%B7%E5%88%B0%E9%9B%86%E6%88%90%E7%BB%84%E4%BB%B6">10.5 模块演进：从独立工具到集成组件</h2>
<p>值得强调的是，当前看海量化交易系统的用户交互重心已是功能更全面的<strong>主回测系统界面</strong>。在此新架构下，数据模块转变为一个核心的功能组件，通过主界面工具栏上的&quot;数据模块&quot;按钮按需调用。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/集成数据模块.gif" alt="从主界面调用数据模块" />
</p>
<p align="center">从主界面调用数据模块的流程演示</p>
<hr>
<h2 id="106-%E8%AF%A6%E7%BB%86%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97%E6%95%B0%E6%8D%AE%E8%8E%B7%E5%8F%96%E5%B7%A6%E4%BE%A7%E9%9D%A2%E6%9D%BF">10.6 详细操作指南：数据获取（左侧面板）</h2>
<p>本节将为您提供一份关于数据中心的、图文并茂的完整使用指南。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/数据工具界面.png" alt="数据工具界面" />
</p>
<p align="center">数据中心模块界面</p>
<h3 id="1061-%E8%AE%BE%E7%BD%AE%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E8%B7%AF%E5%BE%84">10.6.1 设置数据存储路径</h3>
<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_53f4904034701c34ca8f3015d53c81d0.jpg" style="
    margin: 10px auto;
    display: block;">
</div>
<p>此路径<strong>仅对&quot;数据下载&quot;功能有效</strong>，指定生成的.csv文件存放在何处。您可以直接在输入框粘贴路径，或通过&quot;浏览...&quot;按钮选择。此设置会自动保存，下次无需重复设置。</p>
<p>💡 &quot;数据补充&quot;的路径</p>
<p>&quot;数据补充&quot;功能会直接将数据写入QMT的内部数据目录（通常是 userdata_mini\datadir），而<strong>不受此路径设置的影响</strong>。</p>
<h3 id="1062-%E9%80%89%E6%8B%A9%E8%82%A1%E7%A5%A8%E8%8C%83%E5%9B%B4">10.6.2 选择股票范围</h3>
<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2025/01/wp_editor_md_5283e251550080f943981f30b89dffa9.jpg" style="
    margin: 10px auto;
    display: block;">
</div>
<p>系统提供了多种方式来定义您想获取数据的股票范围：</p>
<ul>
<li>预设板块/指数：直接勾选&quot;沪深A股&quot;、&quot;沪深300成分股&quot;等常用板块。</li>
<li>自选清单：系统提供了一个便捷的&quot;自选清单&quot;功能。勾选此项后，直接点击加粗带下划线的&quot;<strong><u>自选清单</u></strong>&quot;文字，即可用记事本打开 otheridx.csv 文件，仿照其中的格式（股票代码,股票名称）编辑和保存您的常用列表。</li>
<li>添加自定义列表：点击&quot;添加自定义列表&quot;按钮，可以从您的电脑中选择一个或多个已编辑好的股票列表文件。</li>
<li>预览与管理：所有选中的列表都会显示在下方的预览框中，可随时点击&quot;清空列表&quot;来重新选择。</li>
</ul>
<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_222892d75c5d72d5969849d59af61f8e.jpg" style="
    width: 170px;
    height: 200px;
    margin: 10px auto;
    display: block;">
  <p style="text-align: center; color: #888;"><i>CSV文件内容示例</i></p>
</div>
<h3 id="1063-%E9%85%8D%E7%BD%AE%E6%95%B0%E6%8D%AE%E5%8F%82%E6%95%B0">10.6.3 配置数据参数</h3>
<p><a href="https://khsci.com/khQuant/wp-content/uploads/2025/01/wp_editor_md_dc886c96ae1a9c0a995aae6f3735d07e.jpg"><img src="https://khsci.com/khQuant/wp-content/uploads/2025/01/wp_editor_md_dc886c96ae1a9c0a995aae6f3735d07e.jpg" alt=""></a></p>
<ul>
<li>周期类型：支持<code>tick</code>、<code>1m</code>、<code>5m</code>和<code>1d</code>四种。当您切换周期时，下方的&quot;字段列表&quot;会自动更新。</li>
<li>复权方式：此项仅对&quot;数据下载&quot;有效。您可以选择&quot;前复权&quot;、&quot;后复权&quot;等，以获取计算好的复权数据csv文件。执行&quot;数据补充&quot;时，系统始终写入不复权的原始行情。</li>
<li>字段选择：根据您的需求勾选要获取的数据字段。Tick周期和K线周期所支持的字段有所不同。</li>
</ul>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_ff150ba4d68fc7e33347f5001adf231f.jpg" alt="Tick周期字段" />
    <br>
    <i>Tick周期可选字段</i>
</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_5b6035b9fde5b20e039af2db5c29f1cb.jpg" alt="K线周期字段" />
    <br>
    <i>K线周期可选字段</i>
</p>
<h3 id="1064-%E8%AE%BE%E5%AE%9A%E6%97%B6%E9%97%B4%E8%8C%83%E5%9B%B4">10.6.4 设定时间范围</h3>
<p><a href="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_01391588c89f49f9cbf82a8adb993e26.jpg"><img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_01391588c89f49f9cbf82a8adb993e26.jpg" alt=""></a></p>
<p>您可以通过日历控件选择一个起止日期，并可选择是获取&quot;全天&quot;数据，还是&quot;指定时间段&quot;（如<code>09:30</code>-<code>10:00</code>）的数据。当选择全天时，数据是从9:15集合竞价阶段开始下载。</p>
<h3 id="1065-%E6%89%A7%E8%A1%8C%E4%BB%BB%E5%8A%A1%E4%B8%8E%E7%9B%91%E6%8E%A7">10.6.5 执行任务与监控</h3>
<p><a href="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_18dfc491366e2c0bdd7228034f6a87ef.jpg"><img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_18dfc491366e2c0bdd7228034f6a87ef.jpg" alt=""></a></p>
<ul>
<li>启动任务：配置完成后，点击 <strong>下载数据</strong> 或 <strong>补充数据</strong> 按钮即可开始。</li>
<li>监控状态：
<ul>
<li>界面不阻塞：得益于多线程技术，无论是下载还是补充数据，<strong>界面都会保持流畅响应</strong>，不会出现&quot;未响应&quot;状态。</li>
<li>随时中断：任务开始后，按钮会变为红色的&quot;停止&quot;按钮，您可以随时点击它来安全地终止当前任务。</li>
<li>进度条：下方的进度条会实时反馈任务完成的百分比。</li>
<li>状态栏：界面最底部的状态栏会滚动显示详细的日志信息，如&quot;正在下载 xxx 的数据...&quot;，或报告下载错误。</li>
</ul>
</li>
</ul>
<p><a href="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_6cc17fa9c0dcea6162ee3e09a2155014.jpg"><img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_6cc17fa9c0dcea6162ee3e09a2155014.jpg" alt=""></a></p>
<p>💡 <strong>下载文件命名规则</strong></p>
<blockquote>
<p>文件命名规则:
– 存储的文件名格式: &quot;{股票代码}{周期类型}{起始日期}{结束日期}{时间段}_{复权方式}.csv&quot;
– 示例1: &quot;000001.SZ_tick_20240101_20240430_all_none.csv&quot;
– 股票代码: 000001.SZ
– 周期类型: tick
– 起始日期: 20240101
– 结束日期: 20240430
– 时间段: all (表示全部时间段)
– 复权方式: none (表示不复权)
– 示例2: &quot;000001.SZ_1d_20240101_20240430_all_front.csv&quot;
– 复权方式: front (表示前复权)
– 如果指定了具体的时间段,时间段部分将替换为 &quot;HH_MM-HH_MM&quot; 的格式
– 示例: &quot;000001.SZ_1m_20240101_20240430_09_30-11_30_none.csv&quot;
– 时间段: 09_30-11_30 (表示 09:30 到 11:30 的时间段)
– 复权方式有以下几种：
– 'none': 不复权，使用原始价格
– 'front': 前复权，基于最新价格进行前复权计算
– 'back': 后复权，基于首日价格进行后复权计算
– 'front_ratio': 等比前复权，基于最新价格进行等比前复权计算
– 'back_ratio': 等比后复权，基于首日价格进行等比后复权计算</p>
</blockquote>
<hr>
<h2 id="107-%E8%AF%A6%E7%BB%86%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97%E6%95%B0%E6%8D%AE%E6%B8%85%E6%B4%97%E5%8F%B3%E4%BE%A7%E9%9D%A2%E6%9D%BF">10.7 详细操作指南：数据清洗（右侧面板）</h2>
<p>数据清洗模块可以说是和历史数据下载模块是一体的，它用于修正您已下载到本地的<code>.csv</code>文件。</p>
<p><a href="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_8a8c8869de2bd8a9e10ad0cb40b3ecee.jpg"><img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_8a8c8869de2bd8a9e10ad0cb40b3ecee.jpg" alt=""></a></p>
<h3 id="1071-%E5%BE%85%E6%B8%85%E6%B4%97%E7%9A%84%E6%96%87%E4%BB%B6%E5%A4%B9%E9%80%89%E6%8B%A9">10.7.1 待清洗的文件夹选择</h3>
<p>此路径会自动与左侧&quot;数据下载&quot;模块的路径相关联，同时也可以手动修改，以增加该模块使用的便捷性和灵活性。</p>
<h3 id="1072-%E9%80%89%E6%8B%A9%E6%B8%85%E6%B4%97%E6%93%8D%E4%BD%9C">10.7.2 选择清洗操作</h3>
<p><a href="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_8dc15da1c34486f1d5faf3c3402b7db6.jpg"><img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_8dc15da1c34486f1d5faf3c3402b7db6.jpg" alt=""></a></p>
<p>此处按需勾选需要执行的清洗任务。</p>
<blockquote>
<p>⚠️ <strong>注意</strong>：</p>
<ul>
<li>&quot;移除异常值&quot;应该慎重选择，因为它可能会剔除一些极端但有效的行情。</li>
<li>点击&quot;开始清洗&quot;后，程序会直接在原数据上进行修改，即<strong>清洗后的数据将覆盖原数据</strong>。请务必提前备份重要数据。</li>
</ul>
</blockquote>
<h3 id="1073-%E6%B8%85%E6%B4%97%E7%BB%93%E6%9E%9C%E6%97%A5%E5%BF%97">10.7.3 清洗结果日志</h3>
<p>程序会生成详细的清洗日志，以便用户确认清理的数据是否符合预期。</p>
<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_8529ee9dfe79906b72c88f4634e96f5f.jpg" style="
    margin: 10px auto;
    display: block;">
</div>
<p>日志内容包括：</p>
<ul>
<li>处理文件的总数量</li>
<li>每个文件的具体处理情况（如缺失值填充数量、重复数据删除数量等）</li>
<li>被删除数据的具体内容，以便核对</li>
<li>清洗完成的时间戳</li>
</ul>
<p>如果想保存所有日志信息，可以点击&quot;保存清洗日志&quot;按钮，将报告导出为<code>.txt</code>文件。</p>
<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_893a1901248804b349cbc3d5bcedda2b.jpg" style="
    margin: 10px auto;
    display: block;">
</div>
<hr>
<h2 id="108-%E8%AF%A6%E7%BB%86%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97%E6%95%B0%E6%8D%AE%E5%8F%AF%E8%A7%86%E5%8C%96%E5%B7%A5%E5%85%B7">10.8 详细操作指南：数据可视化工具</h2>
<p>可视化模块主要为了做两件事：
第一，确认一下下载的数据的总体概况，比如总共有多少只股票的数据、占用的空间等基本信息。
第二，任意选取其中的某一只股票，绘制其数据文件中的各类数据的图线，也就是数据的可视化。</p>
<h3 id="1081-%E5%8F%AF%E8%A7%86%E5%8C%96%E6%A8%A1%E5%9D%97%E4%B8%8E%E4%B8%BB%E7%95%8C%E9%9D%A2%E7%9A%84%E8%A1%94%E6%8E%A5">10.8.1 可视化模块与主界面的衔接</h3>
<p>该功能作为工具模块内置于平台中，通过数据中心顶部的工具栏调用。点击第一个图标，即可弹出此可视化界面。</p>
<p><a href="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_f062f26dbf87b8446ee2ce3fcb6ab0ba.jpg"><img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_f062f26dbf87b8446ee2ce3fcb6ab0ba.jpg" alt=""></a></p>
<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_645afba8e02d81833944e77de56be5e2.jpg" style="
    margin: 10px auto;
    display: block;">
</div>
<p>特别值得一提的是，该模块实现了与主界面的智能联动。它会自动继承主界面当前的数据文件夹路径，这意味着用户在下载完数据后可以直接点击图标进行分析，无需重复设置路径。当然，用户也可以通过界面上的”浏览…”按钮随时切换到其他数据文件夹。</p>
<h3 id="1082-%E6%96%87%E4%BB%B6%E4%BF%A1%E6%81%AF%E7%BB%9F%E8%AE%A1">10.8.2 文件信息统计</h3>
<p>它能自动扫描文件夹，解析每个文件名，并提供全面的数据概览，帮助快速核实数据完整性。</p>
<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_6ecf033f5d328a3855a049c5d386702d.jpg" style="
    margin: 10px auto;
    display: block;">
</div>
统计信息包括：
<ul>
<li>基础信息：股票总数、文件总大小</li>
<li>市场分布：深市、沪市、北交所的股票数量统计</li>
<li>数据特征：数据周期类型（tick、1m、5m、1d等）</li>
<li>时间范围：数据的起止日期
这些信息直观地展示在界面右上角，帮助用户快速了解数据集的基本特征。这个功能特别适合在批量下载数据后使用，可以帮助快速核实数据的完整性。</li>
</ul>
<h3 id="1083-%E6%99%BA%E8%83%BD%E5%8F%AF%E8%A7%86%E5%8C%96%E5%B1%95%E7%A4%BA">10.8.3 智能可视化展示</h3>
<p>系统会根据数据特征自动调整显示模式：</p>
<ul>
<li><strong>股票选择</strong>：在&quot;股票&quot;下拉菜单中，系统会自动将股票代码解析为股票名称。</li>
<li><strong>日期选择</strong>：如果数据是tick、1m或5m级别，会自动生成一个日期选择菜单。</li>
<li><strong>智能降采样</strong>：当数据量过大时，系统会自动降采样以保证图表响应流畅。</li>
</ul>
<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_67858ff5700506888dd7a6cb7bdbc784.jpg" style="
    margin: 10px auto;
    display: block;">
    <p style="color: #888888; margin-top: 5px; margin-bottom: 20px;"><i>日线数据展示</i></p>
</div>
<h3 id="1084-%E4%BA%A4%E4%BA%92%E5%8A%9F%E8%83%BD%E4%B8%8E%E7%94%A8%E6%88%B7%E4%BD%93%E9%AA%8C">10.8.4 交互功能与用户体验</h3>
<p>为了提供更好的数据分析体验，系统实现了丰富的交互功能：</p>
<ul>
<li>通过鼠标框选来放大查看特定时间段的数据。</li>
<li>右键点击可快速重置视图。</li>
<li>鼠标悬停会显示该点的详细数据信息。</li>
<li>点击图例可隐藏/显示对应数据曲线。</li>
</ul>
<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_06285341d8e8d61f4cbf758567d43b2f.jpg" style="margin: 10px auto; display: block;">
  <p style="color: #888888; margin-top: 5px; margin-bottom: 20px;"><i>鼠标悬停显示详细数据</i></p>
</div>
<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_70d9b94743a2c0dd1d8a22f0132c282a.jpg" style="margin: 10px auto; display: block;">
  <p style="color: #888888; margin-top: 5px; margin-bottom: 20px;"><i>窗口局部缩放</i></p>
</div>
<p>这个可视化模块虽然看似简单，但在实现过程中考虑了很多实用性的细节。它既可以用来验证数据完整性，也能支持初步的技术分析。</p>

</body>
</html>
