{"system": {"userdata_path": "I:/国金证券QMT交易端/userdata_mini"}, "run_mode": "backtest", "account": {"account_id": "********", "account_type": "STOCK"}, "strategy_file": "I:/qmt5/code/allstrategies/趋势选股动态止盈止损.py", "data_mode": "custom", "backtest": {"start_time": "********", "end_time": "********", "init_capital": 1000000.0, "min_volume": 100, "benchmark": "sh.000300", "trade_cost": {"min_commission": 5.0, "commission_rate": 0.0001, "stamp_tax_rate": 0.001, "flow_fee": 0.0, "slippage": {"type": "ratio", "tick_size": 0.01, "tick_count": 2, "ratio": 0.01}}, "trigger": {"type": "custom", "custom_times": ["09:31:00", "10:10:00"], "start_time": "09:30:00", "end_time": "15:00:00", "interval": 300}}, "data": {"kline_period": "1m", "dividend_type": "front", "fields": ["open", "high", "low", "close", "volume", "amount", "settelementPrice", "openInterest", "preClose", "suspendFlag"], "stock_list": ["600028.SH", "600030.SH", "600031.SH", "600036.SH", "600048.SH", "600050.SH", "600150.SH", "600276.SH", "600309.SH", "600406.SH", "600438.SH", "600519.SH", "600690.SH", "600809.SH", "600887.SH", "600900.SH", "600941.SH", "601012.SH", "601088.SH", "601127.SH", "601166.SH", "601225.SH", "601288.SH", "601318.SH", "601328.SH", "601390.SH", "601398.SH", "601601.SH", "601628.SH", "601633.SH", "601658.SH", "601668.SH", "601728.SH", "601766.SH", "601816.SH", "601857.SH", "601888.SH", "601899.SH", "601919.SH", "601985.SH", "601988.SH", "603259.SH", "603288.SH", "603501.SH", "603993.SH", "688012.SH", "688041.SH", "688111.SH", "688256.SH", "688981.SH"]}, "market_callback": {"pre_market_enabled": true, "pre_market_time": "08:30:00", "post_market_enabled": false, "post_market_time": "15:30:00"}, "risk": {"position_limit": 0.95, "order_limit": 100, "loss_limit": 0.1}}