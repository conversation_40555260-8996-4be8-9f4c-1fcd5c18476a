# coding: utf-8
from typing import Dict, List
import numpy as np
from xtquant import xtdata
import datetime
import logging  # 添加logging模块导入

# 全局变量
position = {}
params = {}
risk_params = {}
trade_time_ranges = []  # 添加交易时间范围列表

def init():
    """策略初始化"""
    global position, params, risk_params, trade_time_ranges
    
    # 初始化持仓记录
    position = {}
    
    # 初始化策略参数
    params = {
        "ma_period": 20  # 移动平均线周期
    }
    
    # 初始化风控参数
    risk_params = {
        "max_position": 0.1  # 最大持仓比例
    }
    
    # 初始化交易时间范围
    trade_time_ranges = [
        {"start": "09:30:00", "end": "11:30:00"},
        {"start": "13:00:00", "end": "15:00:00"}
    ]
    
    print("策略初始化完成")

def khPreMarket(data: Dict) -> List[Dict]:
    """盘前回调函数
    
    Args:
        data: 行情数据，格式与khHandlebar相同，包含当前日期信息
    
    Returns:
        List[Dict]: 交易信号列表，格式与khHandlebar返回值相同
    """
    signals = []
    
    # 获取当前日期信息
    current_time = data.get("__current_time__", {})
    current_date_str = current_time.get("date", "")
    
    print(f"盘前回调执行 - 日期: {current_date_str}")
    # 添加警告信息
    logging.debug("已进入盘前")
    
    # 在这里添加盘前要执行的代码
    # 例如：检查持仓，准备今日交易计划等
    
    # 如果需要生成交易信号，可以在这里添加
    
    return signals

def khPostMarket(data: Dict) -> List[Dict]:
    """盘后回调函数
    
    Args:
        data: 行情数据，格式与khHandlebar相同，包含当前日期信息
    
    Returns:
        List[Dict]: 交易信号列表，格式与khHandlebar返回值相同
    """
    signals = []
    
    # 获取当前日期信息
    current_time = data.get("__current_time__", {})
    current_date_str = current_time.get("date", "")
    
    print(f"盘后回调执行 - 日期: {current_date_str}")
    
    # 在这里添加盘后要执行的代码
    # 例如：总结当日交易，计算绩效指标，调整参数等
    
    # 如果需要生成交易信号，可以在这里添加
    
    return signals

def calculate_ma(code: str, period: int) -> float:
    """计算移动平均线
    
    Args:
        code: 股票代码
        period: MA周期
    
    Returns:
        float: MA值，如果数据不足返回None
    """
    # 获取历史收盘价数据
    history_data = xtdata.get_market_data(
        field_list=["close"],
        stock_list=[code],
        period="1d",  
        count=period + 5
    )
    
    if history_data is None or history_data['close'].shape[1] < period:
        print(f"警告: {code} 历史数据不足 {period} 个周期")
        return None
        
    # 计算MA - 处理DataFrame格式数据
    closes = history_data['close'].values[0][-period:]  # 获取最近period个周期的收盘价
    ma = np.mean(closes)*0.8
    return ma

def is_in_trading_time(time_str: str) -> bool:
    """判断当前时间是否在交易时间范围内
    
    Args:
        time_str: 时间字符串，格式为"HH:MM:SS"
        
    Returns:
        bool: 是否在交易时间范围内
    """
    global trade_time_ranges
    
    if not time_str:
        return False
        
    for time_range in trade_time_ranges:
        if time_range["start"] <= time_str <= time_range["end"]:
            return True
            
    return False

def khHandlebar(data: Dict) -> List[Dict]:
    """策略主逻辑
    
    Args:
        data: 行情数据，格式为 {股票代码: {字段名: 字段值}, "__current_time__": {...}}
              其中包含特殊键"__current_time__"，其值为一个字典，包含以下字段：
              - timestamp: 时间戳（整数，秒级）
              - datetime: 格式化的日期时间字符串，格式为"%Y-%m-%d %H:%M:%S"
              - date: 格式化的日期字符串，格式为"%Y-%m-%d"
              - time: 格式化的时间字符串，格式为"%H:%M:%S"
              - raw_time: 原始时间戳（保持原格式）
        
    Returns:
        List[Dict]: 交易信号列表，每个信号字典包含以下字段：
        {
            "code": str,       # 股票代码
            "action": str,     # 交易动作，可选值："buy"(买入) | "sell"(卖出)
            "price": float,    # 委托价格
            "volume": int,     # 委托数量，单位：股
            "reason": str,     # 交易原因说明
            "order_type": str, # 可选，委托类型，默认为"limit"：
                              # "limit"(限价) | "market"(市价) | "best"(最优价)
            "position_type": str,  # 可选，持仓方向，默认为"long"：
                                  # "long"(多头) | "short"(空头)
            "order_time": str, # 可选，委托时间，格式"HH:MM:SS"
            "remark": str      # 可选，备注信息
        }
    """
    signals = []
    
    # 获取当前时间信息（如果可用）
    current_time = data.get("__current_time__", {})
    current_time_str = current_time.get("time", "")
    current_date_str = current_time.get("date", "")
    current_datetime_str = current_time.get("datetime", "")
    current_timestamp = current_time.get("timestamp", 0)
    
    # 输出时间信息进行调试（仅在需要时取消注释）
    # print(f"当前处理时间: {current_datetime_str}, 时间戳: {current_timestamp}")
    
    # 检查当前时间是否在交易时间范
    # 
    #围内
    # 注意：如果使用了自定义定时触发或K线触发，这个检查可能是多余的
    # 因为触发器已经控制了触发时间，但保留这个检查可以提供额外的灵活性
    if current_time_str and not is_in_trading_time(current_time_str):
        # print(f"当前时间 {current_time_str} 不在交易时间范围内，跳过信号生成")
        return []
    
    for code, tick_data in data.items():
        # 跳过特殊键
        if code == "__current_time__":
            continue
            
        close = tick_data.get("close", 0)
        if close == 0:
            print(f"警告: {code} 未获取到收盘价")
            continue
            
        # 计算MA
        ma = calculate_ma(code, params['ma_period'])
        if ma is None:
            continue
            
        # 交易逻辑
        if close > ma and code not in position:
            # 产生买入信号
            signal = {
                "code": code,
                "action": "buy",
                "price": close,
                "volume": 100,
                "reason": f"价格 {close} 高于MA{params['ma_period']} {ma}"
            }
            # 添加当前时间到信号中（如果可用）
            if current_time_str:
                signal["order_time"] = current_time_str
                signal["order_date"] = current_date_str
                
            signals.append(signal)
            position[code] = close

        elif close < ma and code in position:
            # 产生卖出信号
            signal = {
                "code": code,
                "action": "sell",
                "price": close,
                "volume": 100,
                "reason": f"价格 {close} 低于MA{params['ma_period']} {ma}"
            }
            # 添加当前时间到信号中（如果可用）
            if current_time_str:
                signal["order_time"] = current_time_str
                signal["order_date"] = current_date_str
                
            signals.append(signal)
            position.pop(code)

    return signals