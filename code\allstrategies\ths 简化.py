# coding: utf-8
from typing import Dict, List
import numpy as np
from xtquant import xtdata
import datetime
import logging
import os
import json
# 从 khQTTools 导入新的信号生成函数和移动过来的函数
from khQTTools import generate_signal, calculate_max_buy_volume

# 全局变量
position = {}  # 仅用于记录操作，不应该用于判断实际持仓
params = {}
risk_params = {}
stock_list = []

def init(stocks=None, data=None):
    """策略初始化
    
    Args:
        stocks: 股票代码列表，例如['000001.SZ', '600000.SH']
        data: 包含时间、账户、持仓等信息的数据字典，结构如下：
            {
                "__current_time__": {
                    "timestamp": 时间戳(int),
                    "datetime": "YYYY-MM-DD HH:MM:SS"格式的日期时间(str),
                    "date": "YYYY-MM-DD"格式的日期(str),
                    "time": "HH:MM:SS"格式的时间(str)
                },
                "__account__": {
                    "cash": 可用资金(float),
                    "total_asset": 总资产(float),
                    "market_value": 持仓市值(float),
                    "frozen_cash": 冻结资金(float)
                },
                "__positions__": {
                    "股票代码1": {
                        "volume": 持仓数量(int),
                        "avg_price": 平均持仓成本(float),
                        "current_price": 当前价格(float),
                        "market_value": 持仓市值(float),
                        "profit": 持仓盈亏(float)
                    },
                    "股票代码2": {...}
                }
            }
    """
    global position, params, risk_params
    
    # 初始化持仓记录
    position = {}
    
    stock_code = stocks[0]
    
    # 初始化策略参数
    params = {
        "short_ma_period": 5,  # 短期均线周期
        "long_ma_period": 20,  # 长期均线周期
        "stock_code": stock_code  # 交易标的
    }
    
    # 初始化风控参数
    risk_params = {
        "max_position": 1.0  # 最大持仓比例（全仓）
    }
    
    print(f"策略初始化完成，交易标的：{stock_code}")

def calculate_ma(code: str, short_period: int, long_period: int, current_date_str: str = None) -> tuple:
    """计算移动平均线
    
    Args:
        code: 股票代码
        short_period: 短期均线周期
        long_period: 长期均线周期
        current_date: 当前回测日期，格式为'YYYYMMDD'
        
    Returns:
        tuple: (短期均线值, 长期均线值)，数据不足则返回(None, None)
    """
    try:
        # 格式化日期用于计算均线
        date_parts = current_date_str.split("-")
        current_date_formatted = f"{date_parts[0]}{date_parts[1]}{date_parts[2]}"
        # 使用基于当前日期的历史数据
        # 获取历史收盘价数据，以当前日期为结束日期
        history_data = xtdata.get_market_data(
            field_list=["close"],
            stock_list=[code],
            period="1d",
            start_time="20240101",  # 使用较早的起始日期
            end_time=current_date_formatted,  # 使用当前回测日期作为结束日期
            dividend_type='front'
        )
            
        # 计算均线
        closes = history_data['close'].values[0]
        
        # 排除最后一个数据点（当日数据），只使用之前的数据
        closes = closes[:-1]
        
        # 计算均线，使用最后需要的几个数据点
        ma_long = round(np.mean(closes[-long_period:]), 2)
        ma_short = round(np.mean(closes[-short_period:]), 2)
                
        return ma_short, ma_long
        
    except Exception as e:
        logging.error(f"计算均线时发生错误: {str(e)}")
        return None, None

def khHandlebar(data: Dict) -> List[Dict]:
    """策略主逻辑，在每个K线或Tick数据到来时执行
    
    Args:
        data: 包含时间、账户、持仓和行情数据的字典，结构如下：
            {
                "__current_time__": {
                    "timestamp": 时间戳(int),
                    "datetime": "YYYY-MM-DD HH:MM:SS"格式的日期时间(str),
                    "date": "YYYY-MM-DD"格式的日期(str),
                    "time": "HH:MM:SS"格式的时间(str)
                },
                "__account__": {
                    "cash": 可用资金(float),
                    "total_asset": 总资产(float),
                    "market_value": 持仓市值(float),
                    "frozen_cash": 冻结资金(float)
                },
                "__positions__": {
                    "股票代码1": {
                        "volume": 持仓数量(int),
                        "avg_price": 平均持仓成本(float),
                        "current_price": 当前价格(float),
                        "market_value": 持仓市值(float),
                        "profit": 持仓盈亏(float)
                    },
                    "股票代码2": {...}
                },
                "股票代码1": {
                    "close": 收盘价/最新价(float),
                    "open": 开盘价(float),
                    "high": 最高价(float),
                    "low": 最低价(float),
                    "volume": 成交量(int/float),
                    "amount": 成交额(float),
                    "其他字段": 根据订阅的数据字段而定
                },
                "股票代码2": {...}
            }
    
    Returns:
        List[Dict]: 交易信号列表，每个信号的格式为：
            {
                "code": 股票代码(str),
                "action": 交易行为，"buy"或"sell"(str),
                "price": 交易价格(float),
                "volume": 交易数量(int),
                "reason": 交易原因(str),
                "timestamp": 时间戳(int，可选)
            }
    """
    signals = []
    
    # 获取当前时间信息
    current_time = data.get("__current_time__", {})
    current_date_str = current_time.get("date", "")  
    
    # 获取股票代码
    stock_code = params["stock_code"]
    
    # 获取当前价格，确保有默认值
    stock_data = data.get(stock_code, {})
    current_price = stock_data.get("close", 0)
    
    # 计算均线
    ma_short, ma_long = calculate_ma(stock_code, params["short_ma_period"], params["long_ma_period"],current_date_str) 
    logging.info(f"计算结果 - 短期均线: {ma_short:.2f}, 长期均线: {ma_long:.2f}")
    
    # 交易逻辑: 使用 generate_signal 生成信号
    if ma_short > ma_long:
        # 金叉且无持仓，生成全仓买入信号
        buy_reason = f"5日线({ma_short:.2f}) 上穿 20日线({ma_long:.2f})，全仓买入"
        signals = generate_signal(data, stock_code, current_price, 1.0, 'buy', buy_reason)

    elif ma_short < ma_long:
        # 死叉且有持仓，生成全仓卖出信号
        sell_reason = f"5日线({ma_short:.2f}) 下穿 20日线({ma_long:.2f})，全仓卖出"
        signals = generate_signal(data, stock_code, current_price, 1.0, 'sell', sell_reason)

    return signals

def khPreMarket(data: Dict) -> List[Dict]:
    """盘前回调函数，在每个交易日开盘前执行
    
    Args:
        data: 包含时间、账户、持仓等信息的数据字典，结构如下：
            {
                "__current_time__": {
                    "timestamp": 时间戳(int),
                    "datetime": "YYYY-MM-DD HH:MM:SS"格式的日期时间(str),
                    "date": "YYYY-MM-DD"格式的日期(str),
                    "time": "HH:MM:SS"格式的时间(str)
                },
                "__account__": {
                    "cash": 可用资金(float),
                    "total_asset": 总资产(float),
                    "market_value": 持仓市值(float),
                    "frozen_cash": 冻结资金(float)
                },
                "__positions__": {
                    "股票代码1": {
                        "volume": 持仓数量(int),
                        "avg_price": 平均持仓成本(float),
                        "current_price": 当前价格(float),
                        "market_value": 持仓市值(float),
                        "profit": 持仓盈亏(float)
                    },
                    "股票代码2": {...}
                }
            }
    
    """
    signals = []
    # 此处可以添加盘前处理逻辑
    return signals

def khPostMarket(data: Dict) -> List[Dict]:
    """盘后回调函数，在每个交易日收盘后执行
    
    Args:
        data: 包含时间、账户、持仓等信息的数据字典，结构如下：
            {
                "__current_time__": {
                    "timestamp": 时间戳(int),
                    "datetime": "YYYY-MM-DD HH:MM:SS"格式的日期时间(str),
                    "date": "YYYY-MM-DD"格式的日期(str),
                    "time": "HH:MM:SS"格式的时间(str)
                },
                "__account__": {
                    "cash": 可用资金(float),
                    "total_asset": 总资产(float),
                    "market_value": 持仓市值(float),
                    "frozen_cash": 冻结资金(float)
                },
                "__positions__": {
                    "股票代码1": {
                        "volume": 持仓数量(int),
                        "avg_price": 平均持仓成本(float),
                        "current_price": 当前价格(float),
                        "market_value": 持仓市值(float),
                        "profit": 持仓盈亏(float)
                    },
                    "股票代码2": {...}
                },
                "股票代码1": {
                    "close": 收盘价(float),
                    "其他字段": 收盘时的行情数据
                },
                "股票代码2": {...}
            }
    
    """
    signals = []
    # 此处可以添加盘后处理逻辑
    return signals