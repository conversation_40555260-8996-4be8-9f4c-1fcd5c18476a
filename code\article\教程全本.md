# 看海量化交易系统 (KHQuant) - 快速入门手册

> **注意**: 本文档是看海量化交易系统 (KHQuant) 的快速入门指南，内容节选自官方文档的第一至四章。我们强烈建议您访问[看海量化官方网站](https://khsci.com/khQuant/)以获取最新、最完整的用户手册和更多高级功能。

---

# 第一章：引言：为什么选择看海量化

## 1.1 关于我

在全身心投入这款量化交易系统的研发之前，我混迹于知乎和公众号，专注于信号处理与深度学习算法的分享与探讨。后来在研究算法的过程中，产生了新的想法，即将这些年在信号处理和深度学习领域积累的思维与方法，跨界应用于充满挑战与机遇的股票市场和量化交易中。

当前教程是看海，也就是我搭建的唯一官方教程，后续教程都是在此处最先更新。

为了方便和大家交流，我在以下平台都开设了账号，欢迎关注：

* **微信公众号**：[看海的城堡](https://mp.weixin.qq.com/s/l_06l72N8WFnbM5_4Ns4Dw)
* **知乎**：[Mr.看海](https://www.zhihu.com/people/feng-zhu-38)
* **抖音**：Mr.看海（抖音号：31281929369）
* **快手**：Mr.看海（快手号：4775269996）
* **B站 (哔哩哔哩)**：[Mr看海](https://space.bilibili.com/3546667687086777)
* **头条**：[Mr.看海](https://www.toutiao.com/c/user/token/MS4wLjABAAAAzyc56KI8pUK0LEasGJ88GQ7YOog-eiSLihjiYFUUUh32URE2tUnJpiPITu4uhJCQ/?)

一直以来，我都致力于为大家提供一款免费开源的量化交易系统。可以说，这是用爱发电的产物。目前，系统开发和维护的微薄收入主要来源于通过我推荐渠道开户的朋友们所带来的些许返佣，以及热心用户的慷慨打赏。如果您觉得这款系统对您有所帮助，不妨请作者喝杯咖啡，您的这份支持，是我持续更新与完善的最大动力！

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250606235710-300x300.png" alt="微信打赏" width="300"/>
</p>

---

## 1.2 我为什么要做这样一款量化交易系统？

市面上的量化工具不少，为何还要"重复造轮子"？核心在于对现有工具的些许不满，以及一些亟待实现的功能与理念。

回测系统是量化交易的基石，它允许策略在历史数据中"演练"，评估可行性。然而，不少现有工具，如QMT，虽具备回测功能，但在策略开发的灵活性上（如Python第三方库的调用）存在限制，这束缚了AI等前沿算法的应用。KHQuant的初衷便是**打破这些枷锁，赋予开发者最大的策略实现自由度**，让Python的强大生态充分助力创新。

**关键考量：**

* **策略安全与本地运行**：策略是核心资产。KHQuant基于MiniQMT开发，确保策略代码和数据在本地运行，保障安全与隐私，同时在处理大规模数据或复杂模型时更具性能与成本优势。
* **打造趁手的"兵器"**：工具应保证专业性的同时，追求高效易用。作为一名长期从事信号处理与机器学习算法研究的开发者，深谙此道。KHQuant力求成为一款称手好用的工具，让用户能更专注于策略研究本身。
* **填补MiniQMT生态空白**：目前市场缺乏针对MiniQMT完善易用的回测与模拟平台。KHQuant愿做"第一个吃螃蟹的人"，为MiniQMT用户和量化爱好者提供新选择。

**KHQuant的核心设计原则：**

1. **模块低耦合——提升灵活性与可维护性**：
   借鉴"乐高积木"的理念，KHQuant追求UI、数据、策略与核心框架的分离。如此，各模块可独立升级、替换，避免"牵一发而动全身"，保证系统未来的迭代与扩展能力。
2. **策略高内聚——聚焦策略，提升效率**：
   使用者应专注于策略逻辑。KHQuant致力于提供稳定的底层支撑和清晰的接口规范，目标是实现策略文件在回测、模拟、实盘间无缝切换，并共享统一的图形化配置界面，最大程度减轻用户负担。未来，甚至期望通过标准化的接口，结合大语言模型辅助策略生成。

> 💡 **一言以蔽之**：KHQuant并非追求"大而全"，而是力求在**图形化、本地化、灵活性和实用性**这些核心痛点上做到"精而深"，为个人投资者提供一款免费、强大且称手的量化研究利器。

---

## 1.3 看海量化系统：特点与比较

为了让大家更清晰地理解KHQuant的定位，这里通过一个简明扼要的表格来对比其与市面上其他主流类型量化工具的特点：


| 特性维度         | 看海量化交易系统 (KHQuant)                               | 国内券商平台 (如：QMT自带, 通达信) | 开源回测框架 (如：Backtrader, vn.py)          | 在线量化平台 (如：聚宽, BigQuant) |
| :--------------- | :--------------------------------------------------------- | :--------------------------------- | :---------------------------------------------- | :-------------------------------- |
| **系统设计侧重** | GUI友好，与MiniQMT深度集成，实现数据接口**开箱即用**       | 账户直连，行情交易便捷，低成本     | 高度灵活定制，免费开源，社区支持                | 云端运行，提供数据，学习资源丰富  |
| **系统当前局限** | 生态初期，功能待完善，依赖MiniQMT，个人维护                | 策略自由度低，回测功能较弱         | 上手门槛高，需自行寻找、配置和维护数据/交易接口 | 核心代码/数据不可控，高级功能收费 |
| **目标用户画像** | 注重易用性的个人量化爱好者，希望数据策略本地化             | 普通交易者，编程能力要求不高       | 编程能力强的开发者，需深度定制                  | 量化初学者，偏好云端服务          |
| **策略编程**     | Python                                                     | 平台特定语言或脚本                 | Python                                          | Python为主                        |
| **数据获取**     | **内置MiniQMT接口，开箱即用**                              | 提供多品种行情数据                 | 需用户自行对接和维护数据源                      | 平台提供常用数据                  |
| **界面形态**     | PyQt5构建的桌面GUI                                         | 标准的行情交易软件界面             | 部分框架提供GUI（如vn.py），其他需自行绘图      | Web界面，图表友好                 |
| **使用成本**     | 免费                                                       | 开户后免费使用                     | 免费                                            | 免费入门，增值服务收费            |

> 简单来说，KHQuant致力于为A股个人投资者，提供一个在**图形化、本地化、简单实用**方面表现出色的量化工具。其与MiniQMT的深度整合，让用户免去了寻找和配置数据源的繁琐工作，可以更专注于策略开发本身。它或许并非完美无瑕，但会持续打磨与进化，力求帮助每一位使用者更高效地进行策略研究与交易实践。

---

选择"看海量化交易系统"，将能深入体验到以下几点核心优势所带来的便利与价值：

**🎨 完全开源免费，拥抱社区共建生态**：
KHQuant 不仅仅是一款工具，更是一个开放的平台。系统源代码完全公开透明，允许自由探索其实现细节，根据自身需求进行个性化修改，甚至参与到项目的共建中。这种开放性，确保了对工具的完全掌控，而不必担心任何"黑箱"操作或潜在的隐性成本。这完全是"用爱发电"的产物，旨在为国内量化爱好者提供一个纯粹、强大的免费选项。

**🛡️ 数据与策略本地化部署，安全与隐私尽在掌握**：
在量化交易领域，数据和策略无疑是核心资产。KHQuant 坚持将所有策略代码、历史数据、回测结果以及交易记录等敏感信息完全存储于本地计算机。这意味着使用者对其知识产权和交易活动拥有绝对的控制权，无需担心因依赖第三方云平台而可能带来的数据泄露、策略被窥探或服务中断的风险。智慧成果得以自主守护。

**⚙️ 可视化便捷操作与Python代码灵活驱动，双引擎满足多层次需求**：
系统精心设计了用户友好的图形化界面（GUI），使得许多常规操作，如参数配置、回测设置、股票池管理等，都可以通过简单的鼠标点击完成，极大降低了上手门槛，即使是编程经验较少的用户也能快速入门。同时，对于追求极致灵活性和复杂逻辑实现的专业开发者，KHQuant 提供了纯粹的Python策略编写环境，允许充分利用Python的强大表达能力和丰富的第三方库，构建高度定制化的交易系统。

**🧠 拥抱AI浪潮，为大模型赋能量化策略，拓展智能边界**：
人工智能飞速发展的时代，大语言模型（LLM）的能力令人瞩目。KHQuant 在设计之初便充分考虑了与AI技术的结合潜力。其清晰的模块划分、标准化的策略接口以及开放的Python环境，都为大模型在量化策略中的应用提供了便利。可以尝试使用大模型辅助进行策略逻辑的构思、代码片段的生成，甚至在未来，期望能实现更深度的融合，让AI成为策略研究与开发过程中的得力助手。

**🔗 深度整合MiniQMT，共享成熟稳定的交易执行**：
KHQuant 的行情获取深度依赖于券商的MiniQMT系统。这意味着可以直接受益于券商提供的成熟、稳定、合规的行情服务，从而能够更专注于策略本身的研发与优化。

**🎯 专注A股优化，更懂本土化交易者的实战需求**：
与其他通用型或主要面向海外市场的量化平台不同，KHQuant 在设计和功能实现上，充分考虑了A股市场的独特性。例如，针对A股的交易规则（如T+1制度、涨跌停限制）、常用的技术指标偏好、数据特点等都进行了细致的适配和优化，力求为国内投资者提供一个更接地气、更符合实战需求的量化工具。

**🚀 极致策略自由度，释放Python生态的无限潜能**：
许多量化平台会对可使用的Python第三方库施加诸多限制，这无疑束缚了策略的创新空间。KHQuant 则致力于打破这些"枷锁"，允许在策略中无拘无束地引入和使用Python生态中几乎所有的公开库。无论是用于高级数据分析的Pandas、NumPy、SciPy，还是用于机器学习的Scikit-learn、TensorFlow、PyTorch，亦或是其他专业领域的强大工具，只要认为对策略有益，都可以自由集成，从而将最前沿的技术和算法应用于量化实践中。

---

## 1.4 使用"看海量化交易平台"的背景知识清单

为了帮助不同需求的用户更好地使用"看海量化交易平台"，这里梳理了一份背景知识清单，分为入门、进阶和高级三个层次。您可以根据自己的目标和现有基础，按图索骥，逐步提升。

### 1.4.1 入门：编写开环策略实现回测

此阶段的目标是能够使用已经打包好的"看海量化平台"，编写并运行开环策略（即策略逻辑相对简单，不涉及复杂的模型训练和动态调优），并对策略进行历史回测，分析回测结果。


| 掌握程度 | 技能                               | 说明                                                                                      |
| :------- | :--------------------------------- | :---------------------------------------------------------------------------------------- |
| **必备** | Python编程基础（含Pandas/NumPy库） | 理解Python核心语法、控制流、函数，并掌握Pandas进行数据处理及NumPy进行数值计算的基本操作。 |
| **必备** | 基本的金融市场知识                 | 了解股票、K线、交易规则、常用技术指标（如均线、MACD、布林带等）的基本概念。               |
| **必备** | 理解回测报告中的关键指标           | 如收益率、最大回撤、夏普比率等。                                                          |
| **必备** | 代码编辑器/IDE的使用               | 熟练使用至少一种代码编辑工具（如VS Code, PyCharm等）进行策略脚本的编写与管理。            |

### 1.4.2 进阶：编写需模型训练的闭环策略实现回测

此阶段的目标是能够在入门基础上，进一步编写包含机器学习、深度学习等模型训练的闭环策略。这类策略通常需要根据市场反馈动态调整模型参数或交易逻辑。


| 掌握程度 | 技能                                         | 说明                                                                                    |
| :------- | :------------------------------------------- | :-------------------------------------------------------------------------------------- |
| **必备** | 扎实的Python编程能力                         | 包括面向对象编程（OOP）思想、模块化编程等。                                             |
| **必备** | Pandas/NumPy高级应用                         | 能够进行更复杂的数据转换、特征工程、性能优化等。                                        |
| **必备** | 机器学习/深度学习基础理论                    | 理解常见的监督学习、无监督学习算法原理，如线性回归、逻辑回归、决策树、SVM、神经网络等。 |
| 建议掌握 | TensorFlow/PyTorch等深度学习框架（至少一种） | 如果策略涉及深度学习模型，需要掌握至少一个主流框架的使用。                              |
| 建议掌握 | 特征工程方法                                 | 如何从原始数据中提取、构建对模型有效的特征。                                            |
| 建议掌握 | 模型评估与调优技巧                           | 了解过拟合、欠拟合，掌握交叉验证、网格搜索等模型调优方法。                              |

### 1.4.3 高级：使用开源代码，定制化修改平台

此阶段的目标是具备深入理解并修改"看海量化平台"源代码的能力，根据自身特殊需求进行二次开发和功能定制。


| 掌握程度 | 技能                                | 说明                                                                             |
| :------- | :---------------------------------- | :------------------------------------------------------------------------------- |
| **必备** | 精通Python高级编程                  | 深入理解Python的内部机制，如装饰器、生成器、元类、异步编程等。                   |
| **必备** | PyQt5 GUI编程框架                   | 深入理解PyQt5的事件循环、布局管理、信号与槽机制、自定义控件等。                  |
| **必备** | 深入理解`xtquant` 库 (MiniQMT接口) | 掌握MiniQMT的核心API调用，包括行情订阅、交易指令发送、账户信息查询等。           |
| **必备** | 软件架构设计能力                    | 能够理解和设计模块化、可扩展、可维护的软件系统，理解KHQuant的现有架构。          |
| **必备** | Git版本控制                         | 熟练使用Git进行代码版本管理与协作。                                              |
| **必备** | 量化交易系统核心组件的理解          | 深入理解事件驱动、行情处理、订单管理、风险控制、绩效计算等核心模块的原理与实现。 |
| 建议掌握 | Python多线程/异步编程               | 用于优化GUI响应、处理耗时操作等，提高平台性能和用户体验。                        |
| 建议掌握 | 事件驱动编程模型                    | 深入理解事件驱动架构，有助于更好地理解和修改平台的核心逻辑。                     |

> ✨ **小贴士**：对于绝大多数希望进行策略回测和研究的用户来说，达到"入门"级别并逐步熟悉平台功能，就已经能够满足大部分需求。"看海量化平台"也会持续推出更多策略示例和教程，帮助大家更好地理解和应用。

---

## 1.5 "看海量化交易系统"适合做什么？

"看海量化交易系统"以其灵活性和易用性，能够很好地支持多种类型的中低频量化交易策略的研发与实践。以下是一些典型的适用场景：

* ✅ **各类因子选股与轮动策略**：无论是基于经典的价值、成长、质量、动量等因子，还是自行构建的特色因子，系统都能方便地进行多因子模型的选股、打分、回测与组合轮动。
* ✅ **趋势跟踪与技术指标策略**：对于依赖均线系统、布林带、MACD、RSI等各类技术指标构建的趋势跟踪、突破或震荡策略，系统提供了良好的支持。
* ✅ **统计套利与均值回归策略**：包括但不限于配对交易、期现套利（基于MiniQMT支持的品种）、ETF套利以及其他利用市场短期失衡的均值回归型策略。
* ✅ **事件驱动型策略**：结合外部事件数据（如财报发布、重要行业新闻、政策变动等），构建在特定事件发生前后进行交易决策的策略。
* ✅ **机器学习与AI辅助策略（中低频）**：利用Scikit-learn、TensorFlow、PyTorch等库，训练机器学习或深度学习模型，对股价走势、市场状态等进行预测，并结合系统生成中低频交易信号。
* ✅ **投资组合管理与动态再平衡**：实现基于特定风险偏好或资产配置模型的投资组合构建，并根据市场变化或预设规则进行定期的动态调仓和再平衡。
* ✅ **自定义指数构建与增强**：根据特定的投资理念或行业偏好，自行编制指数并进行跟踪，或者在现有指数基础上进行Alpha增强。
* ✅ **量化知识学习与策略思想验证**：系统友好的界面和开放的特性，使其成为学习量化交易、快速验证策略思路的理想平台。
* ✅ **成熟交易逻辑的自动化执行**：将经过验证的、系统化的手动交易经验和规则，通过代码实现自动化执行，解放人力，提高效率。

> 总而言之，只要策略的执行频率和对延迟的要求不是极端严苛，KHQuant 都能提供一个强大而便捷的本地化解决方案。

---

## 1.6 "看海量化交易系统"不适合做什么？

> ⚠️ **请注意**：虽然"看海量化交易系统"力求强大与灵活，但基于其设计定位和核心依赖（MiniQMT），在以下一些方面可能并非最佳选择，了解这些局限性有助于用户做出更合理的预期和决策。

* ❌ **高频交易（HFT）与超低延迟策略**：
  * **数据层面**：MiniQMT提供的Tick数据通常是3秒快照，而非逐笔成交数据，这对于需要微秒级行情精度的典型高频策略来说，信息颗粒度不足。
  * **执行层面**：系统本身（Python语言特性、多层架构）以及通过MiniQMT的交易链路，都无法满足高频交易所要求的亚毫秒级执行延迟。
  * **技术栈**：专业的高频交易通常需要C++等高性能语言、FPGA硬件加速以及专用的低延迟交易接口和托管服务。
* ❌ **依赖极久远历史数据的细颗粒度回测**：
  * **MiniQMT数据限制**：券商版MiniQMT对历史数据的下载范围有限制。通常情况下，Tick数据可能只能获取最近一个月左右，1分钟和5分钟K线数据可能为最近一年左右，日线数据则相对完整。这意味着，如果策略需要回测数年前的分钟级甚至Tick级行情，系统可能无法直接提供足够的数据支撑。（有实力的可以开通研投版QMT，这样就有全部的数据了）
* ❌ **对多市场、多资产的复杂联动套利（超出MiniQMT范围）**：
  虽然可以通过Python的灵活性尝试对接其他数据源或接口，但KHQuant的核心优化和原生支持是围绕MiniQMT所能覆盖的A股市场（股票、ETF、部分期货期权等）。对于需要复杂跨市场（如全球市场）、跨资产类别（如外汇、加密货币）进行高精度、低延迟联动的套利策略，可能需要更专业的、针对性的平台。
* ❌ **非Windows操作系统的原生流畅运行**：
  由于MiniQMT客户端本身主要运行于Windows环境，KHQuant的主要开发和测试也是在Windows上进行的。虽然技术上用户可能尝试通过Wine等兼容层在Linux或MacOS上运行，但这并非官方支持的路径，可能会遇到稳定性问题或兼容性障碍。

---

# 第二章：重要声明与权责说明

在您使用"看海量化交易系统"（以下简称"本系统"）之前，请务必仔细阅读并充分理解本章的全部条款。这些条款构成了您与本系统作者之间关于使用本软件的重要约定。

---

## 2.1 系统依赖与免责声明

* **对MiniQMT的依赖**
  本系统的行情数据获取与交易执行功能，完全依赖于您本地安装的券商版MiniQMT客户端。为了实现回测功能，本系统会在本地存储和处理从MiniQMT下载的行情数据，但系统本身不生产任何原始数据。
* **数据验证与检验机制**
  本系统在运行过程中包含了数据有效性检验功能，会对从MiniQMT获取的数据进行基础的完整性和格式校验。但需要明确的是，这些检验仅为程序正常运行的技术保障，**不能等同于对数据准确性的担保**。市场数据的准确性和及时性完全取决于券商MiniQMT及其上游数据源。
* **核心功能定位**
  请注意，当前版本的"看海量化交易系统"是一款**策略回测与研究平台**，其核心功能是历史数据验证，官方版本不包含任何直接执行实盘交易的功能。
* **全面责任界定**
  本系统作者的责任仅限于提供软件工具本身。**使用本软件过程中遇到的任何问题，包括但不限于系统故障、数据错误、策略失效、操作失误、电脑故障等，均由用户自行承担全部责任**。对于因以下原因导致的任何直接或间接损失，作者不承担任何形式的法律或经济责任：

  1. 券商MiniQMT客户端或其服务器的任何故障、错误、延迟或数据偏差。
  2. 网络连接问题、运营商服务中断等第三方因素。
  3. 用户自行修改代码以启用实盘交易功能后，所产生的一切后果（包括但不限于任何资金损失）。
  4. 本软件自身的任何漏洞、错误、兼容性问题或运行异常。
  5. 用户操作不当、配置错误或对软件功能理解偏差。

---

## 2.2 开源承诺与维护责任

* **免费与开源**
  本系统是一款免费且开放源代码的软件，旨在为A股量化爱好者提供一个高效、便利的研究工具。
* **维护责任限制**
  作者会尽力维护系统的稳定性并进行功能迭代，但无法承诺对每一位用户的特定需求提供即时支持。具体而言：

  * **Bug修复**：将根据严重程度与影响范围进行排序并择机处理。
  * **功能开发**：新功能请求将被纳入待办池，作者会进行评估规划，但无法保证实现时间与具体方案。
  * **代码讲解**：由于精力所限，作者不提供针对开源代码的任何个人化、一对一的教学服务。
* **鼓励自主创新**
  本系统完全开源，对于有特殊或紧急功能需求的用户，我们鼓励并支持您在许可协议范围内，利用源代码自行修改、定制和实现。

---

## 2.3 使用许可协议

本系统的源代码及相关文档遵循 **CC BY-NC 4.0 (署名-非商业性使用 4.0 国际)** 许可协议。

* **您可以自由地**：

  * **分享** — 在任何媒介以任何形式复制、发行本作品。
  * **演绎** — 修改、转换或以本作品为基础进行创作。
* **但必须遵守以下条款**：

  * **署名 (BY)** — 您必须给出适当的署名，提供指向本许可协议的链接，并标明是否对作品作出了修改。
  * **非商业性使用 (NC)** — 您不得将本作品用于任何商业目的。
  * **无附加限制** — 您不得附加任何法律条款或技术措施，从而限制他人行使本许可协议所允许的权利。

#### 严正声明：关于商业使用的规定

任何个人或实体均可在协议范围内，使用本系统代码进行学习研究与自用修改。

**严禁将本系统及其任何衍生版本用于任何形式的商业目的**，包括但不限于：出售软件、以本系统为核心提供任何形式的付费服务、搭建商业化平台等。

任何违反此声明的商业行为所引发的一切法律纠纷、商业风险及经济损失，均由该使用者自行承担。**作者保留对所有侵权行为进行法律追究的权利。**

---

## 2.4 内部交流群说明

* **加入方式与条件**
  通过作者提供的推荐渠道开通MiniQMT账户的用户，可以联系作者加入内部交流群。
* **群成员专享权益**
  内部群成员可以享受以下权益：

  * **优先体验权**：最新功能的内测版本优先推送，可比公开发布提前数日甚至数weeks体验新特性。
  * **版本抢先获取**：软件正式版本发布后，群成员可通过内部渠道更早获得下载链接和更新包，无需等待公开发布。
  * **问题优先支持**：在使用过程中遇到的技术问题，能够得到更优先、更及时的响应和技术支持。
  * **策略思路分享**：群内会不定期分享一些原创的策略思路、编程技巧或市场分析心得。
  * **内部策略代码**：群成员可获得一些未公开发布的实用策略代码示例，用于学习参考。
  * **直接反馈通道**：可以直接向作者反馈建议和需求，影响软件未来的开发方向。
  * **同好交流平台**：与其他量化爱好者深度交流，分享经验，共同进步。
* **群规与维护**
  内部群主要用于技术交流和软件支持，请遵守基本的讨论秩序。群内严禁任何形式的广告、推销或与量化交易无关的内容。

---

## 2.5 投资风险免责声明

**重要提示：本系统不构成任何投资建议**

1. **教育与研究目的**
   "看海量化交易系统"及其所有相关内容（包括示例策略、代码、文档、社区讨论等）的唯一目的，是进行量化编程技术交流、策略思想探讨和金融市场研究。
2. **非投资顾问**
   本系统的任何功能、输出信息（如回测报告、性能指标）及示例代码，**均不应被解释为任何形式的投资建议或交易推荐**。历史回测表现不代表未来实际收益，过往的业绩无法预示未来的结果。
3. **用户责任自负**
   您必须基于自身的专业知识、风险承受能力和独立判断来做出投资决策。任何因使用本系统或参考其内容而进行的投资行为，所产生的一切盈利或亏损，**均由您自行承担全部责任**，与本系统作者无任何关系。

**投资有风险，入市需谨慎。**

---

# 第三章：安装与初次配置

在正式开启您的量化之旅前，我们需要确保一切准备就绪。本章将指导您完成运行环境的配置、系统的安装，以及首次启动时的关键设置。这就像战前检查装备，虽显繁琐，却至关重要。

---

## 3.1 运行环境要求

为了让"看海量化交易系统"在您的电脑上流畅运行，请确保满足以下基本环境要求：

### 3.1.1 硬件与操作系统

* **操作系统**: **Windows 10 或更高版本的64位系统**。

  > 💡 **开发者言**：由于本系统的核心依赖——MiniQMT客户端主要运行于Windows平台，因此KHQuant的主要开发和测试环境也都在Windows上。虽然理论上可能通过虚拟机或兼容层在其他系统（如macOS, Linux）上运行，但这并非官方支持的路径，可能会遇到各种意想不到的兼容性问题。
  >
* **硬件建议**:

  * **CPU**: 建议使用现代多核处理器（如 Intel i5 或 AMD R5 及以上）。
  * **内存 (RAM)**: 建议 **16GB** 或以上。请注意，内存的实际需求在很大程度上取决于您策略的复杂度和数据处理量。对于涉及大规模数据回测或复杂机器学习模型的策略，更大的内存将显著提升运行效率。我们将在后续的更新中，提供更精确的关于基础软件运行的最低硬件要求。
  * **硬盘**: 建议使用 **固态硬盘 (SSD)**，以加快数据读取和软件启动速度。

### 3.1.2 核心依赖软件

* **MiniQMT 客户端**: **这是本系统运行的绝对前提**。
  您必须首先从您开户的券商处下载并安装最新版的MiniQMT客户端，并确保您能够正常登录您的账户。本系统所有的数据获取和交易指令（若未来支持）都将通过此客户端完成。[如尚未开通miniQMT，可以点击这里免费开通。](https://khsci.com/khQuant/miniqmt/)

  成功安装后，打开miniQMT客户端将会显示下边的界面：
* **Microsoft Visual C++ Redistributable**:
  为了确保系统图形界面和部分依赖库的正常工作，您需要安装 `Microsoft Visual C++ 2015-2022 Redistributable (x64)`。

  > 🔗 **官方下载链接**: [https://aka.ms/vs/17/release/vc_redist.x64.exe](https://aka.ms/vs/17/release/vc_redist.x64.exe)
  >
  > **如何检查？** 通常，如果您能正常运行其他大型软件或游戏，这个组件很可能已经安装。如果不确定，直接下载并运行安装程序即可，它会自动判断是否需要安装或修复。
  >

---

## 3.2 看海量化系统安装

我们提供了简单快捷的安装包，让您轻松完成所有部署工作。

**1.获取安装包**: 从[看海量化交易系统官网](https://khsci.com/khQuant/)下载最新的安装文件压缩包到本地（解压密码在压缩包名称里），右键解压缩后，将会得到一个exe文件，双击exe文件安装。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_08245868e6125c1dd285cba688adb2e0.jpg" />
</p>

选择软件安装路径，一直点击下一步，直到安装成功。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_04e92a96668bf3cb9cb0a66bf77ae5e2.jpg" />
</p>

安装完成后桌面上将会有软件的快捷方式，双击可以打开软件。

**2.注意事项**:

* 您可能会看到Windows安全提示，请选择"更多信息"，然后点击"仍要运行"。
* 建议保持默认的安装路径 (`C:\Program Files (x86)\khQuant`)，或选择您喜欢的其他路径。
* 在"附加任务"步骤，建议勾选"创建桌面快捷方式"，方便日后快速启动。
* 点击"安装"，稍等片刻即可完成。

---

## 3.3 第一次启动：关键设置要点

无论您通过哪种方式安装，首次启动软件时，请务必完成以下关键设置，这是连接系统与MiniQMT的"握手"环节。

1. **打开设置对话框**:
   启动软件后，在主界面顶部工具栏找到并点击 **设置** 按钮，打开"软件设置"对话框。
2. **配置MiniQMT双路径**:
   在"软件设置"对话框中，找到 **客户端设置** 区域。这里有两个至关重要的路径需要您正确配置，它们共同构成了KHQuant与MiniQMT之间的桥梁。

   <p align="center">
       <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/QQ截图20250609233033.png" />
   </p>

   * **miniQMT客户端路径**:

     * **作用**: 指向MiniQMT的**主程序文件**。
     * **如何设置**: 点击该项旁边的 **浏览...** 按钮，在弹出的文件选择窗口中，找到您的MiniQMT安装目录，并进入 `bin.x64` 文件夹，选择 `XtItClient.exe` 文件。
     * **示例路径**: `D:\国金证券QMT交易端\bin.x64\XtItClient.exe`
   * **miniQMT路径**:

     * **作用**: 指向MiniQMT的**用户数据文件夹**，其中包含了账户信息、日志等关键数据。
     * **如何设置**: 点击该项旁边的 **浏览...** 按钮，在弹出的文件夹选择窗口中，找到并选择您本地MiniQMT的 **`userdata_mini`** 文件夹。
     * **示例路径**: `D:\国金证券QMT交易端\userdata_mini`

   > 🎯 **路径在哪找？**
   > 这两个路径通常都在您的MiniQMT安装根目录下。例如，如果您的MiniQMT安装在 `D:\国金证券QMT交易端`，那么按图索骥即可找到。**请务必确保两个路径都已正确设置**，否则系统无法正常工作。
   >
3. **保存设置**:
   完成两个路径的配置后，点击"软件设置"对话框右下角的 **保存设置** 按钮。软件会保存您的配置。请注意，此设置仅用于数据回测等功能，与主界面上的MiniQMT连接状态指示灯无关。

---

## 3.4 启动与连接

上述步骤讲述了主界面的初始设置，但是需要注意的是，在这个初始设置完成后，后续每次使用看海量化系统，都需要按照以下顺序启动软件（即先启动miniQMT，再启动看海量化交易系统）。

### 3.4.1 系统关系说明：客户端与接口

为了更好地理解为何必须先启动MiniQMT，这里对看海量化系统与MiniQMT的关系做个简要说明：

> *   **看海量化系统**与**MiniQMT**是两个独立的软件。
> *   本软件的角色是一个**接口调用方（客户端）**。它本身不处理与券商服务器的直接通讯。
> *   MiniQMT登录后，会在后台提供一个编程接口（API），所有的数据获取和交易指令都是通过这个接口完成的。
>
> **简而言之，看海量化系统的工作依赖于MiniQMT提供的接口服务。如果MiniQMT没有启动，本软件就无法连接和工作。**

### 3.4.2 正确的启动顺序

为了确保系统能够顺利连接到MiniQMT，请务必遵循以下两步启动流程：

1. **第一步：启动并登录MiniQMT**

   * 首先，打开您的券商MiniQMT客户端。
   * 在登录界面，请务必勾选 **"极简模式"**（部分券商版本可能称之为"独立交易"）。
   * 成功登录后，MiniQMT将自动在后台提供数据和交易接口服务。

   > ⚠️ **重要提示**：必须先成功登录MiniQMT，再进行下一步。看海量化系统本身无法启动MiniQMT。
   >
2. **第二步：启动看海量化系统**

   * 在MiniQMT运行的情况下，双击桌面上的看海量化系统快捷方式来启动软件。

### 3.4.3 检查连接状态

软件启动后，主界面右上角会有一个 **MiniQMT状态指示灯**，它直观地反映了当前的连接情况。

   <p align="center">
       <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/QQ截图20250609233411.png" />
   </p>

* **🟢 绿色**: **连接成功**。恭喜！系统已成功连接到MiniQMT，您可以开始进行回测或其他操作了。
* **🔴 红色**: **连接失败**。警报！这表示系统未能找到正在运行的MiniQMT。请按照以下步骤进行排查：
  1. **检查启动顺序**：确认您是**先启动并登录了MiniQMT**，然后再打开的本软件。
  2. **检查登录状态**：确认您的MiniQMT客户端已经**成功登录**账户，而不仅仅是停留在登录界面。
  3. **检查路径配置**：回到 `设置` -> `客户端设置`，再次检查您在3.3节中设置的 **两个MiniQMT路径** 是否完全正确。
  4. **重启**：尝试依次关闭本软件和MiniQMT客户端，然后重新按照正确的顺序启动它们。

完成以上步骤，您的"看海量化交易系统"就已整装待发。下一章，我们将带领您完成第一个回测流程！

---

# 第四章：快速上手：运行第一个回测

本章将引导使用一个预先配置好的工程文件（`.kh`文件），来快速完整地运行一次回测。这个过程不仅能直观地了解软件的基本操作流程，也是一个检验当前运行环境是否配置妥当、能否与本系统完美兼容的有效方式。

## 4.1 加载并配置示例工程

软件中内置了一些经典的策略示例，以供快速学习和测试。下面将加载其中一个进行演示。

### 4.1.1 加载示例工程

1. 找到并点击主界面顶部工具栏上的 **"加载配置"** 按钮。
2. 在弹出的文件选择对话框中，找到软件安装目录下的`_internal\strategies`目录，例如'C:\Program Files\KhQuant\_internal\strategies'。
3. 选择示例工程文件 `demoMACD.kh`，然后点击"打开"。

### 4.1.2 策略文件路径设置

4. **检查并重设策略文件路径**：由于每个用户的软件安装路径可能不同，加载 `.kh` 文件后，可能需要手动重新指定策略文件的位置。点击"策略文件"输入框右侧的浏览按钮。

   > ⚠️ **重要提醒：策略文件保存位置**
   >
   > 为了避免在软件升级或重新安装时丢失您的策略文件，**强烈建议不要将策略文件保存在软件安装目录下**。推荐的保存位置：
   >
   > - **用户策略目录**（推荐）：软件会自动为您创建专用的策略目录
   >   - Windows: `C:\Users\<USER>\AppData\Local\KhQuant\strategies\`
   > - **自定义目录**：如 `D:\MyStrategies\` 或 `C:\Users\<USER>\Documents\KhQuant\`
   >
   > **如何使用用户策略目录：**
   >
   > 1. 在策略文件选择对话框中，默认会打开用户策略目录
   > 2. 首次使用时，系统会自动复制默认策略文件到此目录
   > 3. 您也可以手动导航到该目录进行文件管理
   >
5. 在策略文件选择对话框中，建议选择用户策略目录中的 `MACD.py` 文件。如果您是首次使用，系统已经自动将示例策略复制到了用户目录中。

### 4.1.3 保存配置

> 💡 **提示**：设置好正确的策略路径后，可以点击工具栏上的 **"配置另存为"** 按钮覆盖原有的 `demoMACD.kh` 文件。这样，下次加载时就不再需要重新选择了。同样建议将 `.kh` 配置文件也保存到用户目录或自定义目录中，而不是软件安装目录。

加载成功并设置好策略路径后，主界面参数会自动填充完毕。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/加载后的参数.png" />
</p>

<p align="center">加载并配置好工程文件后的主界面</p>

> 💡 **什么是 `.kh` 文件？**
>
> `.kh` 文件是"看海量化交易系统"的**工程配置文件**。它就像是工作台的快照，完整地保存了在图形界面上所做的所有设置，包括选择的策略脚本、回测时间、股票池、交易费率等等。
>
> **重点在于，`.kh` 文件本质上是一个可读的文本文件（使用JSON格式）。** 这意味着完全可以用任何文本编辑器（如记事本、VS Code等）打开它，来查看甚至直接修改其中的配置参数。

## 4.1.4 策略文件管理最佳实践

### 🛡️ 文件安全保护机制

软件采用了智能的文件保护机制：

1. **自动目录创建**：首次运行时，系统会自动创建用户策略目录
2. **默认策略复制**：将内置的示例策略复制到用户目录，但不会覆盖已存在的文件
3. **智能路径提示**：选择策略文件时，如果文件不在安全目录中，系统会提醒您
4. **升级保护**：用户目录中的文件在软件升级时完全不受影响

### 🔧 管理策略文件

1. **访问策略目录**：通过策略文件选择对话框导航到用户策略目录
2. **手动打开目录**：直接在文件管理器中导航到用户策略目录路径
3. **管理策略文件**：您可以在此目录中：
   - 查看和编辑现有策略文件
   - 添加新的策略文件
   - 创建策略文件的备份
   - 组织策略文件的目录结构

### ⚠️ 避免的操作

- **不要**将策略文件保存在软件安装目录（如 `C:\Program Files\KhQuant\`）
- **不要**直接修改软件安装目录中的示例文件
- **不要**将重要的策略文件只保存一份，建议定期备份

### 💡 迁移现有策略

如果您已经在软件安装目录中创建了策略文件，建议按以下步骤迁移：

1. 手动打开用户策略目录（Windows: `C:\Users\<USER>\AppData\Local\KhQuant\strategies\`）
2. 将您的策略文件复制到用户策略目录中
3. 重新加载配置文件，并重新选择策略文件路径
4. 保存配置文件到用户目录或其他安全位置

## 4.2 准备回测数据（数据补充）

在启动回测之前，需要确保本地数据库中包含了策略所需的全部历史数据。本次示例中的`MACD`策略在计算指标时，不仅需要用到分钟数据，还需要日线数据作为参考。因此，我们需要将这两个周期的数据都补充到本地。

> ⚠️ **重要提醒：数据补充检查**
>
> **请特别注意：** 即使本地数据库中没有足够的历史数据，回测程序依然能够启动并正常运行，但这可能导致策略计算结果不准确或产生错误的回测报告。因此，在编写和运行回测程序时，务必仔细检查数据是否已经被正确补充到本地。
>
> **对于本次演示的MACD策略，特别需要确保以下两种周期的数据都已完整补充：**
>
> - **分钟数据**：用于策略的主要交易逻辑计算
> - **日线数据**：用于移动平均参数计算
>
> 缺失任何一种周期的数据都可能影响策略的正确运行和回测结果的成功。

1. 在主界面顶部工具栏点击 **"数据"** 按钮，打开数据中心模块。
2. 在数据中心，我们首先补充 **分钟数据**，设置如下：
   * **股票列表**: 在"自选清单"中输入 `002945.SZ,华林证券`，并勾选它。【这里应该是点击"自选清单"，然后在文本编辑器，比如记事本中输入002945.SZ,华林证券，然后保存，然后勾选自选清单】
   * **周期类型**: 勾选 `1分钟`。
   * **日期范围**: 设置开始日期为 `2024-04-03`，结束日期为 `2024-11-01`。
3. 点击 **"补充数据"** 按钮，等待分钟数据下载完成。
4. 接下来补充 **日线数据**。保持其他设置不变，仅将 **周期类型** 的勾选项从 `1分钟` **改为** `1日`。
5. 再次点击 **"补充数据"** 按钮，下载日线数据。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/补充数据.gif" />
</p>

<p align="center">数据补充模块的设置方式</p>

等待两个周期的数据都补充完成的提示出现后，就可以进行下一步的回测了。

### "数据补充"与"数据下载"的区别

> **"数据补充"** 的设计目标是服务于 **系统内部** 的回测功能。它的核心任务是更新和完善 MiniQMT 内部所依赖的历史数据库，为策略回测引擎提供坚实的数据基础。换句话说，回测系统中使用到的数据，通常不是临时从网络获取的，而是通过"数据补充"提前下载到本地的。这样做可以极大提升回测速度。技术上讲，"数据补充"调用的是xtquant的`download_history_data`函数，而回测过程中则通过`get_market_data_ex`来高速读取本地数据。

> **"数据下载"** 的核心在于将金融数据以独立、可见的 **`.csv` 文件** 形式提供给用户，支持 **系统外部** 的多元化数据应用。用户可以方便地将数据导入 Excel、Python、R 等进行复杂的统计建模或外部回测。

## 4.3 开始回测与观察

当工程文件加载完毕，数据也准备就绪后，就可以启动回测了。

1. 点击工具栏上的 **"开始运行"** 按钮。
2. 此时，回测开始进行，可以将注意力转移到界面的以下两个区域：

   <p align="center">
       <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/bandicam-2025-06-12-01-28-29-193.gif" />
   </p>

   * **右侧的"系统日志"面板**：这里会实时滚动输出策略运行的详细信息，包括数据下载进度、策略初始化状态、交易信号的触发、订单的委托与成交等。如果出现任何问题，错误信息也会在这里以醒目的颜色显示。
   * **底部的状态栏**：这里会显示一个详细的进度条，告知当前回测进行到了哪一天，以及总体的完成百分比。

## 4.4 解读第一个回测报告

等待回测进度条走到100%，系统日志中也会提示"回测运行结束"。此时，回测报告将自动弹出。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/回测结果分析.png" />
</p>

<p align="center">回测报告窗口</p>

如果后续不小心关闭了报告窗口，可以随时点击日志面板上方的 **"打开回测指标"** 按钮来重新打开它。

这份报告浓缩了策略在历史数据中的全部表现，主要包含：

* **核心绩效指标**：如总收益率、年化收益、最大回撤、夏普比率等。
* **可视化图表**：包括策略净值与基准对比的资金曲线、回撤曲线等。
* **详细交易记录**：每一笔买入和卖出的明细。
* **每日持仓与资产快照**：方便复盘策略在任何一天的具体状态。

关于如何深入解读这份报告中的每一项数据，我们将在后续的【第九章：策略绩效复盘】中进行详尽的拆解。现在，只需要对它有一个初步的印象即可。

恭喜！已经成功完成了在KHQuant中的第一次策略回测。接下来，可以尝试更深入的策略研究了。

## 4.5 重要提醒：保护您的策略文件

在结束本章之前，再次强调策略文件安全的重要性：

> ⚠️ **策略文件安全检查清单**
>
> 在继续使用软件之前，请确认：
>
> - ✅ 已了解用户策略目录的位置
> - ✅ 已确认策略文件选择对话框默认打开用户策略目录
> - ✅ 重要的策略文件已保存在安全位置（非软件安装目录）
> - ✅ 已为重要策略文件创建备份
> - ✅ 配置文件（.kh文件）也保存在安全位置
>
> **记住：软件安装目录中的文件在升级时可能会被覆盖！**

通过遵循这些最佳实践，就可以安心地使用KHQuant进行策略开发，而不用担心重要文件的丢失。 

---

# 第五章：主界面巡览：功能区详解

欢迎来到"看海量化交易系统"（KHQuant）的主控界面。本章将对主界面进行一次全面的巡览，概要介绍各个功能区的布局与作用。这是一个整体性的介绍，对于左侧的核心配置区、中间的运行驱动区以及右侧的信息反馈区，我们将在随后的第六、七、八章中进行更深入的拆解说明。熟悉主界面的整体布局，是高效施展策略的第一步。

> **重要提示：关于运行模式**
>
> 当前版本的"看海量化交易系统"专注于提供强大、易用的 **回测 (Backtesting)** 功能，旨在帮助用户在历史数据上验证和优化自己的交易策略。因此，软件界面和功能均围绕回测模式进行设计。
>
> **实盘交易 (Live Trading) 功能目前暂不支持。**

---

## 5.1 整体布局一览

首次打开软件，会看到一个精心组织的三栏式布局界面。这种设计的目的是将策略配置、运行监控和状态反馈等核心操作流程清晰地分隔开，从而一目了然地找到所需功能。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/主界面概览.png" alt="主界面概览" width="100%" />
</p>

主界面主要由以下几个部分构成：

* **顶部工具栏**: 位于界面最上方，集成了最高频使用的全局操作，如配置文件的加载/保存、策略的启停、以及打开辅助工具等。
* **左侧面板 (核心配置区)**: 这里是定义策略行为的核心区域。可在此指定策略文件、设置回测参数、管理股票池等。
* **中间面板 (运行驱动区)**: 该区域负责定义策略的"心跳"—即由什么事件来驱动策略逻辑的执行。可设置触发方式、配置盘前盘后任务，并查看账户的资金与持仓状况。
* **右侧面板 (信息反馈区)**: 这是观察系统运行状态的窗口。系统日志、策略中打印的信息、交易委托与成交回报、错误警报等都会在这里显示。
* **底部状态栏**: 位于界面最下方，提供实时的操作状态反馈和回测进度。

---

## 5.2 工具栏按钮说明

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/工具栏.png" alt="工具栏" width="100%" />
</p>

顶部工具栏是进行快速操作的捷径，以下是每个按钮的功能详解：

* **加载配置**: 点击后加载一个之前保存的 `.kh` 配置文件，快速恢复所有参数设置。
* **保存配置**: 将当前所有参数设置保存到当前加载的 `.kh` 文件中。
* **配置另存为**: 将当前所有设置保存为一个新的 `.kh` 文件。
* **开始运行**: 根据当前配置，启动策略回测。
* **停止运行**: 手动停止当前正在运行的策略。
* **数据**: 打开数据中心，用于补充和管理回测所需的历史数据。
* **设置**: 打开"软件设置"对话框，配置MiniQMT路径等全局参数。
* **MiniQMT状态指示灯**: 显示与MiniQMT的连接状态（🟢已连接 / 🔴未连接）。
* **帮助 (?)**: 打开在线教程文档。

---

## 5.3 左侧核心配置区

这是进行策略回测设置的起点，所有参数都在此集中配置。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/左侧面板.png" alt="左侧面板" width="50%" />
</p>

* **策略配置**:

  * **策略文件**: 点击"选择策略文件"按钮，选择编写的Python策略脚本 (`.py`文件)。
  * **运行模式**: 当前版本专注于 **回测** 功能。此选项固定为回测模式，用于在历史数据上验证策略表现。
* **回测参数**:

  * **基准合约**: 设置用于计算Alpha、Beta等相对表现指标的业绩基准，例如 `sh.000300`。
  * **交易成本设置**: 精确模拟真实交易成本。
    * **最低佣金(元)**: 单笔交易佣金的最低收费。
    * **佣金比例**: 按成交金额计算的佣金费率。
    * **卖出印花税**: 按卖出金额计算的印花税率。
    * **流量费(元/笔)**: 部分券商收取的额外通讯费用。
    * **滑点类型/滑点值**: 设置买卖时价格的滑点，可选"按最小变动价位数"或"按成交额比例"。
  * **回测时间设置**: 通过"开始日期"和"结束日期"选择器，设定回测的时间区间。
* **数据设置**:

  * **复权方式**: 选择K线数据的复权类型，如"不复权"、"前复权"、"等比前复权"。
  * **周期类型**: 选择策略运行所依赖的数据周期，如 `tick`, `1m`, `5m`, `1d`。
  * **数据字段**: 根据所选周期，勾选策略在 `handle_bar` 函数中需要用到的具体数据字段（如开盘价、收盘价、成交量等）。
* **股票池设置**:

  * **常用指数**: 快速勾选A股主要指数成分股作为股票池。
  * **自选清单**: 使用自选股列表。
  * **手动管理**: 在下方的表格中直接添加或删除股票代码，或通过右键菜单导入/清空列表。

---

## 5.4 中间运行驱动区

本区域负责定义策略的触发机制和账户信息。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/中间面板.png" alt="中间面板" width="50%" />
</p>

* **触发方式设置**:

  * **触发类型**: 定义策略核心逻辑 (`handle_bar`函数) 的执行频率。
    * **Tick触发**: 每个Tick数据到达时都执行一次策略。
    * **K线触发**: 在每个K线周期（如1分钟、5分钟）形成时执行一次策略。
    * **自定义定时触发**: 按设定的特定时间点列表来执行策略。
* **账户信息**:

  * **虚拟账户**: 在回测模式下，可在此设置策略的"初始资金"和"最小交易量"。
* **盘前盘后触发设置**:

  * 勾选并设置时间，可以在每日开盘前或收盘后，自动执行策略中相应的 `khPreMarket` 或 `khPostMarket` 函数，用于执行盘前准备或盘后复盘等任务。

---

## 5.5 右侧信息反馈区

这里是观察策略运行过程和结果的主要窗口。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/右侧面板.png" alt="右侧面板" width="50%" />
</p>

* **系统日志**:

  * 一个实时滚动的文本框，显示软件的运行状态、策略中 `print()` 的内容、交易委托和成交的详细回报、以及任何错误或警告信息。不同级别的日志会用不同颜色标记，方便快速识别。
* **日志操作**:

  * **日志类型过滤**: 通过勾选 `DEBUG`, `INFO`, `WARNING`, `ERROR`, `TRADE` 等复选框，可以筛选想看的日志级别。
  * **清空日志**: 清除当前日志显示。
  * **保存日志**: 将当前显示的日志内容导出为文本文件。
  * **测试日志**: 点击后会生成一些各种级别的测试日志，用于检查显示是否正常。
  * **打开回测指标**: **回测结束后，此按钮会变为可用状态**。点击它，即可打开详细的回测报告窗口，对策略绩效进行全面复盘。

---

## 5.6 底部状态栏

界面最底部的状态栏提供实时的上下文信息。

* **左侧：当前状态文本**: 用简短文字描述软件正在进行的操作（如"准备就绪", "策略运行中..."）。
* **右侧：进度条**: 在回测进行时，会激活并直观地展示回测的完成进度。

至此，对"看海量化交易系统"主界面的概览就完成了。在下一章，我们将深入探索左侧的核心配置面板，学习如何为策略配置各项参数。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/底部状态栏.png" alt="底部状态栏" width="100%" />
</p>

---

# 第六章：核心配置项：左侧面板精解

左侧面板是"看海量化交易系统"的策略配置中枢。在这里，将定义策略的灵魂（策略文件）、仿真的环境（回测参数）、必需的数据（数据设置）以及执行的范围（股票池）。本章将逐一详解这些配置项，帮助精确地掌控策略的每一个细节。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/左侧面板.png" alt="左侧面板" width="50%" />
</p>

---

## 6.1 "策略配置"组：指定策略的大脑

这是所有设置的起点，需要在这里告诉系统要运行哪个策略。

* **策略文件 (Strategy File)**
  * **功能**: 通过点击 **选择策略文件** 按钮，可以从本地文件夹中加载编写的Python策略脚本（必须是 `.py` 结尾的文件）。这个文件是策略的核心，包含了所有的交易逻辑。
  * **要求**: 框架通过动态加载此模块来执行策略。因此，策略文件需要符合一定的规范，例如包含 `khHandlebar` 等关键的回调函数。关于策略编写的详细规范，请参阅第十二章《核心驱动：策略编写指南》。
* **运行模式 (Run Mode)**
  * **功能**: 当前版本专注于回测功能，此选项固定为 **回测(Backtest)** 模式。
  * **模式说明**: 回测是量化交易的基石。它使用历史数据来模拟策略的过往表现，从而可以在不产生任何真实风险的情况下，检验和评估策略的有效性。

## 6.2 "回测参数"组：精雕细琢的模拟环境

此区域的设置核心目的是尽可能真实地模拟历史交易环境，从而让回测结果更具参考价值。

* **基准合约 (Benchmark)**
  * **功能**: 设定一个业绩比较基准，通常是市场主流指数。系统会根据此基准计算策略的Alpha（超额收益）、Beta（市场相关性）等关键绩效指标。
  * **如何设置**: 直接在文本框中输入想作为基准的合约代码。沪深300指数最为常用，目前系统也仅调试适配了沪深300。
  * **常见示例**: `sh.000300` (沪深300)。
* **交易成本设置 (Transaction Costs)**
  * **功能**: 精确模拟交易中产生的各项费用。忽略交易成本的回测报告是毫无意义的，因为它会系统性地高估策略表现。关于交易成本的详细构成，推荐阅读[这篇文章](https://zhuanlan.zhihu.com/p/29310540747)。
  * **参数详解**:
    * **最低佣金(元)**: 许多券商对单笔交易设有5元的最低佣金收费，即使按比例计算的佣金不足5元，也会按5元收取。
    * **佣金比例**: 根据券商费率设置，例如万分之一，则应填入 `0.0001`。
    * **卖出印花税**: 目前A股为单向收取，仅在卖出时征收，税率为千分之0.5，应填入 `0.0005`。
    * **流量费(元/笔)**: 部分券商可能会对每笔交易收取固定的信息服务费或流量费，具体费率请向您的开户券商确认。
* **滑点设置 (Slippage)**
  * **功能**: 在真实交易中，由于市场流动性、订单执行速度等因素，最终成交价格与策略的理想委托价格之间常存在微小差异，这种差异就是**滑点**。在高频交易或流动性差的品种上，滑点是影响策略盈利的关键因素，因此在回测中必须对其进行模拟。本软件提供以下两种滑点模拟方式：
  * **滑点模拟方式**:
    1.  **按最小变动价位 (Tick模式)**: 这种方式模拟的是因价格跳动导致的滑点，适合对市场微观结构有深入理解的投资者。
        *   **原理**: 对于A股股票，最小变动价位是0.01元。若在"滑点值"中设为 `1`，则系统在计算成交时，会自动将买入价在委托价基础上上浮0.01元，将卖出价下浮0.01元，以此模拟一个"更差"的成交价格。
    2.  **按成交金额比例 (Ratio模式)**: 这种方式模拟的是因冲击成本等因素产生的滑点，更简单直观，适合大多数投资者。
        *   **原理与双边计算**: 系统采用的是**双边滑点**模型。您在界面上设定的比例值会被视为买卖双边的总滑点。在单次交易中，系统会**将这个比例除以2**后应用到成交价上。
        *   **示例**: 若在"滑点值"中设为 `0.001` (即0.1%)：
            *   对于**买入**订单，实际成交价会是 `委托价 * (1 + 0.001 / 2)`，即价格上浮 `0.05%`。
            *   对于**卖出**订单，实际成交价会是 `委托价 * (1 - 0.001 / 2)`，即价格下浮 `0.05%`。
        *   **价格取整**: 计算出的新价格会**四舍五入到小数点后两位**（即精确到分），以模拟真实的报价机制。例如，一个理想买入价为10.00元的订单，在0.1%的双边滑点下，计算出的新价格是 `10.00 * (1 + 0.0005) = 10.005`，四舍五入后为 `10.01`元。
        *   **使用建议**: 滑点的大小与股票的流动性密切相关。对于大盘蓝筹股，滑点可能很小；而对于小盘股或冷门股，滑点可能远大于0.1%。建议根据交易标的的特性调整滑点参数，以获得更准确的回测结果。
  * **如何设置**: 在"滑点类型"下拉框中选择一种模式，然后在右侧的"滑点值"输入框中设定相应的数值。

## 6.3 回测周期设置

* **功能**: 通过"开始日期"和"结束日期"这两个日历控件，可以精确设定回测的起止时间范围。
* **使用建议**: 为了全面评估策略的稳健性，建议选择足够长的时间段，并确保该时段覆盖了牛市、熊市和震荡市等多种不同的市场环境。

---

## 6.4 "数据设置"组：策略的数据食粮

在回测过程中，系统会读取预先补充好的本地数据文件。本组设置决定了在回测时具体读取哪些数据，以及如何对数据进行加工（如复权）。

* **复权方式**:
  *   复权是指为了消除因分红、送股、配股等除权除息（XD）事件导致股价图上出现的价格"跳空"缺口，而对历史股价进行重新计算的过程。这能让技术指标和股价走势保持连续性，从而更真实地反映股票的长期价值增长。软件提供以下几种复权方式：

    > **1. 前复权**
    >
    > **介绍**：前复权就是以目前股价为基准，保持现有价位不变，缩减以前价格，使图形吻合，保持股价走势的连续性。简单说就是把除权前的价格按现在的价格换算过来，复权后现在价格不变，以前的价格减少。
    >
    > **举例来说**：假设A股票10元/股，因除权等原因变成5元/股，在当日股价没有涨跌的情况下，选择前复权后，当天与前一天的股价都是5元/股，之前的股价都会按照一定比例缩小。
    >
    > **作用**：采用前复权历史股价就可以更加准确地反映出股票的涨跌幅和收益情况。

    > **2. 不复权**
    >
    > **介绍**：不复权是指在股票交易中，不考虑除权、除息等事件对股价的影响，直接以当天的实际交易价格作为收盘价进行计算。这样做会导致历史数据的断层，无法准确反映出股票的真实涨跌幅和收益情况。
    >
    > **例如**：如果一只股票在某个日期发生了除权或除息事件，假设这个事件使得股价下跌10%，那么不复权的情况下，历史数据将会按照该事件当天的实际价格进行计算，而不会考虑到除权除息事件带来的影响。
    >
    > **作用**：采用不复权，K线图能真实反映股价历史的除权信息。

    > **3. 后复权**
    >
    > **介绍**：后复权是指在K线图上以除权前的价格为基准来测算除权后股票的市场成本价。简单说就是把除权后的价格按以前的价格换算过来，复权后以前的价格不变，现在的价格增加。
    >
    > **举例来说**：假设A股票10元/股，因除权等原因变成5元/股，在当日股价没有涨跌的情况下，选择后复权后，当天与前一天的股价都是10元/股，之后的股价都会按照一定比例放大。
    >
    - **作用**：采用后复权能够看出股票真实价值的增加及持股者的真实收益率。
    
    > **4. 等比前复权 / 等比后复权**
    >
    > 与常规的前/后复权算法类似，但在价格调整时采用不同的数学模型。在大多数场景下，其效果与普通复权非常接近。

* **周期类型**:
  *   **定义**: 选择策略运行所依赖的数据更新频率。不同的周期适用于不同类型的策略。例如，高频交易策略可能依赖Tick数据，而日内趋势策略可能使用分钟线。
* **数据字段 (Data Fields)**
  * **功能**: 此区域的复选框列表让可以精确选择策略在运行时需要哪些数据。例如，一个简单的均线策略可能只需要收盘价 `close`，而一个复杂的因子模型可能需要开、高、收、低、成交量、成交额等多个字段。
  * **优化作用**:

    > 💡 **小贴士**：请仅勾选策略中确定会用到的字段。这样做有两个好处：
    >
    > 1. **减少内存占用**: 系统无需加载和存储不需要的数据。
    > 2. **加快数据读取速度**: 在进行大规模数据回测时，效果会非常明显。
    >
  * **动态变化**: 可勾选的字段列表会根据在"周期类型"中选择的周期动态变化。例如，Tick周期和K线周期所能提供的数据字段是不同的。

## 6.5 "股票池设置"组：圈定执行范围

股票池定义了策略将在哪些证券范围内进行观察和交易。系统提供了灵活多样的股票池构建方式。

* **常用指数成分股 (Common Index Constituents)**
  * **功能**: 这是最快捷的股票池构建方式。只需勾选相应的复选框，即可将A股市场主流指数（如上证50、沪深300、中证500、创业板指、科创50、上证A股、沪深A股）的全部成分股一次性加入到股票池中。
* **自选清单 (Custom List)**
  * **功能**: 点击 **"自选清单"** 这几个字，系统会用默认文本编辑器（如记事本）打开一个`csv`文件，勾选此项后，系统会加载该文件中的自定义股票列表。
  * **如何编辑**: 在打开的文本文件中编辑股票列表，每行一只股票，编辑完成后直接保存文件即可。请确保文件格式正确：每行格式为`股票代码,股票名称`，例如：
    ```csv
    600036.SH,招商银行
    000001.SZ,平安银行
    688981.SH,中芯国际
    ```
* **手动管理列表 (Manual Management)**
  * **功能**: 当需要进行临时的、更灵活的股票池管理时，可以使用此功能。下方的表格提供了一个可视化的股票列表。
  * **操作方法**:
    * **添加股票**: 直接在表格的"股票代码"列的空白行中输入股票代码，然后按回车键，系统会自动填充股票名称。
    * **删除股票**: 单击选中想删除的一行或多行（按住`Ctrl`可多选），然后按键盘上的 `Delete` 键即可。
    * **导入列表**: 在表格区域 **单击鼠标右键**，会弹出一个上下文菜单，选择 **导入股票列表**。可以从一个 `.csv` 文件（每行一个代码）批量导入股票。请注意，导入文件中的格式也应为`股票代码,股票名称`。
    * **清空列表**: 在右键菜单中选择 **清空所有**，可以快速删除当前手动管理列表中的所有股票。

> ✨ **组合使用**: 上述几种方式可以组合使用。例如，可以先勾选"沪深300"，然后再手动添加几只特别关注的、不在沪深300内的股票。最终的股票池是所有选定方式的并集。

至此，我们已将左侧核心配置面板的每一个角落都探索完毕。掌握了这些设置，就拥有了为策略量身打造运行环境的能力。在下一章，我们将移步至中间面板，探索策略的"心跳"—触发机制。

---

# 第七章：中间运行驱动区：定义策略的心跳

中间面板是策略的"运行驱动区"，其核心任务是定义策略逻辑（即 `khHandlebar` 函数）被调用的频率和时机。可以说，这里决定了策略的"心跳"。本章将详细介绍三种不同的触发方式，并说明如何配置账户信息及盘前盘后任务。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/中间面板.png" alt="中间面板" width="50%" />
</p>

---

## 7.1 触发方式设置

触发方式定义了策略的执行频率。KHQuant提供三种模式，以适应不同类型策略的需求。


<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/触发方式列表.png" alt="中间面板" width="50%" />
</p>


### 一、Tick触发：灵活但负担重

Tick触发在理论上最为简单，它监听每一笔市场成交数据(在MiniQMT中实际是3秒一次快照)，并在数据到达时执行策略。这为策略的编写带来了很大的空间，在策略中可以灵活地添加过滤机制，或者说第二层触发判断，当真正达到触发条件时，再执行策略。第二层触发判断用户在编写时就有了极大的发挥空间。

这种方式的缺点是回测数据量大，因为需要下载所有tick数据——即使有第二层触发条件，这使策略执行频率不会太高。

### 二、K线触发：趋势策略的可靠选择

K线触发基于固定时间周期的数据，在回测环境中实现简单，只需补充对应周期的K线数据即可。

在看海回测系统中，支持1分钟和5分钟这两种K线周期。这并非功能限制，而是当前依赖的MiniQMT非投研版仅支持这两种分钟级别的实时数据订阅。

> 💡 **实盘与回测的触发差异**
>
> 值得注意的是，在实盘环境中，即便是订阅`1m`或`5m`的K线数据，行情接口的推送频率通常也是3秒一次，每次推送的都是当前时间点最新的完整K线数据。因此，若想在实盘中严格实现"每1分钟或5分钟K线走完后才触发一次"，反而需要框架层或策略层编写额外的逻辑来支持。回测模式下则严格在K线结束后执行。

K线触发由于数据量较小，系统资源消耗低，回测速度快，特别适合中长期趋势策略和技术分析策略，但这取决于您的具体策略设计。

### 三、自定义时间触发：增加自由度

自定义时间触发是KHQuant框架的关键功能，它允许用户指定精确的触发时间点，系统会在这些时间点到达时执行策略。

自定义时间触发特别适合定时交易策略，如开盘集合竞价策略、收盘前交易策略等，也适用于系统预设K线周期之外的场景(比如每10分钟，每小时等)。它为策略开发者提供了精确控制交易时机的能力。

> ⚠️ **注意：自定义触发的数据处理**
>
> 需要注意，自定义时间触发仅仅是"触发"策略的运行，**它不会像Tick或K线触发那样，自动向策略的 `khHandlebar` 函数中传入当时的数据**。策略需要在函数内部自行调用数据获取接口（如`get_market_data`）来获取所需数据。
>
> 这样设计是因为自定义时间点的前置数据需求多种多样，难以统一输入标准。同时，使用此类型触发的策略，其需求也往往比简单的K线数据更为复杂。

#### 智能数据适配与时间点生成

为了优化性能，系统会自动分析用户设定的时间点特性：

* 当所有触发时间点都是**整分钟**时（如09:30:00, 10:00:00），系统自动使用1分钟K线数据作为基础。
* 当存在**非整分钟**时间点时（如09:30:15, 10:05:45），系统则会切换到底层的Tick数据来确保精度。

这种智能适配在保证策略执行精度的同时，显著优化了系统资源使用和回测效率。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/触发方式.png" />
</p>

为了方便使用，我专门设计了一个**自定义时间生成模块**。通过设定起始、结束时间以及时间间隔，可以一键生成规范的触发时间列表。当然，也可以根据自己的需求在文本框中逐个手动编辑。

> 💡 **时间点精度提示**
>
> 由于MiniQMT的数据快照特性，所有时间点需设置为3的整数秒，以确保触发的稳定性和精确性。时间点生成工具已自动处理此逻辑。

---

## 7.2 账户信息

由于当前版本专注于**回测**功能，此区域的功能也相应简化：

* **初始资金**: 在此设置回测开始时策略所拥有的虚拟资金总额。
* **最小交易量**: 此项设置保留，但在当前的回测逻辑中并未实际启用限制。


<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/账户信息.png" />
</p>


---

## 7.3 盘前盘后触发设置

本功能允许策略在每日的特定时间点执行一些常规的、非核心交易逻辑的任务。


<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/盘前盘后触发设置.png" />
</p>


* **盘前任务 `khPreMarket`**: 勾选并设置时间（如 `09:25:00`），系统会在每个交易日的指定时间，自动调用策略代码中的 `khPreMarket` 函数。这通常用于执行开盘前的准备工作，例如：
  * 获取当日的股票池。
  * 取消所有昨日未成交的挂单。
  * 重置当日的状态变量。
* **盘后任务 `khPostMarket`**: 勾选并设置时间（如 `15:05:00`），系统会在每个交易日的指定时间，自动调用策略代码中的 `khPostMarket` 函数。这通常用于执行收盘后的复盘和清理工作，例如：
  * 统计当日交易情况。
  * 记录当日的持仓和资产快照。
  * 为第二天的交易进行数据预处理。

在下一章，我们将讲解右侧的信息反馈区，学习如何通过日志和回测报告来观察和分析我们的策略。

---

# 第八章：洞察运行：右侧信息反馈区

右侧面板是您观察策略思想与市场现实碰撞过程的"黑匣子"。它忠实地记录了从软件启动到策略运行的每一个关键步骤。学会解读这里的日志信息，是您调试策略、理解行为、发现问题的核心技能。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/右侧面板.png" alt="右侧面板" width="50%" />
</p>

---

## 8.1 日志系统的核心构成

本软件的日志系统设计围绕两大核心组件展开，两者相辅相成，共同构成了完整的日志监控体系：

* **后台文件日志 (`app.log`)**：作为最详尽的记录载体，它捕捉软件运行期间的各类事件，为深度问题排查提供依据。
* **前台界面日志**：一个可视化的窗口，实时展示关键的运行状态、策略输出和交易活动，提供直观的监控体验。

### 8.1.1 后台文件日志：完整记录运行轨迹

软件在启动时，会自动在程序根目录下的文件夹内创建或覆盖一个名为 `app.log` 的文本文件。这个文件是系统运行状态最详尽的记录载体。它会捕捉软件从启动到退出的整个生命周期内的各类事件，包括但不限于配置文件的加载与保存、策略的启动指令、运行状态变化以及最终的停止信号。

更重要的是，当软件遭遇预期之外的异常时，详细的错误信息和程序执行堆栈（Traceback）会被完整地记录下来，为问题排查提供了关键线索。

> 💡 **日志级别与覆盖规则**
>
> 为了确保信息的全面性，文件日志默认记录 `DEBUG` 及以上所有级别的信息。需要注意的是，该日志文件在**每次软件重新启动时会被重写**，因此只保留当前运行周期的日志。若需长期存档，建议在关闭软件前手动备份该文件。

### 8.1.2 前台界面日志：实时状态的可视化窗口

为了让用户能够直观、实时地掌握软件的运行状态和策略执行情况，后台记录的关键日志信息会被同步呈现在软件主界面的右侧"系统日志"面板中。这个面板是用户观察软件内部活动的主要窗口，它会实时显示核心的运行状态更新、策略通过 `print` 或日志模块输出的信息、发生的交易行为以及重要的系统警告与错误提示。

---

## 8.2 界面日志的特性与交互功能

用户界面的日志显示区域并非后台日志的简单复刻，它融入了多项旨在提升用户体验和信息获取效率的设计。

### 8.2.1 日志级别与颜色区分

为了帮助用户在信息流中快速抓住重点，不同重要程度的日志被赋予了不同的颜色标识，使得用户能够迅速定位到关键的错误警报或重点关注的交易活动。

* `[DEBUG]` (调试): **浅紫色**，主要用于开发者调试，信息较为冗余。
* `[INFO]` (信息): **白色**，常规运行信息，如启动/停止、配置更改等。
* `[WARNING]` (警告): **橙色**，潜在问题或需要用户注意的情况。
* `[ERROR]` (错误): **红色**，发生了错误，可能影响正常运行。
* `[TRADE]` (交易): **蓝色**，专门用于记录交易相关的委托和成交信息。

### 8.2.2 日志过滤与管理工具

随着软件运行时间的增长，日志信息可能会变得非常庞杂。界面日志区域下方提供了一组复选框和管理按钮，允许用户聚焦核心信息并进行便捷操作。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/日志按钮.png" />
</p>

* **日志过滤**: 通过勾选 `DEBUG`, `INFO`, `WARNING`, `ERROR`, `TRADE` 等复选框，可以动态过滤日志。每次勾选状态的改变都会即时生效，让关键信息一目了然。
* **清空日志**: 点击可以迅速清除当前界面上显示的所有日志内容（此操作不影响后台的 `app.log` 文件）。
* **保存日志**: 点击后，系统会将当前界面显示的（已应用过滤规则的）日志内容导出到一个用户指定的文本文件中，方便存档或分享。
* **测试日志**: 点击会生成几条不同级别的模拟日志，用于快速验证日志系统的显示和颜色功能是否正常。
* **打开回测指标**: 这是通往策略绩效复盘的**核心入口**。此按钮在平时是灰色不可用状态，**只有在一次回测成功运行结束后**才会被激活。点击后将打开详细的回测报告窗口。

### 8.2.3 特殊日志的差异化处理

系统对特定类型的日志进行了特殊处理以优化信息呈现。例如，对于回测过程中的进度反馈（如 "回测进度: 55.3%"），这类信息虽然也会在后台日志中记录，但在界面上并不会直接逐条打印，而是被用来驱动主窗口底部状态栏中的**进度条**进行实时更新。这种处理方式避免了大量进度信息刷屏，同时提供了更为直观、集中的进度展示。

---

## 8.3 在策略中使用日志

为了方便调试和监控策略的内部状态，您可以在策略代码中直接调用日志输出功能，将信息发送到看海量化交易系统的日志系统。

您可以根据需要输出不同级别的日志信息，这些级别与界面上显示的颜色直接对应：

* **普通信息 (INFO)**: 使用 `self.log("进入长仓条件判断", "INFO")` 或类似语句输出常规的流程信息或变量状态。这对应界面上的 **白色** 文本。
* **调试信息 (DEBUG)**: 如果需要输出更详细的、仅在调试时关心的变量值，可以使用 `self.log("当前ATR值", "DEBUG")`。这对应界面上的 **浅紫色** 文本。
* **警告信息 (WARNING)**: 当策略遇到一些非致命但需要注意的情况时，比如某个数据获取失败但有备用方案，可以使用 `self.log("无法获取最新行情，使用上一周期数据代替", "WARNING")`。这对应界面上的 **橙色** 文本。
* **错误信息 (ERROR)**: 当策略发生严重错误，可能导致后续逻辑无法正常执行时，应使用 `self.log("计算指标时出现除零错误", "ERROR")`。这对应界面上最醒目的 **红色** 文本。
* **交易信息 (TRADE)**: 虽然系统会自动记录委托和成交回报，但如果您想在策略逻辑中额外标记关键的交易决策点，也可以使用 `self.log(f"信号触发，准备买入", "TRADE")`。这对应界面上的 **蓝色** 文本。

> **如何输出醒目的内容？**
>
> 如果您希望某条日志信息在界面上特别突出，最直接的方式是使用 `WARNING` 或 `ERROR` 级别。`ERROR` 级别（红色）最为醒目，通常用于指示发生了必须处理的问题。`WARNING` 级别（橙色）也比较突出，适合用于提示潜在风险或需要关注的状态。请根据信息的重要性和紧急程度，审慎选择合适的级别进行输出。

在下一章，我们将深入那个最激动人心的部分——详细解读回测报告，真正开始对您的策略进行定量评估。

---

# 第九章：策略复盘：解读回测"成绩单"

0当.

回测的进度条走到100%，最激动人心的时刻便到来了。系统会自动弹出一个独立的回测报告窗口，这是对您策略在历史长河中表现的全面总结。如果说编写策略是"播种"，那么解读这份报告就是"收获"。本章将带您逐一拆解这份报告，学习如何从这份"成绩单"中洞察策略的优劣。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/回测结果分析.png" alt="回测报告窗口" />
</p>
<p align="center">回测报告窗口概览</p>

---

## 9.1 概览部分：收益曲线与关键指标

打开窗口，最先映入眼帘的就是策略的整体表现总结，它清晰地分为"基本信息"和"资金曲线"两部分，旨在让用户对策略表现快速建立量化认知。

### 9.1.1 基本信息

该区域是策略回测的**数字摘要**，以表格形式清晰列出了超过20项关键绩效指标（KPIs），包括策略名称、回测周期、初始与最终资金、总收益率、年化收益率、最大回撤、夏普比率等。这些精确计算的指标，如同策略的"体检报告"，让您可以快速抓住策略的亮点与短板，为初步评估提供客观依据。

### 9.1.2 资金曲线

资金曲线是报告中最重要的**可视化图表**，它将策略的净值变化过程以"心电图"的形式直观呈现。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/交互式图表.gif" alt="回测报告窗口交互" />
</p>

这张图表集成了丰富的信息维度：

* **多曲线对比**：不仅包含策略本身的净值曲线（蓝色），还绘制了基准指数（如沪深300，橙色）的对比曲线，策略是否跑赢市场一目了然。
* **一体化子图**：下方附带了时间轴完全对齐的多个子图，包括描绘风险深度的"回撤图"（红色区域）、展示每日盈亏的"日收益图"（红绿柱）以及标记具体操作的"买卖点图"（红蓝点）。
* **交互式数据探查**：鼠标在图表区域悬停，即可激活一条跟随光标的垂直标尺，并弹出一个信息框。该信息框会即时显示所指日期的详细数据，包括策略/基准收益、回撤、当日盈亏、成交量等，极大地提升了复盘效率。
* **清晰的视觉标记**：图表中的涨跌、盈亏、买卖点和最大回撤点都采用了不同的颜色进行标记，便于快速识别。

这种将收益、风险、盈亏和交易行为整合在同一视图下的设计，有助于用户更全面、立体地理解策略的动态表现，而无需在不同表格和图表间来回切换。

---

## 9.2 交易记录

窗口下方的"交易记录"标签页，提供了一份详尽的**交易流水账**。它以表格形式清晰记录了策略在回测期间完成的每一笔买入（红色）和卖出（蓝色）操作。内容包括成交时间、股票代码、方向、价格、数量、成交金额及手续费等。这份记录是分析策略具体行为、验证交易逻辑是否按预期执行的重要依据。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/交易记录.png" />
</p>

---

## 9.3 日收益表

"日收益"标签页提供的是账户的**每日快照**。它以表格形式逐日记录收盘后的总资产、持仓市值、剩余现金及当日收益率。其中，日收益率根据盈亏（红/绿）进行颜色区分，便于快速浏览每日的盈亏波动情况。这个表格从数据层面补充了资金曲线的宏观视角，让用户能更细致地观察策略的每日资金变化。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/每日收益.png" />
</p>

---

## 9.4 绩效分析

"绩效分析"标签页提供了两张高级统计图表，旨在帮助用户**深挖策略的内在"性格"和风险特征**。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/绩效分析.png" />
</p>

* **收益分布图**：这张直方图统计了策略所有交易周期的收益率分布情况。通过观察图形的形态（如是否接近正态分布、是否存在"长尾"），可以判断策略收益的稳定性，并评估其发生极端盈利或亏损的隐藏风险。
* **月度收益热力图**：这张图表形如日历，用颜色的冷暖与深浅（绿赚红亏）直观展示策略在不同年份和月份的收益表现。通过它可以快速发现策略是否存在周期性或市场依赖性（例如，是否只在牛市有效，或在特定月份表现不佳）。

这两张图表让用户能从统计学和周期的角度审视策略，发现一些仅从资金曲线上无法察觉的深层信息，从而对策略的风险收益特征有更全面的认知。

至此，您已经掌握了分析回测报告的关键方法。通过这份详尽的"成绩单"，您可以不断迭代、优化您的策略，在量化的道路上走得更远。

---

# 第十章：数据模块：回测的"燃料库"

"兵马未动，粮草先行"。在量化交易中，数据就是策略的"粮草"。看海量化交易系统提供了一个强大的数据中心模块，用于管理回测所需的所有历史数据。本章将首先深入介绍数据中心的设计理念、两大核心机制及其技术实现，随后提供一份详尽的图形化操作指南。

---

## 10.1 数据获取的双重核心

数据中心提供了两种关键的数据获取机制，它们虽然都旨在从市场获取数据，但其设计理念、数据流向及最终应用场景存在显著差异：

* **数据下载**：将数据作为独立的 `.csv` 文件显式存储，主要用于**系统外部**的量化研究。
* **数据补充**：更新和完善MiniQMT**系统内部**的历史数据库，专门服务于内部回测功能。

---

## 10.2 数据下载：显式存储，赋能量化研究

"数据下载"功能的核心在于将金融数据以独立、可见、可直接操作的 `.csv` 文件格式提供给用户。这种设计使得数据变得"触手可及"，为后续的分析研究铺平了道路。

* **应用场景**:
  * **直接检视数据**：在Excel或文本编辑器中检查数据质量、格式及具体数值。
  * **利用外部工具分析**：将数据导入Python（使用Pandas库）、R等进行复杂的统计建模或策略回测原型开发。
  * **与其他系统集成**：作为标准数据格式，方便导入其他研究平台。
* **复权处理**: 该功能支持在下载时直接选择复权方式，允许用户获取基于特定复权逻辑（如前复权、后复权）的CSV数据。

---
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/csv数据文件.png" alt="csv数据文件" />
</p>
<p align="center">csv数据文件</p>

## 10.3 数据补充：无缝集成，驱动内部回测

与"数据下载"不同，"数据补充"功能的设计目标是服务于**看海量化交易系统与MiniQMT本身**。它的核心任务是更新和完善MiniQMT内部所依赖的历史数据库，为策略回测引擎提供坚实的数据基础。

> 💡 **数据调用链路**
>
> 换句话说，回测系统中使用到的数据，并非临时从网络下载，而是通过"数据补充"提前下载到本地的。更具体地说，"数据补充"调用的是xtquant的`download_history_data`函数将数据写入本地；而在回测过程中，策略通过`get_market_data_ex`等函数高速地读取这些本地数据。

执行"数据补充"时，获取的数据并不会以独立文件的形式存储在用户指定的通用目录，而是直接写入**MiniQMT系统自身的数据文件夹**。该路径通常位于QMT安装目录下的 `userdata_mini\datadir` 中。数据的存储格式是内部优化的二进制格式（`.dat`文件），目的是确保最高效地读取和利用。

是在QMT软件安装路径的userdata_mini\datadir当中可以看到：

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/datadir文件夹.png" alt="datadir文件夹" />
</p>
<p align="center">datadir文件夹</p>

其中包括了上交所、深交所股票、权重信息、成分股信息等等。打开"SH"文件夹可以看到有这几个子文件夹：

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/datadir.png" alt="SH文件夹内数据" />
</p>
<p align="center">SH文件夹内数据</p>
其中文件夹的数字代表的是秒，0文件夹中存的是tick数据，60存的是1m数据，300存的是5m数据，86400则是1d数据。再进一步打开则是DAT文件，这就是QMT的二进制数据了。
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/DAT数据.png" alt="DAT数据" />
</p>
<p align="center">DAT数据</p>

---

## 10.4 性能优化：多线程技术化解界面阻塞

无论是下载外部CSV文件还是补充内部数据，这两种操作均涉及大量的网络请求和磁盘I/O，属于典型的耗时任务。为解决早期版本中因此导致的界面长时间冻结（"未响应"）问题，当前数据模块采用了**多线程优化策略**。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/保持响应.png" alt="数据下载时界面保持响应" />
</p>
<p align="center">数据下载时界面保持响应，且可随时停止</p>

* **任务分离**：将耗时的数据处理逻辑移至独立的后台工作线程执行。
* **异步通信**：主界面线程仅负责启动和接收反馈。后台线程通过PyQt的信号将执行进度、结果或错误信息实时发送回主线程，安全地更新进度条、日志等UI元素。
* **中断控制**：用户可在任务执行期间随时点击"停止"按钮，安全地终止后台操作。

这项优化显著提升了用户体验，确保了界面的流畅响应和任务的稳定性。

---

## 10.5 模块演进：从独立工具到集成组件

值得强调的是，当前看海量化交易系统的用户交互重心已是功能更全面的**主回测系统界面**。在此新架构下，数据模块转变为一个核心的功能组件，通过主界面工具栏上的"数据模块"按钮按需调用。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/集成数据模块.gif" alt="从主界面调用数据模块" />
</p>
<p align="center">从主界面调用数据模块的流程演示</p>

---

## 10.6 详细操作指南：数据获取（左侧面板）

本节将为您提供一份关于数据中心的、图文并茂的完整使用指南。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/数据工具界面.png" alt="数据工具界面" />
</p>
<p align="center">数据中心模块界面</p>

### 10.6.1 设置数据存储路径

<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_53f4904034701c34ca8f3015d53c81d0.jpg" style="
    margin: 10px auto;
    display: block;">
</div>

此路径**仅对"数据下载"功能有效**，指定生成的.csv文件存放在何处。您可以直接在输入框粘贴路径，或通过"浏览..."按钮选择。此设置会自动保存，下次无需重复设置。

💡 "数据补充"的路径

"数据补充"功能会直接将数据写入QMT的内部数据目录（通常是 userdata_mini\datadir），而**不受此路径设置的影响**。

### 10.6.2 选择股票范围

<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2025/01/wp_editor_md_5283e251550080f943981f30b89dffa9.jpg" style="
    margin: 10px auto;
    display: block;">
</div>

系统提供了多种方式来定义您想获取数据的股票范围：

* 预设板块/指数：直接勾选"沪深A股"、"沪深300成分股"等常用板块。
* 自选清单：系统提供了一个便捷的"自选清单"功能。勾选此项后，直接点击加粗带下划线的"**<u>自选清单</u>**"文字，即可用记事本打开 otheridx.csv 文件，仿照其中的格式（股票代码,股票名称）编辑和保存您的常用列表。
* 添加自定义列表：点击"添加自定义列表"按钮，可以从您的电脑中选择一个或多个已编辑好的股票列表文件。
* 预览与管理：所有选中的列表都会显示在下方的预览框中，可随时点击"清空列表"来重新选择。

<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_222892d75c5d72d5969849d59af61f8e.jpg" style="
    width: 170px;
    height: 200px;
    margin: 10px auto;
    display: block;">
  <p style="text-align: center; color: #888;"><i>CSV文件内容示例</i></p>
</div>

### 10.6.3 配置数据参数

[![](https://khsci.com/khQuant/wp-content/uploads/2025/01/wp_editor_md_dc886c96ae1a9c0a995aae6f3735d07e.jpg)](https://khsci.com/khQuant/wp-content/uploads/2025/01/wp_editor_md_dc886c96ae1a9c0a995aae6f3735d07e.jpg)

* 周期类型：支持`tick`、`1m`、`5m`和`1d`四种。当您切换周期时，下方的"字段列表"会自动更新。
* 复权方式：此项仅对"数据下载"有效。您可以选择"前复权"、"后复权"等，以获取计算好的复权数据csv文件。执行"数据补充"时，系统始终写入不复权的原始行情。
* 字段选择：根据您的需求勾选要获取的数据字段。Tick周期和K线周期所支持的字段有所不同。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_ff150ba4d68fc7e33347f5001adf231f.jpg" alt="Tick周期字段" />
    <br>
    <i>Tick周期可选字段</i>
</p>

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_5b6035b9fde5b20e039af2db5c29f1cb.jpg" alt="K线周期字段" />
    <br>
    <i>K线周期可选字段</i>
</p>

### 10.6.4 设定时间范围

[![](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_01391588c89f49f9cbf82a8adb993e26.jpg)](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_01391588c89f49f9cbf82a8adb993e26.jpg)

您可以通过日历控件选择一个起止日期，并可选择是获取"全天"数据，还是"指定时间段"（如`09:30`-`10:00`）的数据。当选择全天时，数据是从9:15集合竞价阶段开始下载。

### 10.6.5 执行任务与监控

[![](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_18dfc491366e2c0bdd7228034f6a87ef.jpg)](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_18dfc491366e2c0bdd7228034f6a87ef.jpg)

* 启动任务：配置完成后，点击 **下载数据** 或 **补充数据** 按钮即可开始。
* 监控状态：
  * 界面不阻塞：得益于多线程技术，无论是下载还是补充数据，**界面都会保持流畅响应**，不会出现"未响应"状态。
  * 随时中断：任务开始后，按钮会变为红色的"停止"按钮，您可以随时点击它来安全地终止当前任务。
  * 进度条：下方的进度条会实时反馈任务完成的百分比。
  * 状态栏：界面最底部的状态栏会滚动显示详细的日志信息，如"正在下载 xxx 的数据..."，或报告下载错误。

[![](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_6cc17fa9c0dcea6162ee3e09a2155014.jpg)](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_6cc17fa9c0dcea6162ee3e09a2155014.jpg)

💡 **下载文件命名规则**

> 文件命名规则:
> – 存储的文件名格式: "{股票代码}{周期类型}{起始日期}{结束日期}{时间段}_{复权方式}.csv"
> – 示例1: "000001.SZ_tick_20240101_20240430_all_none.csv"
> – 股票代码: 000001.SZ
> – 周期类型: tick
> – 起始日期: 20240101
> – 结束日期: 20240430
> – 时间段: all (表示全部时间段)
> – 复权方式: none (表示不复权)
> – 示例2: "000001.SZ_1d_20240101_20240430_all_front.csv"
> – 复权方式: front (表示前复权)
> – 如果指定了具体的时间段,时间段部分将替换为 "HH_MM-HH_MM" 的格式
> – 示例: "000001.SZ_1m_20240101_20240430_09_30-11_30_none.csv"
> – 时间段: 09_30-11_30 (表示 09:30 到 11:30 的时间段)
> – 复权方式有以下几种：
> – 'none': 不复权，使用原始价格
> – 'front': 前复权，基于最新价格进行前复权计算
> – 'back': 后复权，基于首日价格进行后复权计算
> – 'front_ratio': 等比前复权，基于最新价格进行等比前复权计算
> – 'back_ratio': 等比后复权，基于首日价格进行等比后复权计算

---

## 10.7 详细操作指南：数据清洗（右侧面板）

数据清洗模块可以说是和历史数据下载模块是一体的，它用于修正您已下载到本地的`.csv`文件。

[![](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_8a8c8869de2bd8a9e10ad0cb40b3ecee.jpg)](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_8a8c8869de2bd8a9e10ad0cb40b3ecee.jpg)

### 10.7.1 待清洗的文件夹选择

此路径会自动与左侧"数据下载"模块的路径相关联，同时也可以手动修改，以增加该模块使用的便捷性和灵活性。

### 10.7.2 选择清洗操作

[![](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_8dc15da1c34486f1d5faf3c3402b7db6.jpg)](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_8dc15da1c34486f1d5faf3c3402b7db6.jpg)

此处按需勾选需要执行的清洗任务。

> ⚠️ **注意**：
>
> * "移除异常值"应该慎重选择，因为它可能会剔除一些极端但有效的行情。
> * 点击"开始清洗"后，程序会直接在原数据上进行修改，即**清洗后的数据将覆盖原数据**。请务必提前备份重要数据。

### 10.7.3 清洗结果日志

程序会生成详细的清洗日志，以便用户确认清理的数据是否符合预期。

<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_8529ee9dfe79906b72c88f4634e96f5f.jpg" style="
    margin: 10px auto;
    display: block;">
</div>

日志内容包括：

* 处理文件的总数量
* 每个文件的具体处理情况（如缺失值填充数量、重复数据删除数量等）
* 被删除数据的具体内容，以便核对
* 清洗完成的时间戳

如果想保存所有日志信息，可以点击"保存清洗日志"按钮，将报告导出为`.txt`文件。

<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_893a1901248804b349cbc3d5bcedda2b.jpg" style="
    margin: 10px auto;
    display: block;">
</div>

---

## 10.8 详细操作指南：数据可视化工具

可视化模块主要为了做两件事：
第一，确认一下下载的数据的总体概况，比如总共有多少只股票的数据、占用的空间等基本信息。
第二，任意选取其中的某一只股票，绘制其数据文件中的各类数据的图线，也就是数据的可视化。

### 10.8.1 可视化模块与主界面的衔接

该功能作为工具模块内置于平台中，通过数据中心顶部的工具栏调用。点击第一个图标，即可弹出此可视化界面。

[![](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_f062f26dbf87b8446ee2ce3fcb6ab0ba.jpg)](https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_f062f26dbf87b8446ee2ce3fcb6ab0ba.jpg)

<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_645afba8e02d81833944e77de56be5e2.jpg" style="
    margin: 10px auto;
    display: block;">
</div>

特别值得一提的是，该模块实现了与主界面的智能联动。它会自动继承主界面当前的数据文件夹路径，这意味着用户在下载完数据后可以直接点击图标进行分析，无需重复设置路径。当然，用户也可以通过界面上的"浏览…"按钮随时切换到其他数据文件夹。

### 10.8.2 文件信息统计

它能自动扫描文件夹，解析每个文件名，并提供全面的数据概览，帮助快速核实数据完整性。

<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_6ecf033f5d328a3855a049c5d386702d.jpg" style="
    margin: 10px auto;
    display: block;">
</div>
统计信息包括：

* 基础信息：股票总数、文件总大小
* 市场分布：深市、沪市、北交所的股票数量统计
* 数据特征：数据周期类型（tick、1m、5m、1d等）
* 时间范围：数据的起止日期
  这些信息直观地展示在界面右上角，帮助用户快速了解数据集的基本特征。这个功能特别适合在批量下载数据后使用，可以帮助快速核实数据的完整性。

### 10.8.3 智能可视化展示

系统会根据数据特征自动调整显示模式：

* **股票选择**：在"股票"下拉菜单中，系统会自动将股票代码解析为股票名称。
* **日期选择**：如果数据是tick、1m或5m级别，会自动生成一个日期选择菜单。
* **智能降采样**：当数据量过大时，系统会自动降采样以保证图表响应流畅。

<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_67858ff5700506888dd7a6cb7bdbc784.jpg" style="
    margin: 10px auto;
    display: block;">
    <p style="color: #888888; margin-top: 5px; margin-bottom: 20px;"><i>日线数据展示</i></p>
</div>

### 10.8.4 交互功能与用户体验

为了提供更好的数据分析体验，系统实现了丰富的交互功能：

* 通过鼠标框选来放大查看特定时间段的数据。
* 右键点击可快速重置视图。
* 鼠标悬停会显示该点的详细数据信息。
* 点击图例可隐藏/显示对应数据曲线。

<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_06285341d8e8d61f4cbf758567d43b2f.jpg" style="margin: 10px auto; display: block;">
  <p style="color: #888888; margin-top: 5px; margin-bottom: 20px;"><i>鼠标悬停显示详细数据</i></p>
</div>

<div align="center">
  <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_70d9b94743a2c0dd1d8a22f0132c282a.jpg" style="margin: 10px auto; display: block;">
  <p style="color: #888888; margin-top: 5px; margin-bottom: 20px;"><i>窗口局部缩放</i></p>
</div>

这个可视化模块虽然看似简单，但在实现过程中考虑了很多实用性的细节。它既可以用来验证数据完整性，也能支持初步的技术分析。

---

# 第十一章：个性化配置：软件设置详解

"看海量化交易系统"提供了一个独立的"软件设置"对话框，用于管理那些独立于具体策略、具有全局性或不常变动的参数。理解并正确配置这些选项，将有助于提升使用体验，并确保软件与关键依赖（如MiniQMT）的顺畅通信。

---

## 11.1 如何打开"设置"对话框？

在主界面顶部工具栏，点击 **设置** 按钮（齿轮图标⚙️），即可打开"软件设置"对话框。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/软件设置.png" alt="软件设置对话框" />
</p>

---

## 11.2 "基本参数设置"与"账户设置"

* **无风险收益率 (Risk-Free Rate)**:

  * **功能**: 设定一个年化的无风险收益率，主要用于计算夏普比率、索提诺比率等风险调整后收益指标。
  * **如何设置**:在实际研究中，通常使用十年期国债收益率作为无风险利率的代理。您可以根据当前市场情况进行调整，可点击此处查看：[<ins>中国十年期国债收益率行情</ins>](https://cn.investing.com/rates-bonds/china-10-year-bond-yield)。
* **延迟显示日志 (Delay Log Display)**:

  * **功能**: 勾选此项后，在策略高速回测期间，系统会将所有日志先缓存起来，待回测结束后再一次性显示在右侧日志面板。
  * **优势**: 对于运行时间长、日志输出量大的回测，开启此功能可以避免因界面频繁刷新而导致的UI卡顿，从而显著提升回测性能和界面流畅度。
* **初始化行情数据 (Initialize Market Data)**:

  * **功能**: 用于控制每次**启动回测时**，是否自动检查并补充本地缺失的历史数据。
  * **工作原理**: 
    > - **勾选时**: 回测启动前会自动检测策略所需的历史数据，如发现缺失会自动从服务器下载补充
    > - **不勾选时**: 直接使用本地已有数据进行回测，不会自动下载缺失数据
  * **性能影响**: 
    > - **优点**: 能够自动补充缺失的数据，确保回测数据完整性
    > - **缺点**: 会显著拖慢回测启动速度，特别是在数据缺失较多时
  * **使用建议**: 
    > - **推荐做法**: 保持此选项**不勾选**，在回测前先通过"数据中心"的"数据补充"功能手动补充所需数据
    > - **适用场景**: 仅在偶尔需要快速测试且不介意等待时间的情况下临时勾选
    > - **最佳实践**: 养成定期使用数据中心补充数据的习惯，这样既能保证数据完整，又能获得最佳的回测性能
* **账户设置 (Account Settings)**:

  * **功能**: 此为实盘/模拟盘功能预用选项，用于配置交易账户信息。
  * **说明**: **由于当前版本专注于回测功能，此处的账户设置不会对回测产生影响，暂时无需理会。**

---

## 11.3 "股票列表管理"

* **更新成分股列表 (Update Constituent Stocks)**:
  * **功能**: 点击此按钮，系统会连接到网络，下载并更新本地存储的A股主要指数（如沪深300、中证500等）的最新成分股列表文件。这些文件保存在软件根目录下的 `data` 文件夹中。
  * **使用建议**: 当您发现指数成分股发生较大调整，或者长时间未更新时，可以执行此操作。过程需要一定时间，请耐心等待，无需频繁点击。

---

## 11.4 "客户端设置"

这是整个设置对话框中**至关重要**的部分，它负责建立本软件与MiniQMT之间的连接。

* **miniQMT客户端路径 (miniQMT Client Path)**:

  * **功能**: 指向您电脑上MiniQMT客户端的主程序文件 (`XtItClient.exe`)。
  * **如何设置**: 点击 **浏览...** 按钮，找到您的MiniQMT安装目录，并进入 `bin.x64` 文件夹，选择 `XtItClient.exe` 文件。
* **QMT路径 (QMT Path)**:

  * **功能**: 指向MiniQMT的用户数据文件夹 (`userdata_mini`)。框架需要从此文件夹读取账户配置、日志等信息。
  * **如何设置**: 点击 **浏览...** 按钮，找到您的MiniQMT安装目录，并选择 `userdata_mini` 文件夹。

---

## 11.5 "版本信息"与"问题反馈"

* **版本信息 (Version Info)**:

  * **功能**: 此区域会清晰地展示您当前使用的"看海量化交易系统"的**版本号**、**构建日期**和**更新通道**（如`stable`稳定版）。当您需要反馈问题或寻求帮助时，提供这些版本信息将非常有帮助。
* **反馈问题 (Feedback)**:

  * **功能**: 点击此按钮，会直接跳转到作者的官方在线反馈页面。如果您在使用中遇到任何Bug、有功能建议或想进行交流，欢迎通过此渠道联系。

---

完成所有设置后，请务必点击右下角的 **保存设置** 按钮，您的所有更改才能生效。 

# 第十二章：策略编写初探：API与基本结构

如果说前十一章是对软件操作的指南，相对易于掌握，那么本章将深入到整个系统的核心——策略编写。本章是实现量化交易思想的关键，旨在为开发者提供一个关于"看海量化交易系统"策略框架的直观理解，并详细说明如何基于此框架，一步步构建属于自己的交易策略。我们将重点介绍策略文件的基本结构、核心回调函数，以及与框架交互的关键API。

---

## 策略框架是什么

对于每一位交易者而言，心中都有一套独特的交易逻辑。量化交易的本质，便是将这套逻辑转化为代码，让计算机去执行。然而，从零开始编写一套完整的交易程序是复杂且耗时的，需要处理数据获取、订单管理、风险控制、事件驱动等一系列繁琐的底层任务。

**策略框架**正是为了解决这一问题而生。可以将它理解为一个半成品的交易机器人，它已经为开发者搭建好了坚实的骨架，处理了所有与交易逻辑无关的技术细节。开发者只需要专注于策略本身，将交易思想填充到框架预留的接口中，即可快速构建出功能完备、稳定可靠的自动化交易策略。

为了贴近主流的量化实践和用户习惯，`khQuant` 定义了一套符合直觉的策略框架。它主要包含四个核心部分，分别对应策略生命周期中的不同阶段：

* **初始化 (`init`)**: 策略开始运行时，用于进行全局设置，仅执行一次。
* **盘前回调 (`khPreMarket`)**: 每个交易日开盘前，用于执行每日的准备工作。
* **主回调 (`khHandlebar`)**: 在盘中根据设定的频率被反复调用，是策略逻辑的核心。
* **盘后回调 (`khPostMarket`)**: 每个交易日收盘后，用于执行当日的复盘和清理任务。

这四个部分均以函数的形式存在于策略文件中。框架会在特定的时间点自动调用这些函数，并将包含当前市场行情、账户资金、持仓情况等所有必要信息的 `context` 对象作为参数传递给它们。在函数中完成逻辑判断后，只需返回标准的交易指令（我们称之为"信号"），框架便会自动执行后续的下单操作。这种设计极大地简化了策略编写的复杂度，使开发者可以聚焦于策略逻辑本身，而非底层实现。

---

## 12.1 策略框架一览

一个策略文件本质上是一个标准的Python脚本，通过实现框架预定义的一系列函数来完成策略逻辑。以下是一个策略文件的最简化结构，它包含了所有主要的回调函数，可以作为开始编写新策略的模板。

```python
from typing import Dict, List

def init(stock_list, context):
    """
    策略初始化函数，在任务开始时仅执行一次。
    用于定义全局变量、加载外部数据等。
    """
    pass

def khHandlebar(context: Dict) -> List[Dict]:
    """
    策略核心逻辑，会被框架根据设定的频率反复调用。
    负责行情判断和生成交易信号。
    """
    signals = []
    return signals

def khPreMarket(context: Dict) -> List[Dict]:
    """
    盘前处理函数（可选）。
    在每日开盘前的指定时间点调用。
    """
    signals = []
    return signals

def khPostMarket(context: Dict) -> List[Dict]:
    """
    盘后处理函数（可选）。
    在每日收盘后的指定时间点调用。
    """
    signals = []
    return signals
```

---

## 12.2 核心回调函数详解

框架通过在特定时机调用策略文件中的特定函数来驱动策略运行。开发者需要根据需求实现这些函数。为了更好地理解，可以参考我们提供的 `strategies/MACD.py` 策略示例文件，它展示了如何实现一个完整的策略。

### 12.2.1 `init(stock_list, context)` - 初始化函数

* **执行时机**：在整个回测或交易任务开始时，被框架**调用一次**。
* **核心作用**：用于执行策略的全局初始化任务，如设置参数、加载数据等。
* **参数说明**：
  * `stock_list` (list): 框架传入的股票池列表，例如 `['000001.SZ', '600000.SH']`。
  * `context` (dict): 一个包含初始化时刻上下文信息的字典。其内部详细结构如下：
    * **`__current_time__`** (dict): 包含当前时间信息的字典。
      * `timestamp`: (int) 标准Unix时间戳。
      * `datetime`: (str) 格式为 "YYYY-MM-DD HH:MM:SS" 的日期时间字符串。
      * `date`: (str) 格式为 "YYYY-MM-DD" 的日期字符串。
      * `time`: (str) 格式为 "HH:MM:SS" 的时间字符串。
    * **`__account__`** (dict): 包含账户资金信息的字典。
      * `account_type`: (str) 账户类型 (例如, 'STOCK')。
      * `account_id`: (str) 账户ID。
      * `cash`: (float) 当前可用资金。
      * `frozen_cash`: (float) 冻结资金。
      * `market_value`: (float) 持仓市值。
      * `total_asset`: (float) 总资产。
      * `benchmark`: (str) 基准指数代码。
    * **`__positions__`** (dict): 包含持仓信息的字典，在初始化时通常为空 `{}`。
    * **`__framework__`** (object): 框架核心类的实例。它包含了最全面的框架信息和功能接口。如果其他上下文参数不包含所需信息，可以尝试通过此对象获取。

### 12.2.2 `khHandlebar(context)` - 策略主逻辑函数

* **执行时机**：根据主界面"运行驱动区"设置的**触发方式**，被框架反复、高频地调用。
* **核心作用**：这是实现交易策略核心逻辑的地方，包括行情判断、信号生成、下单等。
* **参数说明**：
  * `context` (dict): 包含当前时间点所有可用信息的字典，其结构为：
    * **`__current_time__`** (dict): 包含当前时间信息的字典。
      * `timestamp`: (int) 标准Unix时间戳。
      * `datetime`: (str) 格式为 "YYYY-MM-DD HH:MM:SS" 的日期时间字符串。
      * `date`: (str) 格式为 "YYYY-MM-DD" 的日期字符串。
      * `time`: (str) 格式为 "HH:MM:SS" 的时间字符串。
    * **`__account__`** (dict): 包含账户资金信息的字典。
      * `account_type`: (str) 账户类型。
      * `account_id`: (str) 账户ID。
      * `cash`: (float) 当前可用资金。
      * `frozen_cash`: (float) 冻结资金。
      * `market_value`: (float) 持仓市值。
      * `total_asset`: (float) 总资产。
      * `benchmark`: (str) 基准指数代码。
    * **`__positions__`** (dict): 包含当前持仓信息的字典。其结构为 `{股票代码: 持仓详情}`。持仓详情是一个字典，包含了该股票的详细持仓信息，其内部字段如下：
      * `account_type`: (int) 账户类型。
      * `account_id`: (str) 账户ID。
      * `stock_code`: (str) 股票代码。
      * `volume`: (int) 持仓数量。
      * `can_use_volume`: (int) 可用数量（可卖出数量）。
      * `open_price`: (float) 当日开盘价。
      * `market_value`: (float) 持仓市值。
      * `frozen_volume`: (int) 冻结数量。
      * `on_road_volume`: (int) 在途数量。
      * `yesterday_volume`: (int) 昨日持仓数量。
      * `avg_price`: (float) 持仓成本价。
      * `current_price`: (float) 当前市价。
      * `direction`: (int) 持仓方向。
      * `profit`: (float) 持仓浮动盈亏。
      * `profit_ratio`: (float) 持仓盈亏率。
    * **`__framework__`** (object): 框架核心类的实例。它包含了最全面的框架信息和功能接口。如果其他上下文参数不包含所需信息，可以尝试通过此对象获取。
    * **`[股票代码]`** (pandas.Series): 以股票代码（如`'000001.SZ'`）为键，值为一个Pandas Series对象，包含了该股票在当前时间点的所有行情字段（如`open`, `high`, `low`, `close`, `volume`等）。
* **返回值**:
  * 该函数需要返回一个**交易信号列表** (`List[Dict]`)。框架在收到返回的列表后，会自动解析其中的每一条指令，并调用底层的交易接口去执行。如果列表为空，则框架认为当前时间点无任何操作。
  * 一个标准的交易信号字典包含以下键值对：

| 键 (Key) | 类型 (Type) | 必填 | 说明 |
|---|---|---|---|
| `code` | str | 是 | 股票代码，必须是标准的QMT格式，例如 `'000001.SZ'` 或 `'600036.SH'`。|
| `action` | str | 是 | 交易动作。可选值为 'buy' (买入) 或 'sell' (卖出)。|
| `price` | float | 是 | 交易价格。|
| `volume` | int | 是 | 交易数量，必须是100的整数倍。|
| `reason` | str | 否 | 交易原因或备注。此信息会显示在日志和交易记录中。|
| `timestamp`| int | 否 | 信号生成时的时间戳。 |

  * 关于信号字典的更详细说明，请参考 **12.7 交易信号详解**。

### 12.2.3 `khPreMarket(context)` - 盘前处理函数（可选）

* **执行时机**：在每个交易日的指定盘前时间点（如09:00）被调用一次。该功能需要在主界面"盘前盘后触发设置"中勾选"触发盘前回调"才会生效。
* **参数 `context`**：其结构与 `khHandlebar` 函数的 `context` **完全相同**。它包含了**当天第一个行情数据点**的所有信息，包括账户、持仓、以及股票池内所有股票在该时刻的行情数据。
* **返回值**：与 `khHandlebar` 类似，返回一个交易信号列表 (`List[Dict]`)。
* **常见用途**：
  * 每日选股、计算因子。
  * 重置当日的交易状态或计数器。
  * 提前下达一些集合竞价阶段的预埋单。

### 12.2.4 `khPostMarket(context)` - 盘后处理函数（可选）

* **执行时机**：在每个交易日的指定盘后时间点（如15:30）被调用一次。该功能需要在主界面"盘前盘后触发设置"中勾选"触发盘后回调"才会生效。
* **参数 `context`**：其结构与 `khHandlebar` 函数的 `context` **完全相同**。它包含了**当天最后一个行情数据点**的所有信息，包括账户、持仓、以及股票池内所有股票在该时刻的行情数据。
* **返回值**：与 `khHandlebar` 类似，返回一个交易信号列表 (`List[Dict]`)。
* **常见用途**：
  * 当日交易复盘、业绩归因分析。
  * 保存当日的策略状态或数据到本地文件。
  * 清理当日持仓，或为下一个交易日做准备。

---

## 12.3 获取时间数据

在策略中，所有时间相关的信息都储存在 `context['__current_time__']` 这个字典中。通过访问它，可以获取到策略当前执行点的精确时间。

* `context['__current_time__']['timestamp']`: 返回一个整数形式的Unix时间戳。
* `context['__current_time__']['datetime']`: 返回 `YYYY-MM-DD HH:MM:SS` 格式的字符串，最常用。
* `context['__current_time__']['date']`: 返回 `YYYY-MM-DD` 格式的日期字符串。
* `context['__current_time__']['time']`: 返回 `HH:MM:SS` 格式的时间字符串。

**示例：实现简单的择时逻辑**

```python
def khHandlebar(context: Dict) -> List[Dict]:
    time_info = context['__current_time__']
  
    # 获取当前时间字符串，如 '09:31:00'
    current_time = time_info['time']
  
    # 简单的交易时间控制：只在上午10点后、下午2点半前进行交易判断
    if "10:00:00" < current_time < "14:30:00":
        # 在这里编写主要的策略逻辑...
        print("处于交易时间段，执行策略。")
    else:
        # 非交易时间段，不执行任何操作
        print("非交易时间段，跳过。")
  
    return []
```

---

## 12.4 获取账户数据

账户的资金状况信息储存在 `context['__account__']` 字典中，它提供了账户资产的实时快照。

* `context['__account__']['cash']`: (float) 当前可用于交易的现金。
* `context['__account__']['market_value']`: (float) 所有持仓按当前市价计算的总市值。
* `context['__account__']['total_asset']`: (float) 总资产，即 `cash` + `market_value`。
* `context['__account__']['frozen_cash']`: (float) 因挂单而冻结的资金。

**示例：根据资金动态计算买入量**

```python
def khHandlebar(context: Dict) -> List[Dict]:
    account = context['__account__']
  
    # 获取当前可用资金
    available_cash = account['cash']
  
    # 设定一个目标仓位：使用当前可用资金的20%
    cash_to_use = available_cash * 0.2
  
    # 获取 '000001.SZ' 的当前价格
    stock_data = context.get('000001.SZ')
    if stock_data is not None and not stock_data.empty:
        stock_price = stock_data['close']
  
        # 计算理论上可以买入多少股，并向下取整到100的倍数
        if stock_price > 0:
            volume_to_buy = int(cash_to_use / stock_price / 100) * 100
            if volume_to_buy > 0:
                print(f"资金充足，计划以 {stock_price} 元的价格买入 {volume_to_buy} 股 000001.SZ")
                # 此处可以构建并返回一个买入信号
    
    return []
```

---

## 12.5 获取持仓数据

持仓信息 `context['__positions__']` 是一个字典，键为股票代码，值为该股票的详细持仓信息字典。这使得检查特定持仓、获取持仓细节变得非常方便。

**示例：实现持仓股票的止盈止损**

```python
def khHandlebar(context: Dict) -> List[Dict]:
    positions = context['__positions__']
    signals = []
  
    # 检查是否持有 '600519.SH' (贵州茅台)
    if '600519.SH' in positions:
        # 获取该持仓的详细信息
        pos_info = positions['600519.SH']
  
        volume = pos_info['volume']
        profit_ratio = pos_info['profit_ratio'] # 获取持仓盈亏率
  
        print(f"持有 {volume} 股 600519.SH，当前盈利 {profit_ratio*100:.2f}%")
  
        # 止盈逻辑：如果盈利超过10%，就全部卖出
        if profit_ratio > 0.10:
            sell_signal = {
                'action': 'sell',
                'code': '600519.SH',
                'volume': volume, # 卖出全部持仓
                'remark': '盈利超过10%，止盈'
            }
            signals.append(sell_signal)
  
        # 止损逻辑：如果亏损超过5%，就全部卖出
        elif profit_ratio < -0.05:
            sell_signal = {
                'action': 'sell',
                'code': '600519.SH',
                'volume': volume, # 卖出全部持仓
                'remark': '亏损超过5%，止损'
            }
            signals.append(sell_signal)
  
    return signals
```

---

## 12.6 获取当前运行配置

在某些复杂的策略场景中，可能需要在策略逻辑内部获取在GUI界面上配置的参数，例如获取设置的基准合约代码，或者根据不同的手续费配置调整交易行为。

这些配置信息可以通过 `context` 中的 `__framework__` 对象进行访问。

* **`context['__framework__'].config`**: 这是一个 `khConfig.KhConfig` 类的实例，持有本次运行的所有配置信息。

**示例：在策略中获取基准合约和手续费配置**

```python
def init(stock_list, context):
    # 从框架对象中获取配置实例
    config = context['__framework__'].config

    # 读取回测配置中的 'benchmark' 参数
    benchmark_code = config.get_config('backtest.benchmark')
    print(f"当前配置的基准合约为: {benchmark_code}")

    # 读取交易成本中的佣金比例
    commission_rate = config.get_config('backtest.trade_cost.commission_rate')
    print(f"当前配置的佣金比例为: {commission_rate}")
```

---

## 12.7 交易信号详解

交易信号是策略与交易执行模块沟通的唯一方式。它是一个包含了所有交易必要信息的Python字典。`khHandlebar`、`khPreMarket`、`khPostMarket`的返回值以及`__framework__.trade()`的参数都是交易信号。

一个标准的交易信号字典包含以下键值对：


| 键 (Key)    | 类型 (Type) | 必填 | 说明                                                                |
| ----------- | ----------- | ---- | ------------------------------------------------------------------- |
| `code`      | str         | 是   | 股票代码，必须是标准的QMT格式，例如`'000001.SZ'` 或 `'600036.SH'`。 |
| `action`    | str         | 是   | 交易动作。可选值为 'buy' (买入) 或 'sell' (卖出)。                  |
| `price`     | float       | 是   | 交易价格。                                                          |
| `volume`    | int         | 是   | 交易数量，必须是100的整数倍。                                       |
| `reason`    | str         | 否   | 交易原因或备注。此信息会显示在日志和交易记录中。                    |
| `timestamp` | int         | 否   | 信号生成时的时间戳。                                                |

**示例：**

```python
# 以10.5元的价格买入100股平安银行
signal_1 = {
    'code': '000001.SZ',
    'action': 'buy',
    'price': 10.5,
    'volume': 100,
    'reason': 'MACD金叉买入'
}

# 以18.5元的价格卖出200股贵州茅台
signal_2 = {
    'code': '600519.SH',
    'action': 'sell',
    'price': 18.50,
    'volume': 200,
    'reason': '达到止盈位卖出'
}
```

## 12.8 在策略中使用日志

为了方便调试和监控策略的内部状态，可以在策略代码中直接调用日志输出功能，将信息发送到看海量化交易系统的日志系统（包括界面和文件）。

**核心机制**：框架已经对Python内置的 `logging` 模块进行了配置。因此，开发者无需关心日志的底层实现，只需在策略代码中导入 `logging` 模块，然后直接调用其标准函数即可。

**使用方法：**

1. 在策略文件顶部添加 `import logging`。
2. 在代码中调用 `logging.info()`, `logging.warning()`, `logging.error()` 等函数。

日志级别与界面上显示的颜色直接对应：

* **普通信息 (INFO)**: 使用 `logging.info("进入长仓条件判断")` 输出常规的流程信息或变量状态。这对应界面上的 **白色** 文本。
* **调试信息 (DEBUG)**: 如果需要输出更详细的、仅在调试时关心的变量值或中间计算结果，可以使用 `logging.debug(f"当前ATR值: {atr_value}")`。这对应界面上的 **浅紫色** 文本。（注意：默认配置下，DEBUG级别的日志可能不会显示，需在设置中调整）
* **警告信息 (WARNING)**: 当策略遇到一些非致命但需要注意的情况时，比如某个数据获取失败但有备用方案，可以使用 `logging.warning("无法获取最新行情，使用上一周期数据代替")`。这对应界面上的 **橙色** 文本，比较醒目。
* **错误信息 (ERROR)**: 当策略发生严重错误，可能导致后续逻辑无法正常执行时，应使用 `logging.error("计算指标时出现除零错误")` 。这对应界面上最醒目的 **红色** 文本，强烈提示需要检查问题。

**示例：**

```python
import logging
from typing import Dict, List

def khHandlebar(context: Dict) -> List[Dict]:
    # 检查账户现金
    cash = context['__account__']['cash']
    logging.info(f"当前可用资金: {cash}")

    if cash < 10000:
        logging.warning("可用资金不足1万元，跳过本次交易机会。")
        return []
  
    # ...后续策略逻辑...
    try:
        # 可能会出错的代码
        risky_value = 1 / 0
    except Exception as e:
        logging.error(f"计算风险值时发生严重错误: {e}", exc_info=True) # exc_info=True会记录完整的错误堆栈

    return []
```

### 如何输出醒目的内容？

如果希望某条日志信息在界面上特别突出，最直接的方式是使用 `logging.warning()` 或 `logging.error()`。`ERROR` 级别（红色）最为醒目，通常用于指示发生了必须处理的问题。`WARNING` 级别（橙色）也比较突出，适合用于提示潜在风险或需要关注的状态。请根据信息的重要性和紧急程度，审慎选择合适的级别进行输出。

---

## 12.9 策略工具箱：`khQTTools` 模块详解

为了进一步简化策略开发，`khQuant` 提供了一个名为 `khQTTools.py` 的工具模块，其中包含了一系列高效实用的辅助函数。这些工具涵盖了时间判断、交易计算、数据获取等多个方面，能够帮助开发者快速实现复杂的逻辑，避免重复造轮子。

要使用这些工具，只需在策略文件的顶部从 `khQTTools` 模块导入所需的函数即可。

```python
# 在策略文件顶部导入工具函数
from khQTTools import (
    KhQuTools,
    calculate_max_buy_volume,
    generate_signal,
    get_stock_names,
    khHistory
)
```

### 12.9.1 时间工具 (`KhQuTools`)

`KhQuTools` 是一个封装了常用时间判断功能的类。使用前需要先进行实例化。

```python
# 在init函数或策略的全局部分实例化
tools = KhQuTools()
```

#### `is_trade_time()`

* **功能**：判断当前时间是否处于A股的常规交易时间段内（09:30-11:30, 13:00-15:00）。
* **返回值**：`bool`，是则返回 `True`，否则返回 `False`。
* **使用场景**：确保交易逻辑只在开盘时段执行。

**示例：**

```python
def khHandlebar(context: Dict) -> List[Dict]:
    if not tools.is_trade_time():
        return [] # 非交易时间，直接返回

    # ... 在此编写交易时间内的策略逻辑 ...
    logging.info("交易时间内，执行策略。")
    return []
```

#### `is_trade_day(date_str)`

* **功能**：判断指定日期是否为A股的交易日（会剔除周末和法定节假日）。
* **参数**：
  * `date_str` (str, 可选): `YYYY-MM-DD` 格式的日期字符串。如果留空，则默认判断当天。
* **返回值**：`bool`，是交易日则返回 `True`，否则返回 `False`。

**示例：**

```python
# 判断2024年10月1日是否是交易日
is_trading = tools.is_trade_day("2024-10-01") 
print(f"2024-10-01 是交易日吗? {'是' if is_trading else '否'}")

# 判断今天是否是交易日
is_today_trading = tools.is_trade_day()
print(f"今天是交易日吗? {'是' if is_today_trading else '否'}")
```

#### `get_trade_days_count(start_date, end_date)`

* **功能**：计算两个日期之间的A股交易日天数。
* **参数**：
  * `start_date` (str): `YYYY-MM-DD` 格式的开始日期。
  * `end_date` (str): `YYYY-MM-DD` 格式的结束日期。
* **返回值**：`int`，两个日期之间的交易日总数。

**示例：**

```python
# 计算2024年全年的交易日天数
days = tools.get_trade_days_count("2024-01-01", "2024-12-31")
print(f"2024年共有 {days} 个交易日。")
```

### 12.9.2 交易辅助函数

#### `calculate_max_buy_volume(data, stock_code, price, cash_ratio)`

* **功能**：一个非常实用的函数，用于根据可用资金和股价，计算出理论上可以买入的最大股票数量（会自动向下取整到100的倍数）。
* **参数**：
  * `data` (dict): 即策略回调函数中的 `context` 对象。
  * `stock_code` (str): 计划买入的股票代码。
  * `price` (float): 计划买入的价格。
  * `cash_ratio` (float, 可选): 计划使用的资金比例，默认为 `1.0` (即全部可用资金)。
* **返回值**：`int`，可以买入的最大股数。

**示例：**

```python
def khHandlebar(context: Dict) -> List[Dict]:
    # 计划用50%的资金买入平安银行
    stock_to_buy = '000001.SZ'
    current_price = context.get(stock_to_buy)['close'] # 获取当前价

    # 计算最多能买多少股
    max_volume = calculate_max_buy_volume(context, stock_to_buy, current_price, cash_ratio=0.5)

    if max_volume > 0:
        logging.info(f"资金足够，计划以 {current_price} 的价格买入 {max_volume} 股 {stock_to_buy}")
        # ...后续可以生成并返回交易信号
  
    return []
```

#### `generate_signal(data, stock_code, price, ratio, action, reason)`

* **功能**：高级信号生成函数。对于买入操作，它会自动计算最大可买股数；对于卖出操作，它会自动计算最大可卖股数。极大简化了信号的创建过程。
* **参数**：
  * `data` (dict): 即 `context` 对象。
  * `stock_code` (str): 交易的股票代码。
  * `price` (float): 交易价格。
  * `ratio` (float): 比例。当 `action` 为 `'buy'` 时，这是资金使用比例；当 `action` 为 `'sell'` 时，这是持仓卖出比例。
  * `action` (str): 交易动作，`'buy'` 或 `'sell'`。
  * `reason` (str, 可选): 交易原因。
* **返回值**：`List[Dict]`，一个包含单个交易信号的列表，如果条件不满足（如无钱可买或无仓可卖），则返回空列表。

**示例：**

```python
def khHandlebar(context: Dict) -> List[Dict]:
    signals = []
  
    # 假设 is_rsi_low 和 is_rsi_high 是已经计算好的布尔值
    is_rsi_low = True 
    is_rsi_high = True

    # 1. 如果RSI指标小于30，使用30%的资金买入平安银行
    if is_rsi_low and '000001.SZ' in context:
        # 只需指定买入比例，函数会自动计算股数
        buy_signals = generate_signal(context, '000001.SZ', price=context.get('000001.SZ')['close'], ratio=0.3, action='buy', reason='RSI < 30')
        signals.extend(buy_signals)

    # 2. 如果持有茅台，且RSI指标大于70，卖出50%的仓位
    if '600519.SH' in context['__positions__'] and is_rsi_high:
        # 只需指定卖出比例，函数会自动计算股数
        sell_signals = generate_signal(context, '600519.SH', price=context.get('600519.SH')['close'], ratio=0.5, action='sell', reason='RSI > 70')
        signals.extend(sell_signals)

    return signals
```

### 12.9.3 数据获取与处理

#### `khHistory(symbol_list, fields, bar_count, fre_step, current_time=None, skip_paused=False, fq='pre', force_download=False)`

*   **功能**：**（核心功能）** 获取指定证券的历史K线数据，是计算各种技术指标的基础。

*   **入口参数 (Input)**:
    *   `symbol_list` (list): 证券代码列表。必须是包含标准QMT格式（如`'600000.SH'`）的字符串列表。
    *   `fields` (list): 希望获取的行情字段列表。常用的字段包括 `'time'`, `'open'`, `'high'`, `'low'`, `'close'`, `'volume'` (成交量), `'amount'` (成交额)。返回的DataFrame将包含这些列。
    *   `bar_count` (int): 希望获取的K线数量。例如，`bar_count=30` 和 `fre_step='1d'` 将获取过去30个交易日的日K线数据。
    *   `fre_step` (str): K线周期。支持 `'1m'`, `'5m'`, `'15m'`, `'30m'`, `'60m'` 等分钟线周期，以及 `'1d'` (日线), `'1w'` (周线), `'1mon'` (月线)。
    *   `current_time` (str, 可选): 获取历史数据的结束时间点，格式为 `'YYYYMMDDHHMMSS'`。如果设为 `None` 或不提供，则数据截止到当前最新时间。这对于回测中模拟特定时间点的情景非常重要。
    *   `skip_paused` (bool, 可选): 是否跳过停牌日。默认为 `False`。如果设为 `True`，返回的 `bar_count` 根K线将不包含该股票的停牌日，K线是连续的。
    *   `fq` (str, 可选): 复权类型。默认为 `'pre'` (前复权)。可选值为 `'back'` (后复权) 或 `'none'` (不复权)。
    *   `force_download` (bool, 可选): 是否强制从远程服务器下载新数据，忽略本地缓存。默认为 `False`。在进行数据补全或怀疑本地数据有误时，可设为 `True`。

*   **输出参数 (Output)**:
    *   **返回值**: 一个Python字典。
    *   **字典结构**:
        *   **键 (Key)**: 股票代码字符串 (e.g., `'000001.SZ'`)。
        *   **值 (Value)**: 一个`pandas.DataFrame`对象，或者在获取失败时为`None`。
    *   **DataFrame结构**:
        *   **索引 (Index)**: 默认的整数索引。
        *   **列 (Columns)**: 与输入的 `fields` 参数完全对应。例如，如果 `fields=['time', 'open', 'close']`，DataFrame就会有这三列。`time`列是 `datetime` 对象，方便进行时间序列分析。

**示例：在 `init` 中预加载数据，在 `khHandlebar` 中计算5日均线**

```python
import pandas as pd
import logging
from khQTTools import khHistory

# 在全局范围定义一个缓存，用于存储计算好的指标
g = {'sma5': {}}

def init(stock_list, context):
    """
    在初始化时，一次性获取所有股票过去30天的日线数据，用于后续计算。
    """
    logging.info("开始获取历史数据用于初始化...")
    history_data = khHistory(
        symbol_list=stock_list,
        fields=['time', 'open', 'close'],
        bar_count=30,
        fre_step='1d'
    )
  
    for stock in stock_list:
        df = history_data.get(stock)
        if df is not None and not df.empty:
            # 计算5日均线并存入全局缓存
            g['sma5'][stock] = df['close'].rolling(window=5).mean().iloc[-1]
            logging.info(f"{stock} 的初始5日均线值为: {g['sma5'][stock]}")

def khHandlebar(context: Dict) -> List[Dict]:
    signals = []
    # 假设股票池里有 '000001.SZ'
    stock_code = '000001.SZ'
    if stock_code in context:
        current_price = context.get(stock_code)['close']
        sma5_value = g['sma5'].get(stock_code)

        if sma5_value and current_price > sma5_value:
            logging.info(f"{stock_code} 当前价格上穿5日线，产生买入信号。")
            # 此处可构建买入信号
          
    # 注意：在实盘中，需要每日更新缓存的指标值，可以在khPreMarket中实现
    return signals
```

#### `get_stock_names(stock_codes, stock_list_file)`

* **功能**：根据股票代码列表，查询并返回对应的股票名称。
* **参数**：
  * `stock_codes` (list/str): 一个或多个股票代码。
  * `stock_list_file` (str): 包含股票代码和名称映射关系的CSV文件路径。通常是 `data/stock_list.csv`。
* **返回值**：一个字典，键为股票代码，值为股票名称。

**示例：**

```python
# 假设 stock_list.csv 在 data 目录下
stock_list_path = "data/stock_list.csv" 
names_dict = get_stock_names(['000001.SZ', '600519.SH'], stock_list_path)
# 返回: {'000001.SZ': '平安银行', '600519.SH': '贵州茅台'}
print(names_dict)
```
