<!DOCTYPE html>
<html>
<head>
<title>KHQuant_Manual_Chapter6.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E7%AC%AC%E5%85%AD%E7%AB%A0%E6%A0%B8%E5%BF%83%E9%85%8D%E7%BD%AE%E9%A1%B9%E5%B7%A6%E4%BE%A7%E9%9D%A2%E6%9D%BF%E7%B2%BE%E8%A7%A3">第六章：核心配置项：左侧面板精解</h1>
<p>左侧面板是&quot;看海量化交易系统&quot;的策略配置中枢。在这里，将定义策略的灵魂（策略文件）、仿真的环境（回测参数）、必需的数据（数据设置）以及执行的范围（股票池）。本章将逐一详解这些配置项，帮助精确地掌控策略的每一个细节。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/左侧面板.png" alt="左侧面板" width="50%" />
</p>
<hr>
<h2 id="61-%22%E7%AD%96%E7%95%A5%E9%85%8D%E7%BD%AE%22%E7%BB%84%E6%8C%87%E5%AE%9A%E7%AD%96%E7%95%A5%E7%9A%84%E5%A4%A7%E8%84%91">6.1 &quot;策略配置&quot;组：指定策略的大脑</h2>
<p>这是所有设置的起点，需要在这里告诉系统要运行哪个策略。</p>
<ul>
<li><strong>策略文件 (Strategy File)</strong>
<ul>
<li><strong>功能</strong>: 通过点击 <strong>选择策略文件</strong> 按钮，可以从本地文件夹中加载编写的Python策略脚本（必须是 <code>.py</code> 结尾的文件）。这个文件是策略的核心，包含了所有的交易逻辑。</li>
<li><strong>要求</strong>: 框架通过动态加载此模块来执行策略。因此，策略文件需要符合一定的规范，例如包含 <code>khHandlebar</code> 等关键的回调函数。关于策略编写的详细规范，请参阅第十二章《核心驱动：策略编写指南》。</li>
</ul>
</li>
<li><strong>运行模式 (Run Mode)</strong>
<ul>
<li><strong>功能</strong>: 当前版本专注于回测功能，此选项固定为 <strong>回测(Backtest)</strong> 模式。</li>
<li><strong>模式说明</strong>: 回测是量化交易的基石。它使用历史数据来模拟策略的过往表现，从而可以在不产生任何真实风险的情况下，检验和评估策略的有效性。</li>
</ul>
</li>
</ul>
<h2 id="62-%22%E5%9B%9E%E6%B5%8B%E5%8F%82%E6%95%B0%22%E7%BB%84%E7%B2%BE%E9%9B%95%E7%BB%86%E7%90%A2%E7%9A%84%E6%A8%A1%E6%8B%9F%E7%8E%AF%E5%A2%83">6.2 &quot;回测参数&quot;组：精雕细琢的模拟环境</h2>
<p>此区域的设置核心目的是尽可能真实地模拟历史交易环境，从而让回测结果更具参考价值。</p>
<ul>
<li><strong>基准合约 (Benchmark)</strong>
<ul>
<li><strong>功能</strong>: 设定一个业绩比较基准，通常是市场主流指数。系统会根据此基准计算策略的Alpha（超额收益）、Beta（市场相关性）等关键绩效指标。</li>
<li><strong>如何设置</strong>: 直接在文本框中输入想作为基准的合约代码。沪深300指数最为常用，目前系统也仅调试适配了沪深300。</li>
<li><strong>常见示例</strong>: <code>sh.000300</code> (沪深300)。</li>
</ul>
</li>
<li><strong>交易成本设置 (Transaction Costs)</strong>
<ul>
<li><strong>功能</strong>: 精确模拟交易中产生的各项费用。忽略交易成本的回测报告是毫无意义的，因为它会系统性地高估策略表现。关于交易成本的详细构成，推荐阅读<a href="https://zhuanlan.zhihu.com/p/29310540747">这篇文章</a>。</li>
<li><strong>参数详解</strong>:
<ul>
<li><strong>最低佣金(元)</strong>: 许多券商对单笔交易设有5元的最低佣金收费，即使按比例计算的佣金不足5元，也会按5元收取。</li>
<li><strong>佣金比例</strong>: 根据券商费率设置，例如万分之一，则应填入 <code>0.0001</code>。</li>
<li><strong>卖出印花税</strong>: 目前A股为单向收取，仅在卖出时征收，税率为千分之0.5，应填入 <code>0.0005</code>。</li>
<li><strong>流量费(元/笔)</strong>: 部分券商可能会对每笔交易收取固定的信息服务费或流量费，具体费率请向您的开户券商确认。</li>
</ul>
</li>
</ul>
</li>
<li><strong>滑点设置 (Slippage)</strong>
<ul>
<li><strong>功能</strong>: 在真实交易中，由于市场流动性、订单执行速度等因素，最终成交价格与策略的理想委托价格之间常存在微小差异，这种差异就是<strong>滑点</strong>。在高频交易或流动性差的品种上，滑点是影响策略盈利的关键因素，因此在回测中必须对其进行模拟。本软件提供以下两种滑点模拟方式：</li>
<li><strong>滑点模拟方式</strong>:
<ol>
<li><strong>按最小变动价位 (Tick模式)</strong>: 这种方式模拟的是因价格跳动导致的滑点，适合对市场微观结构有深入理解的投资者。
<ul>
<li><strong>原理</strong>: 对于A股股票，最小变动价位是0.01元。若在&quot;滑点值&quot;中设为 <code>1</code>，则系统在计算成交时，会自动将买入价在委托价基础上上浮0.01元，将卖出价下浮0.01元，以此模拟一个&quot;更差&quot;的成交价格。</li>
</ul>
</li>
<li><strong>按成交金额比例 (Ratio模式)</strong>: 这种方式模拟的是因冲击成本等因素产生的滑点，更简单直观，适合大多数投资者。
<ul>
<li><strong>原理与双边计算</strong>: 系统采用的是<strong>双边滑点</strong>模型。您在界面上设定的比例值会被视为买卖双边的总滑点。在单次交易中，系统会<strong>将这个比例除以2</strong>后应用到成交价上。</li>
<li><strong>示例</strong>: 若在&quot;滑点值&quot;中设为 <code>0.001</code> (即0.1%)：
<ul>
<li>对于<strong>买入</strong>订单，实际成交价会是 <code>委托价 * (1 + 0.001 / 2)</code>，即价格上浮 <code>0.05%</code>。</li>
<li>对于<strong>卖出</strong>订单，实际成交价会是 <code>委托价 * (1 - 0.001 / 2)</code>，即价格下浮 <code>0.05%</code>。</li>
</ul>
</li>
<li><strong>价格取整</strong>: 计算出的新价格会<strong>四舍五入到小数点后两位</strong>（即精确到分），以模拟真实的报价机制。例如，一个理想买入价为10.00元的订单，在0.1%的双边滑点下，计算出的新价格是 <code>10.00 * (1 + 0.0005) = 10.005</code>，四舍五入后为 <code>10.01</code>元。</li>
<li><strong>使用建议</strong>: 滑点的大小与股票的流动性密切相关。对于大盘蓝筹股，滑点可能很小；而对于小盘股或冷门股，滑点可能远大于0.1%。建议根据交易标的的特性调整滑点参数，以获得更准确的回测结果。</li>
</ul>
</li>
</ol>
</li>
<li><strong>如何设置</strong>: 在&quot;滑点类型&quot;下拉框中选择一种模式，然后在右侧的&quot;滑点值&quot;输入框中设定相应的数值。</li>
</ul>
</li>
</ul>
<h2 id="63-%E5%9B%9E%E6%B5%8B%E5%91%A8%E6%9C%9F%E8%AE%BE%E7%BD%AE">6.3 回测周期设置</h2>
<ul>
<li><strong>功能</strong>: 通过&quot;开始日期&quot;和&quot;结束日期&quot;这两个日历控件，可以精确设定回测的起止时间范围。</li>
<li><strong>使用建议</strong>: 为了全面评估策略的稳健性，建议选择足够长的时间段，并确保该时段覆盖了牛市、熊市和震荡市等多种不同的市场环境。</li>
</ul>
<hr>
<h2 id="64-%22%E6%95%B0%E6%8D%AE%E8%AE%BE%E7%BD%AE%22%E7%BB%84%E7%AD%96%E7%95%A5%E7%9A%84%E6%95%B0%E6%8D%AE%E9%A3%9F%E7%B2%AE">6.4 &quot;数据设置&quot;组：策略的数据食粮</h2>
<p>在回测过程中，系统会读取预先补充好的本地数据文件。本组设置决定了在回测时具体读取哪些数据，以及如何对数据进行加工（如复权）。</p>
<ul>
<li>
<p><strong>复权方式</strong>:</p>
<ul>
<li>复权是指为了消除因分红、送股、配股等除权除息（XD）事件导致股价图上出现的价格&quot;跳空&quot;缺口，而对历史股价进行重新计算的过程。这能让技术指标和股价走势保持连续性，从而更真实地反映股票的长期价值增长。软件提供以下几种复权方式：</li>
</ul>
<blockquote>
<p><strong>1. 前复权</strong></p>
<p><strong>介绍</strong>：前复权就是以目前股价为基准，保持现有价位不变，缩减以前价格，使图形吻合，保持股价走势的连续性。简单说就是把除权前的价格按现在的价格换算过来，复权后现在价格不变，以前的价格减少。</p>
<p><strong>举例来说</strong>：假设A股票10元/股，因除权等原因变成5元/股，在当日股价没有涨跌的情况下，选择前复权后，当天与前一天的股价都是5元/股，之前的股价都会按照一定比例缩小。</p>
<p><strong>作用</strong>：采用前复权历史股价就可以更加准确地反映出股票的涨跌幅和收益情况。</p>
</blockquote>
<blockquote>
<p><strong>2. 不复权</strong></p>
<p><strong>介绍</strong>：不复权是指在股票交易中，不考虑除权、除息等事件对股价的影响，直接以当天的实际交易价格作为收盘价进行计算。这样做会导致历史数据的断层，无法准确反映出股票的真实涨跌幅和收益情况。</p>
<p><strong>例如</strong>：如果一只股票在某个日期发生了除权或除息事件，假设这个事件使得股价下跌10%，那么不复权的情况下，历史数据将会按照该事件当天的实际价格进行计算，而不会考虑到除权除息事件带来的影响。</p>
<p><strong>作用</strong>：采用不复权，K线图能真实反映股价历史的除权信息。</p>
</blockquote>
<blockquote>
<p><strong>3. 后复权</strong></p>
<p><strong>介绍</strong>：后复权是指在K线图上以除权前的价格为基准来测算除权后股票的市场成本价。简单说就是把除权后的价格按以前的价格换算过来，复权后以前的价格不变，现在的价格增加。</p>
<p><strong>举例来说</strong>：假设A股票10元/股，因除权等原因变成5元/股，在当日股价没有涨跌的情况下，选择后复权后，当天与前一天的股价都是10元/股，之后的股价都会按照一定比例放大。</p>
<p><strong>作用</strong>：采用后复权能够看出股票真实价值的增加及持股者的真实收益率。</p>
</blockquote>
<blockquote>
<p><strong>4. 等比前复权 / 等比后复权</strong></p>
<p>与常规的前/后复权算法类似，但在价格调整时采用不同的数学模型。在大多数场景下，其效果与普通复权非常接近。</p>
</blockquote>
</li>
<li>
<p><strong>周期类型</strong>:</p>
<ul>
<li><strong>定义</strong>: 选择策略运行所依赖的数据更新频率。不同的周期适用于不同类型的策略。例如，高频交易策略可能依赖Tick数据，而日内趋势策略可能使用分钟线。</li>
</ul>
</li>
<li>
<p><strong>数据字段 (Data Fields)</strong></p>
<ul>
<li>
<p><strong>功能</strong>: 此区域的复选框列表让可以精确选择策略在运行时需要哪些数据。例如，一个简单的均线策略可能只需要收盘价 <code>close</code>，而一个复杂的因子模型可能需要开、高、收、低、成交量、成交额等多个字段。</p>
</li>
<li>
<p><strong>优化作用</strong>:</p>
<blockquote>
<p>💡 <strong>小贴士</strong>：请仅勾选策略中确定会用到的字段。这样做有两个好处：</p>
<ol>
<li><strong>减少内存占用</strong>: 系统无需加载和存储不需要的数据。</li>
<li><strong>加快数据读取速度</strong>: 在进行大规模数据回测时，效果会非常明显。</li>
</ol>
</blockquote>
</li>
<li>
<p><strong>动态变化</strong>: 可勾选的字段列表会根据在&quot;周期类型&quot;中选择的周期动态变化。例如，Tick周期和K线周期所能提供的数据字段是不同的。</p>
</li>
</ul>
</li>
</ul>
<h2 id="65-%22%E8%82%A1%E7%A5%A8%E6%B1%A0%E8%AE%BE%E7%BD%AE%22%E7%BB%84%E5%9C%88%E5%AE%9A%E6%89%A7%E8%A1%8C%E8%8C%83%E5%9B%B4">6.5 &quot;股票池设置&quot;组：圈定执行范围</h2>
<p>股票池定义了策略将在哪些证券范围内进行观察和交易。系统提供了灵活多样的股票池构建方式。</p>
<ul>
<li><strong>常用指数成分股 (Common Index Constituents)</strong>
<ul>
<li><strong>功能</strong>: 这是最快捷的股票池构建方式。只需勾选相应的复选框，即可将A股市场主流指数（如上证50、沪深300、中证500、创业板指、科创50、上证A股、沪深A股）的全部成分股一次性加入到股票池中。</li>
</ul>
</li>
<li><strong>自选清单 (Custom List)</strong>
<ul>
<li><strong>功能</strong>: 点击 <strong>&quot;自选清单&quot;</strong> 这几个字，系统会用默认文本编辑器（如记事本）打开一个<code>csv</code>文件，勾选此项后，系统会加载该文件中的自定义股票列表。</li>
<li><strong>如何编辑</strong>: 在打开的文本文件中编辑股票列表，每行一只股票，编辑完成后直接保存文件即可。请确保文件格式正确：每行格式为<code>股票代码,股票名称</code>，例如：<pre class="hljs"><code><div>600036.SH,招商银行
000001.SZ,平安银行
688981.SH,中芯国际
</div></code></pre>
</li>
</ul>
</li>
<li><strong>手动管理列表 (Manual Management)</strong>
<ul>
<li><strong>功能</strong>: 当需要进行临时的、更灵活的股票池管理时，可以使用此功能。下方的表格提供了一个可视化的股票列表。</li>
<li><strong>操作方法</strong>:
<ul>
<li><strong>添加股票</strong>: 直接在表格的&quot;股票代码&quot;列的空白行中输入股票代码，然后按回车键，系统会自动填充股票名称。</li>
<li><strong>删除股票</strong>: 单击选中想删除的一行或多行（按住<code>Ctrl</code>可多选），然后按键盘上的 <code>Delete</code> 键即可。</li>
<li><strong>导入列表</strong>: 在表格区域 <strong>单击鼠标右键</strong>，会弹出一个上下文菜单，选择 <strong>导入股票列表</strong>。可以从一个 <code>.csv</code> 文件（每行一个代码）批量导入股票。请注意，导入文件中的格式也应为<code>股票代码,股票名称</code>。</li>
<li><strong>清空列表</strong>: 在右键菜单中选择 <strong>清空所有</strong>，可以快速删除当前手动管理列表中的所有股票。</li>
</ul>
</li>
</ul>
</li>
</ul>
<blockquote>
<p>✨ <strong>组合使用</strong>: 上述几种方式可以组合使用。例如，可以先勾选&quot;沪深300&quot;，然后再手动添加几只特别关注的、不在沪深300内的股票。最终的股票池是所有选定方式的并集。</p>
</blockquote>
<p>至此，我们已将左侧核心配置面板的每一个角落都探索完毕。掌握了这些设置，就拥有了为策略量身打造运行环境的能力。在下一章，我们将移步至中间面板，探索策略的&quot;心跳&quot;—触发机制。</p>

</body>
</html>
