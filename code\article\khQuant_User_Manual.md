# 看海量化交易平台 (khQuant) 使用教程

---

## 前言

### 欢迎使用 khQuant

欢迎您选择看海量化交易平台 (khQuant)！

khQuant 是一款旨在帮助您将交易想法转化为实际策略，并在历史数据中进行验证的工具。无论您是想测试一个新的交易指标，还是评估一个自动化交易流程的效果，khQuant 都致力于为您提供一个便捷、高效的平台来测试和验证您的交易想法。

### 阅读对象

本教程主要面向 **khQuant 软件的使用者**。无论您是量化交易的新手，还是有一定经验的交易者，只要您希望利用本软件进行策略的回测分析，本教程都将为您提供必要的指导。本教程侧重于软件的**操作和使用**。

### 主要功能概览

khQuant 提供了以下核心功能，帮助您更好地进行量化策略研究：

* **图形用户界面 (GUI)**: 提供直观易用的操作界面，方便您配置参数、运行策略和查看结果。
* **核心运行模式**: 支持策略的 **历史回测**，让您可以在历史数据上检验策略效果。
* **便捷的数据管理**: 内置数据下载工具，方便获取股票历史行情数据。
* **策略执行与监控**: 在回测过程中实时显示模拟的账户资金、持仓变动、订单状态和策略日志。
* **结果查看**: 提供回测报告或关键指标展示，帮助评估策略表现。

### 与其他平台的简要对比

您可能了解或使用过其他的量化交易平台。下表旨在帮助您理解 khQuant 与常见的 vn.py 和 Backtrader 平台之间的主要差异和侧重点：


| 特性               | khQuant                                                                                                 | vn.py                                                        | Backtrader                                             |
| :----------------- | :------------------------------------------------------------------------------------------------------ | :----------------------------------------------------------- | :----------------------------------------------------- |
| **主要定位**       | **集成图形界面的回测平台**，具备 **标准化的 Python 策略框架**                                           | 功能全面的**综合性量化平台** (覆盖回测、模拟、实盘)          | 专注于策略回测的**纯 Python 框架库**                   |
| **易用性**         | **高** (GUI 驱动友好，同时核心框架也可独立使用)                                                         | 中等 (功能强大但配置和学习曲线较陡峭)                        | 低 (纯代码驱动，需要扎实的 Python 编程能力)            |
| **灵活性与扩展性** | **高** (标准化框架易于维护; **GUI 与核心逻辑低耦合, 支持纯代码运行**; 为 AI 生成策略等集成提供良好基础) | 高 (模块化设计，支持多种交易接口和自定义开发)                | **极高** (完全代码控制，提供最大的自定义可能性)        |
| **框架设计**       | **结构清晰、标准化** (降低策略开发复杂度，利于 AI 对接与团队协作); **GUI 与核心分离**                   | 事件驱动，模块化                                             | 核心精简，高度灵活，依赖用户自行构建                   |
| **核心依赖**       | **深度绑定 miniQMT / xtquant** (数据和未来交易依赖迅投生态)                                             | 相对独立 (支持对接多种 CTP/API 接口及数据源)                 | 框架独立 (用户需自行解决数据和交易接口对接)            |
| **典型用户**       | 偏好 GUI 操作、需快速回测验证、**或希望基于标准框架进行代码回测**、关注 AI 策略应用、miniQMT 用户       | 需要完整交易周期支持(含实盘)、有一定开发能力的专业用户或机构 | 资深 Python 开发者、进行复杂算法研究、需极致定制化用户 |
| **核心优势**       | **易用性 (GUI) 与 结构化 (标准框架) 的结合**, **支持 GUI 或纯代码运行模式**, **AI 策略集成潜力**        | **功能覆盖全面** (从研究到实盘)，成熟稳定，社区支持广泛      | **无与伦比的代码灵活性和控制力**，深度研究利器         |

总的来说，如果您希望通过图形界面快速进行策略的历史回测，或者倾向于使用一个结构清晰、标准化的框架进行代码驱动的回测（该框架的低耦合设计也为后续引入 AI 生成策略等扩展提供了便利），并且主要在 miniQMT 生态内操作，khQuant 提供了一个非常有吸引力的选择。如果您需要直接支持广泛的实盘接口、进行高度复杂的代码级自定义开发或纯粹的代码级深度研究，vn.py 或 Backtrader 可能是更合适的选项。

### 为什么开发 khQuant？

您可能会好奇，在已有不少量化工具的情况下，为什么还要开发 khQuant？这主要源于以下几点考虑，这些考虑也旨在最终为用户带来更好的体验：

1. **追求开放与灵活的潜力**: 许多现有系统在策略实现上可能存在限制，特别是在引入最新的 AI 算法方面。khQuant 基于 `xtquant` 构建，旨在保留最大的灵活性，其标准化的策略框架也为未来集成更先进的分析方法（例如 AI 生成策略）打下了基础，希望能为用户的策略探索提供更广阔的空间。
2. **保障策略安全与本地运行**: 策略是量化交易的核心。khQuant 确保您的策略代码完全在本地计算机上运行，仅通过接口与 miniQMT 交互获取行情和（未来可能的）执行交易，这是保障策略安全性的最佳方式。同时，本地运行也为处理大规模数据或复杂计算提供了性能优势。
3. **打造称手易用的工具**: 软件终究是工具，目的是服务于策略研究。khQuant 的开发目标是打造一款"称手好用"的兵器——在保证专业功能的同时，力求界面友好、操作便捷，让用户能将更多精力投入到策略开发与验证本身，而不是被复杂的工具所困扰。
4. **丰富 miniQMT 生态选择**: 针对 miniQMT 用户，khQuant 希望填补一个空白，提供一个集成化的、易于使用的回测平台选项，为大家在该生态下的量化研究提供更多便利。

希望本教程能帮助您快速上手 khQuant，开启您的量化策略研究之旅！

---

## 第一章：准备工作

本章将指导您完成运行 khQuant 前所需的所有准备工作，包括检查运行环境、获取必要的软件以及进行首次配置。

### 1.1 软件运行环境

在开始之前，请确保您的计算机满足以下基本要求：

* **操作系统**: 目前 khQuant 主要在 **Windows** 操作系统上进行开发和测试。推荐使用 Windows 10 或更高版本。
* **Python 环境 (可选)**:
  * 如果您直接运行源代码，需要安装 Python。推荐使用 **Python 3.8 或更高版本**。
  * 为了避免与其他 Python 项目产生冲突，建议您创建一个独立的虚拟环境 (例如使用 `venv` 或 `conda`) 来运行 khQuant。 (如果您不熟悉虚拟环境，可以暂时跳过这一步，但推荐学习使用)。

### 1.2 获取与安装

接下来，您需要获取 khQuant 软件本身以及它依赖的 miniQMT 客户端。

* **下载 khQuant**:

  * 请从 [此处填写 khQuant 的发布页面或下载链接] 下载最新的 khQuant 软件包。
  * 下载后，将其解压到您希望存放软件的目录，例如 `D:\khQuant`。
* **安装依赖库 (如果需要)**:

  * 如果您下载的是源代码包或需要手动安装依赖，请打开命令行/终端，进入 khQuant 的目录，然后运行以下命令来安装所需库：

  ```bash
  pip install -r requirements.txt 
  ```

  * (如果提供了包含依赖的打包版本，则可以忽略此步骤)。
* **安装 miniQMT 客户端**:

  * khQuant 需要依赖 **迅投的 miniQMT 客户端** 来获取行情数据和执行交易（即使是回测也需要其数据服务）。
  * 请访问 [迅投官网](https://www.xtquant.com/) 或相关渠道下载并安装最新版本的 miniQMT 客户端。
  * **重要提示**: 安装 miniQMT 后，请务必 **至少运行一次** 并使用您的 **模拟账号** 或 **实盘账号** 登录，以便客户端完成初始化配置和数据下载。回测功能也需要 miniQMT 客户端提供基础数据服务。

### 1.3 首次启动与配置

完成上述步骤后，您可以首次启动 khQuant 并进行关键配置。

* **启动 khQuant**:
  * 进入您解压或存放 khQuant 的目录。
  * 找到并运行启动脚本或主程序文件 (通常可能是 `GUIkhQuant.py` 或一个名为 `khQuant.exe` 的可执行文件)。
  * 如果一切顺利，您将看到 khQuant 的主界面。
* **配置 miniQMT 路径**:
  * 这是 **至关重要** 的一步。khQuant 需要知道您的 miniQMT 客户端安装在哪里。
  * 在 khQuant 主界面，点击菜单栏的 "设置" -> "软件设置" (或工具栏上的设置图标)。
  * 在弹出的 "软件设置" 对话框中，找到 "miniQMT客户端路径" 的设置项。
  * 点击 "浏览..." 按钮，找到并选中您 miniQMT 的主程序文件 (通常是 `miniQMT.exe`)。
  * 路径选择正确后，点击 "保存设置"。
* **(可选) 配置其他参数**:
  * 在 "软件设置" 对话框中，您还可以配置 "无风险收益率" 等参数，这些参数主要用于回测结果的指标计算。您可以暂时使用默认值，以后根据需要再调整。

完成以上所有准备工作后，您的 khQuant 就基本就绪了！接下来，您可以开始熟悉软件界面和进行数据管理了。

---

## 第二章：熟悉操作界面 (GUI)

### 2.1 主窗口导览

* 认识界面布局：左侧参数区、中间显示区、右侧信息区。
* **工具栏**: 常用按钮的功能（启动/停止策略、数据管理、设置等）。
* **菜单栏**: 各菜单项的功能介绍。
* **状态栏**: 如何看懂底部状态提示信息。

### 2.2 参数配置区详解 (左侧)

* **运行模式**: 如何选择 "回测"、"模拟" 或 "实盘"。
* **策略选择**: 如何加载你的策略文件 (.py)。
* **时间与周期**: 设置回测/运行的开始结束日期、K 线频率 (日/分钟/Tick)。
* **股票池**:
  * 选择常用指数成分股 (如沪深300)。
  * 如何导入自己的股票列表 (CSV 文件格式说明)。
  * 如何添加、删除、清空当前列表中的股票。
* **交易参数**:
  * 设置账户初始资金。
  * 设置滑点（简单解释什么是滑点以及如何设置）。
  * 设置手续费和印花税（简单解释这些成本）。
  * 选择复权方式（简单解释什么是复权）。
* **触发方式**: 选择策略按什么频率执行（例如，每分钟、特定时间点）。
* **其他配置**: 如保存配置、加载配置等。

### 2.3 信息显示区 (中间 & 右侧)

* **账户信息**: 查看资金、市值、盈亏等。
* **持仓信息**: 查看当前持有的股票、数量、成本、盈亏等。
* **委托与成交**: 查看交易指令的发送和成交情况。
* **日志窗口**:
  * 理解不同级别的日志信息 (普通信息、警告、错误、交易)。
  * 如何过滤日志、清空日志、保存日志文件。
* **状态表格**: 查看策略运行中的关键状态信息。

---

## 第三章：数据管理

### 3.1 下载历史行情数据

* 打开 "数据管理" 模块。
* 选择要下载的股票 (来自文件)、数据字段 (开高低收量等)、时间周期、日期范围、复权方式。
* 开始下载并查看进度。
* 了解下载的数据保存在哪里。

### 3.2 更新行情数据

* 如何补充下载缺失的数据或更新到最新。

### 3.3 更新股票列表

* 通过 "软件设置" 或 "数据管理" 更新指数成分股等列表。

---

## 第四章：编写你的第一个策略 (入门)

### 4.1 策略文件是什么

* 简单说明策略文件就是一个包含交易逻辑的 Python 脚本。

### 4.2 策略基础

* 认识 `handle_data` 函数：这是策略的核心，每次行情更新时会被调用。
* `handle_data` 能获取什么信息 (当前时间、账户资金、持仓、行情数据)。

### 4.3 发出交易信号

* 如何使用内置工具 (`khQTTools.generate_signal`) 方便地生成买入或卖出信号。
* 示例：一个简单的均线交叉策略如何编写（提供代码片段和解释）。

### 4.4 在策略中打印日志

* 如何在策略里加入 `print` 或 `logging.info` 来输出信息，方便调试。

---

## 第五章：运行策略：回测、模拟与实盘

### 5.1 回测模式

* 用途：用历史数据检验策略效果。
* 操作步骤：配置好参数，选择回测模式，点击 "开始"。
* 如何查看回测结果：找到生成的结果文件/报告，理解关键指标 (收益、回撤等)。

### 5.2 模拟交易模式

* 用途：用实时行情模拟真实交易，但不产生实际盈亏。
* 操作步骤：配置好参数，选择模拟模式，连接行情服务器 (miniQMT 需登录)，点击 "开始"。
* 观察要点：实时查看账户、持仓变化，检查交易信号是否按预期执行。

### 5.3 实盘交易模式

* **极其重要**: 实盘交易涉及真实资金，请务必谨慎，充分测试策略，了解风险！
* 操作步骤：确保策略稳定可靠，配置好参数，选择实盘模式，连接实盘账户 (miniQMT 需登录实盘账户)，点击 "开始"。
* 监控：密切关注账户资金、持仓、订单状态，及时处理异常。

### 5.4 启动与停止

* 如何安全地启动和停止正在运行的策略。

---

## 第六章：理解交易过程与结果

### 6.1 查看交易活动

* 在 GUI 的 "委托" 和 "成交" 标签页中跟踪订单状态。
* 在日志窗口中查看 "TRADE" 级别的日志，了解交易细节。

### 6.2 理解交易成本

* 回顾 GUI 中设置的手续费、印花税、滑点参数。
* 了解这些成本如何影响最终的交易价格和盈亏。

### 6.3 查看账户与持仓

* 实时监控 "账户信息" 和 "持仓信息" 面板的变化。
* 理解各个字段的含义 (可用资金、总资产、持仓市值、浮动盈亏等)。

---

## 第七章：常见问题解答 (FAQ)

### 7.1 安装与启动问题

* 软件打不开怎么办？
* 提示缺少 xxx 库怎么办？
* miniQMT 路径配置不正确。

### 7.2 连接问题

* 连接不上 miniQMT 客户端。
* 模拟/实盘模式无法获取行情或交易。

### 7.3 数据问题

* 数据下载很慢或失败。
* 回测时提示缺少数据。

### 7.4 策略运行问题

* 策略加载失败 (检查 .py 文件)。
* 策略运行报错 (查看日志窗口的 ERROR 信息)。
* 回测/模拟结果与预期不符 (检查策略逻辑、参数配置)。

### 7.5 交易问题

* 为什么没有下单？(检查日志是否有资金/持仓不足的错误)。
* 实际成交价和策略里的价格不一样？(滑点和成本的影响)。

### 7.6 如何寻求帮助

* 通过 "软件设置" -> "反馈问题" 页面。
* (可选) 其他联系方式。

---

## 附录

### A.1 术语解释

* 简单解释教程中用到的一些名词 (如：回测、滑点、复权、K线、Tick 等)。

---
