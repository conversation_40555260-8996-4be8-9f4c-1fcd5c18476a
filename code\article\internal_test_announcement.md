> 我是Mr.看海，我在尝试用信号处理的知识积累和思考方式做量化交易，应用深度学习和AI实现股票自动交易，目的是实现财务自由\~
> 目前我正在开发基于miniQMT的量化交易系统——看海量化交易系统（KhQuant 是其核心框架）。
各位关注"看海量化交易平台"（khQuant）的朋友们，大家好！

我是看海。首先，由衷地感谢大家一直以来的关注与支持。在经历了无数个日夜的开发与打磨后，我非常激动地向大家宣布：**看海量化交易平台即将迎来一次重大的版本更新，我们正式将新版本定义为V2.0！**

在正式向所有用户推送之前，为了确保软件的稳定与高效，我们将启动一个小范围的**内部测试阶段**。

这篇文章，将向大家详细介绍本次内测活动，并解答您可能关心的几个核心问题。

## 1. 精益求精：为什么需要一个内测阶段？

大家可能会问，为什么不直接发布正式版？

这主要是因为，一个软件在开发者的电脑上运行流畅，不代表在所有用户那里都能完美工作。尽管我已尽最大努力去完善系统，但不可避免地会陷入自己的"开发者思维定式"。为了跳出这个定式，确保软件的普适性和稳定性，小范围的内部测试就显得至关重要。这和很多游戏在公测前需要进行封闭内测是一个道理，其目的在于：

* **发现兼容性暗礁**：开发环境是固定的，但不同用户的电脑配置、Windows版本、屏幕分辨率、网络环境各不相同。内测可以最大范围地暴露在不同环境下可能出现的兼容性问题，避免公开发布后出现大面积的"水土不服"。
* **检验真实使用流程**：开发者的操作习惯是固定的，但用户的真实使用路径是千变万化的。内测能帮助发现那些意想不到的交互冲突或设计不合理之处，从而优化用户体验，让软件不仅能用，而且好用。
* **聚焦关键问题**：相比于公开发布后海量的、零散的反馈，小范围测试能更高效地暴露和集中解决最突出的兼容性或功能性问题，为后续的全面开放扫清主要障碍。
* **完善文档与教程**：通过内测阶段的实践检验，可以发现并补充现有用户手册中的模糊或遗漏之处，确保文档的清晰易懂。

总而言之，内测是连接开发者与用户的桥梁，旨在通过核心用户的协作，共同将khQuant打磨成一把真正的量化利器。

## 2. 如何成为核心一员：怎样参与本次内测？

本次内测资格，将**优先开放给内部交流群的成员**。

这是我们回馈核心支持者的一种方式。一直以来，khQuant都是一个免费、用爱发电的开源项目，其开发与维护的动力，很大一部分来源于通过我推荐渠道开通MiniQMT账户的朋友们所带来的支持，以及热心用户的慷慨打赏。

**参与内测的方式非常明确：**

> **通过作者提供的推荐渠道开通MiniQMT账户后，联系作者"看海"，即可受邀加入内部交流群，自动获得本次及后续所有新版本的内测资格。**

如果您已经是内部群成员，无需任何额外操作，敬请期待群内的下载链接和通知！

## 3. 蓄势待发：何时正式发布？

内测阶段的具体时长目前还不好预计。这是一个集中力量迭代完善的时期，在此过程中，会根据收到的反馈问题情况，来评估正式版的发布时间。内测阶段的核心工作包括：

* **高效迭代**：根据内测反馈，快速修复问题、优化体验，并敏捷地发布更新版本。
* **完善文档**：同步完成《用户手册》的修订和全新的《策略编写指南》的撰写，为公测铺平道路。
* **社区支持**：在内部群提供及时的技术支持。

项目的核心原则是"质量优先"。一旦软件达到一个稳定状态，便会启动正式版的发布流程，并同步将V2.0的全部源码开源。

在此期间，公众号会持续更新内测的进展和成果，欢迎大家持续关注。

## 4. 不止于此：我们的未来开发计划

V2.0是一个新的里程碑，但绝不是终点。借此机会，也向大家分享我们后续的开发蓝图。在下一个大的版本更新中，我们将重点推进以下方向：

* **AI策略生成集成**：我们计划将AI（特别是大语言模型）的能力集成到图形界面中，实现策略代码的辅助生成。用户也许只需输入策略思想，就能获得一个基础可用的策略框架，极大地降低量化入门的门槛。
* **交易品类与市场扩展**：在稳定支持股票的基础上，逐步增加对**ETF、可转债**等更多交易品类的支持，丰富平台的可玩性和适用性。
* **扩展数据源支持**：针对MiniQMT在部分长周期历史数据上的不足，未来计划加入更多第三方数据源的支持，解决数据获取的短板，为长周期的策略研究提供坚实基础。

---

最后，再次感谢每一位同行的朋友。无论是参与内测，还是在论坛提出建议，甚至只是默默关注，您的每一份支持，都是看海量化项目持续前进的最大动力。

让我们共同期待一个更强大、更稳定的khQuant V2.0的到来！

<div align="right">
<strong>看海</strong>
<br>
<strong>2025年6月26日</strong>
</div>
