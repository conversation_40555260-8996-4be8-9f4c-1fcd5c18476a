<!DOCTYPE html>
<html>
<head>
<title>KHQuant_Manual_Chapter8.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E7%AC%AC%E5%85%AB%E7%AB%A0%E6%B4%9E%E5%AF%9F%E8%BF%90%E8%A1%8C%E5%8F%B3%E4%BE%A7%E4%BF%A1%E6%81%AF%E5%8F%8D%E9%A6%88%E5%8C%BA">第八章：洞察运行：右侧信息反馈区</h1>
<p>右侧面板是您观察策略思想与市场现实碰撞过程的&quot;黑匣子&quot;。它忠实地记录了从软件启动到策略运行的每一个关键步骤。学会解读这里的日志信息，是您调试策略、理解行为、发现问题的核心技能。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/右侧面板.png" alt="右侧面板" width="50%" />
</p>
<hr>
<h2 id="81-%E6%97%A5%E5%BF%97%E7%B3%BB%E7%BB%9F%E7%9A%84%E6%A0%B8%E5%BF%83%E6%9E%84%E6%88%90">8.1 日志系统的核心构成</h2>
<p>本软件的日志系统设计围绕两大核心组件展开，两者相辅相成，共同构成了完整的日志监控体系：</p>
<ul>
<li><strong>后台文件日志 (<code>app.log</code>)</strong>：作为最详尽的记录载体，它捕捉软件运行期间的各类事件，为深度问题排查提供依据。</li>
<li><strong>前台界面日志</strong>：一个可视化的窗口，实时展示关键的运行状态、策略输出和交易活动，提供直观的监控体验。</li>
</ul>
<h3 id="811-%E5%90%8E%E5%8F%B0%E6%96%87%E4%BB%B6%E6%97%A5%E5%BF%97%E5%AE%8C%E6%95%B4%E8%AE%B0%E5%BD%95%E8%BF%90%E8%A1%8C%E8%BD%A8%E8%BF%B9">8.1.1 后台文件日志：完整记录运行轨迹</h3>
<p>软件在启动时，会自动在程序根目录下的文件夹内创建或覆盖一个名为 <code>app.log</code> 的文本文件。这个文件是系统运行状态最详尽的记录载体。它会捕捉软件从启动到退出的整个生命周期内的各类事件，包括但不限于配置文件的加载与保存、策略的启动指令、运行状态变化以及最终的停止信号。</p>
<p>更重要的是，当软件遭遇预期之外的异常时，详细的错误信息和程序执行堆栈（Traceback）会被完整地记录下来，为问题排查提供了关键线索。</p>
<blockquote>
<p>💡 <strong>日志级别与覆盖规则</strong></p>
<p>为了确保信息的全面性，文件日志默认记录 <code>DEBUG</code> 及以上所有级别的信息。需要注意的是，该日志文件在<strong>每次软件重新启动时会被重写</strong>，因此只保留当前运行周期的日志。若需长期存档，建议在关闭软件前手动备份该文件。</p>
</blockquote>
<h3 id="812-%E5%89%8D%E5%8F%B0%E7%95%8C%E9%9D%A2%E6%97%A5%E5%BF%97%E5%AE%9E%E6%97%B6%E7%8A%B6%E6%80%81%E7%9A%84%E5%8F%AF%E8%A7%86%E5%8C%96%E7%AA%97%E5%8F%A3">8.1.2 前台界面日志：实时状态的可视化窗口</h3>
<p>为了让用户能够直观、实时地掌握软件的运行状态和策略执行情况，后台记录的关键日志信息会被同步呈现在软件主界面的右侧&quot;系统日志&quot;面板中。这个面板是用户观察软件内部活动的主要窗口，它会实时显示核心的运行状态更新、策略通过 <code>print</code> 或日志模块输出的信息、发生的交易行为以及重要的系统警告与错误提示。</p>
<hr>
<h2 id="82-%E7%95%8C%E9%9D%A2%E6%97%A5%E5%BF%97%E7%9A%84%E7%89%B9%E6%80%A7%E4%B8%8E%E4%BA%A4%E4%BA%92%E5%8A%9F%E8%83%BD">8.2 界面日志的特性与交互功能</h2>
<p>用户界面的日志显示区域并非后台日志的简单复刻，它融入了多项旨在提升用户体验和信息获取效率的设计。</p>
<h3 id="821-%E6%97%A5%E5%BF%97%E7%BA%A7%E5%88%AB%E4%B8%8E%E9%A2%9C%E8%89%B2%E5%8C%BA%E5%88%86">8.2.1 日志级别与颜色区分</h3>
<p>为了帮助用户在信息流中快速抓住重点，不同重要程度的日志被赋予了不同的颜色标识，使得用户能够迅速定位到关键的错误警报或重点关注的交易活动。</p>
<ul>
<li><code>[DEBUG]</code> (调试): <strong>浅紫色</strong>，主要用于开发者调试，信息较为冗余。</li>
<li><code>[INFO]</code> (信息): <strong>白色</strong>，常规运行信息，如启动/停止、配置更改等。</li>
<li><code>[WARNING]</code> (警告): <strong>橙色</strong>，潜在问题或需要用户注意的情况。</li>
<li><code>[ERROR]</code> (错误): <strong>红色</strong>，发生了错误，可能影响正常运行。</li>
<li><code>[TRADE]</code> (交易): <strong>蓝色</strong>，专门用于记录交易相关的委托和成交信息。</li>
</ul>
<h3 id="822-%E6%97%A5%E5%BF%97%E8%BF%87%E6%BB%A4%E4%B8%8E%E7%AE%A1%E7%90%86%E5%B7%A5%E5%85%B7">8.2.2 日志过滤与管理工具</h3>
<p>随着软件运行时间的增长，日志信息可能会变得非常庞杂。界面日志区域下方提供了一组复选框和管理按钮，允许用户聚焦核心信息并进行便捷操作。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/日志按钮.png" />
</p>
<ul>
<li><strong>日志过滤</strong>: 通过勾选 <code>DEBUG</code>, <code>INFO</code>, <code>WARNING</code>, <code>ERROR</code>, <code>TRADE</code> 等复选框，可以动态过滤日志。每次勾选状态的改变都会即时生效，让关键信息一目了然。</li>
<li><strong>清空日志</strong>: 点击可以迅速清除当前界面上显示的所有日志内容（此操作不影响后台的 <code>app.log</code> 文件）。</li>
<li><strong>保存日志</strong>: 点击后，系统会将当前界面显示的（已应用过滤规则的）日志内容导出到一个用户指定的文本文件中，方便存档或分享。</li>
<li><strong>测试日志</strong>: 点击会生成几条不同级别的模拟日志，用于快速验证日志系统的显示和颜色功能是否正常。</li>
<li><strong>打开回测指标</strong>: 这是通往策略绩效复盘的<strong>核心入口</strong>。此按钮在平时是灰色不可用状态，<strong>只有在一次回测成功运行结束后</strong>才会被激活。点击后将打开详细的回测报告窗口。</li>
</ul>
<h3 id="823-%E7%89%B9%E6%AE%8A%E6%97%A5%E5%BF%97%E7%9A%84%E5%B7%AE%E5%BC%82%E5%8C%96%E5%A4%84%E7%90%86">8.2.3 特殊日志的差异化处理</h3>
<p>系统对特定类型的日志进行了特殊处理以优化信息呈现。例如，对于回测过程中的进度反馈（如 &quot;回测进度: 55.3%&quot;），这类信息虽然也会在后台日志中记录，但在界面上并不会直接逐条打印，而是被用来驱动主窗口底部状态栏中的<strong>进度条</strong>进行实时更新。这种处理方式避免了大量进度信息刷屏，同时提供了更为直观、集中的进度展示。</p>
<hr>
<h2 id="83-%E5%9C%A8%E7%AD%96%E7%95%A5%E4%B8%AD%E4%BD%BF%E7%94%A8%E6%97%A5%E5%BF%97">8.3 在策略中使用日志</h2>
<p>为了方便调试和监控策略的内部状态，您可以在策略代码中直接调用日志输出功能，将信息发送到看海量化交易系统的日志系统。</p>
<p>您可以根据需要输出不同级别的日志信息，这些级别与界面上显示的颜色直接对应：</p>
<ul>
<li><strong>普通信息 (INFO)</strong>: 使用 <code>self.log(&quot;进入长仓条件判断&quot;, &quot;INFO&quot;)</code> 或类似语句输出常规的流程信息或变量状态。这对应界面上的 <strong>白色</strong> 文本。</li>
<li><strong>调试信息 (DEBUG)</strong>: 如果需要输出更详细的、仅在调试时关心的变量值，可以使用 <code>self.log(&quot;当前ATR值&quot;, &quot;DEBUG&quot;)</code>。这对应界面上的 <strong>浅紫色</strong> 文本。</li>
<li><strong>警告信息 (WARNING)</strong>: 当策略遇到一些非致命但需要注意的情况时，比如某个数据获取失败但有备用方案，可以使用 <code>self.log(&quot;无法获取最新行情，使用上一周期数据代替&quot;, &quot;WARNING&quot;)</code>。这对应界面上的 <strong>橙色</strong> 文本。</li>
<li><strong>错误信息 (ERROR)</strong>: 当策略发生严重错误，可能导致后续逻辑无法正常执行时，应使用 <code>self.log(&quot;计算指标时出现除零错误&quot;, &quot;ERROR&quot;)</code>。这对应界面上最醒目的 <strong>红色</strong> 文本。</li>
<li><strong>交易信息 (TRADE)</strong>: 虽然系统会自动记录委托和成交回报，但如果您想在策略逻辑中额外标记关键的交易决策点，也可以使用 <code>self.log(f&quot;信号触发，准备买入&quot;, &quot;TRADE&quot;)</code>。这对应界面上的 <strong>蓝色</strong> 文本。</li>
</ul>
<blockquote>
<p><strong>如何输出醒目的内容？</strong></p>
<p>如果您希望某条日志信息在界面上特别突出，最直接的方式是使用 <code>WARNING</code> 或 <code>ERROR</code> 级别。<code>ERROR</code> 级别（红色）最为醒目，通常用于指示发生了必须处理的问题。<code>WARNING</code> 级别（橙色）也比较突出，适合用于提示潜在风险或需要关注的状态。请根据信息的重要性和紧急程度，审慎选择合适的级别进行输出。</p>
</blockquote>
<p>在下一章，我们将深入那个最激动人心的部分——详细解读回测报告，真正开始对您的策略进行定量评估。</p>

</body>
</html>
