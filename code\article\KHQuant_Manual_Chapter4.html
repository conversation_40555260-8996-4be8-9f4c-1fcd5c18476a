<!DOCTYPE html>
<html>
<head>
<title>KHQuant_Manual_Chapter4.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E7%AC%AC%E5%9B%9B%E7%AB%A0%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B%E8%BF%90%E8%A1%8C%E7%AC%AC%E4%B8%80%E4%B8%AA%E5%9B%9E%E6%B5%8B">第四章：快速上手：运行第一个回测</h1>
<p>本章将引导使用一个预先配置好的工程文件（<code>.kh</code>文件），来快速完整地运行一次回测。这个过程不仅能直观地了解软件的基本操作流程，也是一个检验当前运行环境是否配置妥当、能否与本系统完美兼容的有效方式。</p>
<h2 id="41-%E5%8A%A0%E8%BD%BD%E5%B9%B6%E9%85%8D%E7%BD%AE%E7%A4%BA%E4%BE%8B%E5%B7%A5%E7%A8%8B">4.1 加载并配置示例工程</h2>
<p>软件中内置了一些经典的策略示例，以供快速学习和测试。下面将加载其中一个进行演示。</p>
<ol>
<li>
<p>找到并点击主界面顶部工具栏上的 <strong>&quot;加载配置&quot;</strong> 按钮。</p>
</li>
<li>
<p>在弹出的文件选择对话框中，找到软件自带的<code>strategies</code>目录。</p>
</li>
<li>
<p>选择示例工程文件 <code>demoMACD.kh</code>，然后点击&quot;打开&quot;。</p>
</li>
<li>
<p><strong>检查并重设策略文件路径</strong>：由于每个用户的软件安装路径可能不同，加载 <code>.kh</code> 文件后，可能需要手动重新指定策略文件的位置。点击&quot;策略文件&quot;输入框右侧的浏览按钮，在<code>strategies</code>目录中选择 <code>MACD.py</code> 文件。</p>
<blockquote>
<p>💡 <strong>提示</strong>：设置好正确的策略路径后，可以点击工具栏上的 <strong>&quot;保存配置&quot;</strong> 按钮覆盖原有的 <code>demoMACD.kh</code> 文件。这样，下次加载时就不再需要重新选择了。</p>
</blockquote>
</li>
</ol>
<p>加载成功并设置好策略路径后，主界面参数会自动填充完毕。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/加载后的参数.png" />
</p>
<p align="center">加载并配置好工程文件后的主界面</p>
<blockquote>
<p>💡 <strong>什么是 <code>.kh</code> 文件？</strong></p>
<p><code>.kh</code> 文件是&quot;看海量化交易系统&quot;的<strong>工程配置文件</strong>。它就像是工作台的快照，完整地保存了在图形界面上所做的所有设置，包括选择的策略脚本、回测时间、股票池、交易费率等等。</p>
<p><strong>重点在于，<code>.kh</code> 文件本质上是一个可读的文本文件（使用JSON格式）。</strong> 这意味着完全可以用任何文本编辑器（如记事本、VS Code等）打开它，来查看甚至直接修改其中的配置参数。</p>
</blockquote>
<h2 id="42-%E5%87%86%E5%A4%87%E5%9B%9E%E6%B5%8B%E6%95%B0%E6%8D%AE%E6%95%B0%E6%8D%AE%E8%A1%A5%E5%85%85">4.2 准备回测数据（数据补充）</h2>
<p>在启动回测之前，需要确保本地数据库中包含了策略所需的全部历史数据。本次示例回测的是华林证券（<code>002945.SZ</code>）在 <code>2024-04-03</code> 至 <code>2024-11-01</code> 这段时间内的表现，使用的是分钟数据，因此需要先将这段时间的分钟数据补充到本地。</p>
<ol>
<li>在主界面顶部工具栏点击 <strong>&quot;数据&quot;</strong> 按钮，打开数据中心模块。</li>
<li>在数据中心，按照以下方式进行设置：
<ul>
<li><strong>股票列表</strong>: 在&quot;自选清单&quot;中输入 <code>002945.SZ,华林证券</code>，并勾选它。【这里应该是点击“自选清单”，然后在文本编辑器，比如记事本中输入002945.SZ,华林证券，然后保存，然后勾选自选清单】</li>
<li><strong>周期类型</strong>: 勾选 <code>1分钟</code>。</li>
<li><strong>日期范围</strong>: 设置开始日期为 <code>2024-04-03</code>，结束日期为 <code>2024-11-01</code>。</li>
</ul>
</li>
<li>确认设置无误后，点击 <strong>&quot;补充数据&quot;</strong> 按钮。系统将开始下载对应的数据并存入本地数据库。</li>
</ol>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/补充数据.gif" />
</p>
<p align="center">数据补充模块的设置方式</p>
<p>等待数据补充完成的提示出现后，就可以进行下一步的回测了。</p>
<h3 id="%22%E6%95%B0%E6%8D%AE%E8%A1%A5%E5%85%85%22%E4%B8%8E%22%E6%95%B0%E6%8D%AE%E4%B8%8B%E8%BD%BD%22%E7%9A%84%E5%8C%BA%E5%88%AB">&quot;数据补充&quot;与&quot;数据下载&quot;的区别</h3>
<blockquote>
<p><strong>&quot;数据补充&quot;</strong> 的设计目标是服务于 <strong>系统内部</strong> 的回测功能。它的核心任务是更新和完善 MiniQMT 内部所依赖的历史数据库，为策略回测引擎提供坚实的数据基础。换句话说，回测系统中使用到的数据，通常不是临时从网络获取的，而是通过&quot;数据补充&quot;提前下载到本地的。这样做可以极大提升回测速度。技术上讲，&quot;数据补充&quot;调用的是xtquant的<code>download_history_data</code>函数，而回测过程中则通过<code>get_market_data_ex</code>来高速读取本地数据。</p>
</blockquote>
<blockquote>
<p><strong>&quot;数据下载&quot;</strong> 的核心在于将金融数据以独立、可见的 <strong><code>.csv</code> 文件</strong> 形式提供给用户，支持 <strong>系统外部</strong> 的多元化数据应用。用户可以方便地将数据导入 Excel、Python、R 等进行复杂的统计建模或外部回测。</p>
</blockquote>
<h2 id="43-%E5%BC%80%E5%A7%8B%E5%9B%9E%E6%B5%8B%E4%B8%8E%E8%A7%82%E5%AF%9F">4.3 开始回测与观察</h2>
<p>当工程文件加载完毕，数据也准备就绪后，就可以启动回测了。</p>
<ol>
<li>
<p>点击工具栏上的 <strong>&quot;开始运行&quot;</strong> 按钮。</p>
</li>
<li>
<p>此时，回测开始进行，可以将注意力转移到界面的以下两个区域：</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/bandicam-2025-06-12-01-28-29-193.gif" />
</p>
<ul>
<li><strong>右侧的&quot;系统日志&quot;面板</strong>：这里会实时滚动输出策略运行的详细信息，包括数据下载进度、策略初始化状态、交易信号的触发、订单的委托与成交等。如果出现任何问题，错误信息也会在这里以醒目的颜色显示。</li>
<li><strong>底部的状态栏</strong>：这里会显示一个详细的进度条，告知当前回测进行到了哪一天，以及总体的完成百分比。</li>
</ul>
</li>
</ol>
<h2 id="44-%E8%A7%A3%E8%AF%BB%E7%AC%AC%E4%B8%80%E4%B8%AA%E5%9B%9E%E6%B5%8B%E6%8A%A5%E5%91%8A">4.4 解读第一个回测报告</h2>
<p>等待回测进度条走到100%，系统日志中也会提示&quot;回测运行结束&quot;。此时，回测报告将自动弹出。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/回测结果分析.png" />
</p>
<p align="center">回测报告窗口</p>
<p>如果后续不小心关闭了报告窗口，可以随时点击日志面板上方的 <strong>&quot;打开回测指标&quot;</strong> 按钮来重新打开它。</p>
<p>这份报告浓缩了策略在历史数据中的全部表现，主要包含：</p>
<ul>
<li><strong>核心绩效指标</strong>：如总收益率、年化收益、最大回撤、夏普比率等。</li>
<li><strong>可视化图表</strong>：包括策略净值与基准对比的资金曲线、回撤曲线等。</li>
<li><strong>详细交易记录</strong>：每一笔买入和卖出的明细。</li>
<li><strong>每日持仓与资产快照</strong>：方便复盘策略在任何一天的具体状态。</li>
</ul>
<p>关于如何深入解读这份报告中的每一项数据，我们将在后续的【第九章：策略绩效复盘】中进行详尽的拆解。现在，只需要对它有一个初步的印象即可。</p>
<p>恭喜！已经成功完成了在KHQuant中的第一次策略回测。接下来，可以尝试更深入的策略研究了。</p>

</body>
</html>
