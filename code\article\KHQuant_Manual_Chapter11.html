<!DOCTYPE html>
<html>
<head>
<title>KHQuant_Manual_Chapter11.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%E7%AC%AC%E5%8D%81%E4%B8%80%E7%AB%A0%E4%B8%AA%E6%80%A7%E5%8C%96%E9%85%8D%E7%BD%AE%E8%BD%AF%E4%BB%B6%E8%AE%BE%E7%BD%AE%E8%AF%A6%E8%A7%A3">第十一章：个性化配置：软件设置详解</h1>
<p>&quot;看海量化交易系统&quot;提供了一个独立的&quot;软件设置&quot;对话框，用于管理那些独立于具体策略、具有全局性或不常变动的参数。理解并正确配置这些选项，将有助于提升使用体验，并确保软件与关键依赖（如MiniQMT）的顺畅通信。</p>
<hr>
<h2 id="111-%E5%A6%82%E4%BD%95%E6%89%93%E5%BC%80%22%E8%AE%BE%E7%BD%AE%22%E5%AF%B9%E8%AF%9D%E6%A1%86">11.1 如何打开&quot;设置&quot;对话框？</h2>
<p>在主界面顶部工具栏，点击 <strong>设置</strong> 按钮（齿轮图标⚙️），即可打开&quot;软件设置&quot;对话框。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/软件设置.png" alt="软件设置对话框" />
</p>
<hr>
<h2 id="112-%22%E5%9F%BA%E6%9C%AC%E5%8F%82%E6%95%B0%E8%AE%BE%E7%BD%AE%22%E4%B8%8E%22%E8%B4%A6%E6%88%B7%E8%AE%BE%E7%BD%AE%22">11.2 &quot;基本参数设置&quot;与&quot;账户设置&quot;</h2>
<ul>
<li>
<p><strong>无风险收益率 (Risk-Free Rate)</strong>:</p>
<ul>
<li><strong>功能</strong>: 设定一个年化的无风险收益率，主要用于计算夏普比率、索提诺比率等风险调整后收益指标。</li>
<li><strong>如何设置</strong>:在实际研究中，通常使用十年期国债收益率作为无风险利率的代理。您可以根据当前市场情况进行调整，可点击此处查看：<a href="https://cn.investing.com/rates-bonds/china-10-year-bond-yield"><ins>中国十年期国债收益率行情</ins></a>。</li>
</ul>
</li>
<li>
<p><strong>延迟显示日志 (Delay Log Display)</strong>:</p>
<ul>
<li><strong>功能</strong>: 勾选此项后，在策略高速回测期间，系统会将所有日志先缓存起来，待回测结束后再一次性显示在右侧日志面板。</li>
<li><strong>优势</strong>: 对于运行时间长、日志输出量大的回测，开启此功能可以避免因界面频繁刷新而导致的UI卡顿，从而显著提升回测性能和界面流畅度。</li>
</ul>
</li>
<li>
<p><strong>初始化行情数据 (Initialize Market Data)</strong>:</p>
<ul>
<li><strong>功能</strong>: 用于控制每次<strong>启动回测时</strong>，是否自动检查并补充本地缺失的历史数据。</li>
<li><strong>说明</strong>: <strong>此功能在当前版本中尚未完全启用。因此，无论是否勾选，启动回测时都不会自动补充数据。在开始回测前，请务必手动使用&quot;数据中心&quot;里的&quot;数据补充&quot;功能来确保数据完整。</strong></li>
</ul>
</li>
<li>
<p><strong>账户设置 (Account Settings)</strong>:</p>
<ul>
<li><strong>功能</strong>: 此为实盘/模拟盘功能预用选项，用于配置交易账户信息。</li>
<li><strong>说明</strong>: <strong>由于当前版本专注于回测功能，此处的账户设置不会对回测产生影响，暂时无需理会。</strong></li>
</ul>
</li>
</ul>
<hr>
<h2 id="113-%22%E8%82%A1%E7%A5%A8%E5%88%97%E8%A1%A8%E7%AE%A1%E7%90%86%22">11.3 &quot;股票列表管理&quot;</h2>
<ul>
<li><strong>更新成分股列表 (Update Constituent Stocks)</strong>:
<ul>
<li><strong>功能</strong>: 点击此按钮，系统会连接到网络，下载并更新本地存储的A股主要指数（如沪深300、中证500等）的最新成分股列表文件。这些文件保存在软件根目录下的 <code>data</code> 文件夹中。</li>
<li><strong>使用建议</strong>: 当您发现指数成分股发生较大调整，或者长时间未更新时，可以执行此操作。过程需要一定时间，请耐心等待，无需频繁点击。</li>
</ul>
</li>
</ul>
<hr>
<h2 id="114-%22%E5%AE%A2%E6%88%B7%E7%AB%AF%E8%AE%BE%E7%BD%AE%22">11.4 &quot;客户端设置&quot;</h2>
<p>这是整个设置对话框中<strong>至关重要</strong>的部分，它负责建立本软件与MiniQMT之间的连接。</p>
<ul>
<li>
<p><strong>miniQMT客户端路径 (miniQMT Client Path)</strong>:</p>
<ul>
<li><strong>功能</strong>: 指向您电脑上MiniQMT客户端的主程序文件 (<code>XtItClient.exe</code>)。</li>
<li><strong>如何设置</strong>: 点击 <strong>浏览...</strong> 按钮，找到您的MiniQMT安装目录，并进入 <code>bin.x64</code> 文件夹，选择 <code>XtItClient.exe</code> 文件。</li>
</ul>
</li>
<li>
<p><strong>QMT路径 (QMT Path)</strong>:</p>
<ul>
<li><strong>功能</strong>: 指向MiniQMT的用户数据文件夹 (<code>userdata_mini</code>)。框架需要从此文件夹读取账户配置、日志等信息。</li>
<li><strong>如何设置</strong>: 点击 <strong>浏览...</strong> 按钮，找到您的MiniQMT安装目录，并选择 <code>userdata_mini</code> 文件夹。</li>
</ul>
</li>
</ul>
<hr>
<h2 id="115-%22%E7%89%88%E6%9C%AC%E4%BF%A1%E6%81%AF%22%E4%B8%8E%22%E9%97%AE%E9%A2%98%E5%8F%8D%E9%A6%88%22">11.5 &quot;版本信息&quot;与&quot;问题反馈&quot;</h2>
<ul>
<li>
<p><strong>版本信息 (Version Info)</strong>:</p>
<ul>
<li><strong>功能</strong>: 此区域会清晰地展示您当前使用的&quot;看海量化交易系统&quot;的<strong>版本号</strong>、<strong>构建日期</strong>和<strong>更新通道</strong>（如<code>stable</code>稳定版）。当您需要反馈问题或寻求帮助时，提供这些版本信息将非常有帮助。</li>
</ul>
</li>
<li>
<p><strong>反馈问题 (Feedback)</strong>:</p>
<ul>
<li><strong>功能</strong>: 点击此按钮，会直接跳转到作者的官方在线反馈页面。如果您在使用中遇到任何Bug、有功能建议或想进行交流，欢迎通过此渠道联系。</li>
</ul>
</li>
</ul>
<hr>
<p>完成所有设置后，请务必点击右下角的 <strong>保存设置</strong> 按钮，您的所有更改才能生效。</p>

</body>
</html>
