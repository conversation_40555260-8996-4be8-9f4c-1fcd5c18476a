# coding: utf-8
from typing import Dict, List
import numpy as np
from xtquant import xtdata
import datetime
import logging

# 全局变量
position = {}  # 格式: {股票代码: {'price': 入场价格, 'volume': 持仓数量}}
price_cache = {}  # 缓存每只股票的近期价格数据
params = {}  # 策略参数
risk_params = {}  # 风控参数
trade_time_ranges = []  # 交易时间范围

def init():
    """策略初始化"""
    global position, params, risk_params, trade_time_ranges, price_cache
    
    # 初始化持仓记录
    position = {}
    
    # 初始化价格缓存
    price_cache = {}
    
    # 初始化策略参数
    params = {
        # MACD参数
        "macd_fast": 12,       # 快线周期
        "macd_slow": 26,       # 慢线周期
        "macd_signal": 9,      # 信号线周期
        
        # 交易参数
        "volume_per_trade": 100,  # 每次交易数量（股）
        "data_length": 50      # 保存的价格数据点数量
    }
    
    # 初始化风控参数
    risk_params = {
        "stop_loss": 0.02,     # 止损比例（2%）
        "take_profit": 0.05,   # 止盈比例（5%）
        "max_positions": 5     # 最大持仓数量
    }
    
    # 初始化交易时间范围
    trade_time_ranges = [
        {"start": "09:30:00", "end": "11:30:00"},
        {"start": "13:00:00", "end": "14:57:00"}  # 收盘前3分钟停止开仓
    ]
    
    logging.info("简化版MACD策略初始化完成")
    print("简化版MACD策略初始化完成")

def khPreMarket(data: Dict) -> List[Dict]:
    """盘前回调函数"""
    # 清空价格缓存，每天重新收集
    global price_cache
    price_cache = {}
    
    # 获取当前日期信息
    current_time = data.get("__current_time__", {})
    current_date_str = current_time.get("date", "")
    
    print(f"盘前回调 - 日期: {current_date_str}")
    
    return []

def khPostMarket(data: Dict) -> List[Dict]:
    """盘后回调函数 - 平掉所有持仓"""
    signals = []
    
    # 获取当前时间信息
    current_time = data.get("__current_time__", {})
    current_time_str = current_time.get("time", "")
    
    # 平掉所有持仓
    for code, pos_info in list(position.items()):
        # 获取最新价格
        close_price = 0
        if code in data:
            close_price = data[code].get("close", 0)
        
        # 如果没有价格，使用持仓价格
        if close_price <= 0 and "price" in pos_info:
            close_price = pos_info["price"]
        
        if close_price > 0:
            signal = {
                "code": code,
                "action": "sell",
                "price": close_price,
                "volume": pos_info["volume"],
                "reason": "盘后平仓",
                "order_time": current_time_str
            }
            signals.append(signal)
            print(f"盘后平仓 - {code}, 价格: {close_price}")
    
    # 清空持仓
    position.clear()
    
    return signals

def is_in_trading_time(time_str: str) -> bool:
    """判断当前时间是否在交易时间范围内"""
    if not time_str:
        return False
        
    for time_range in trade_time_ranges:
        if time_range["start"] <= time_str <= time_range["end"]:
            return True
            
    return False

def update_price_cache(code: str, price: float):
    """更新价格缓存"""
    global price_cache
    
    if code not in price_cache:
        price_cache[code] = []
    
    price_cache[code].append(price)
    
    # 限制缓存大小
    if len(price_cache[code]) > params["data_length"]:
        price_cache[code] = price_cache[code][-params["data_length"]:]

def calculate_macd(prices: np.ndarray) -> dict:
    """计算MACD指标
    
    Args:
        prices: 价格数组
        
    Returns:
        dict: MACD相关指标，包含'macd', 'signal', 'histogram'
    """
    fast = params["macd_fast"]
    slow = params["macd_slow"]
    signal_period = params["macd_signal"]
    
    if len(prices) < slow + signal_period:
        return {'macd': 0, 'signal': 0, 'histogram': 0}
    
    # 计算EMA
    fast_ema = np.zeros_like(prices)
    slow_ema = np.zeros_like(prices)
    
    # 初始值使用SMA
    fast_ema[fast-1] = np.mean(prices[:fast])
    slow_ema[slow-1] = np.mean(prices[:slow])
    
    # 计算EMA其余值
    alpha_fast = 2.0 / (fast + 1)
    alpha_slow = 2.0 / (slow + 1)
    
    for i in range(fast, len(prices)):
        fast_ema[i] = prices[i] * alpha_fast + fast_ema[i-1] * (1 - alpha_fast)
        
    for i in range(slow, len(prices)):
        slow_ema[i] = prices[i] * alpha_slow + slow_ema[i-1] * (1 - alpha_slow)
    
    # 计算MACD线(DIF)：快EMA - 慢EMA
    macd_line = fast_ema - slow_ema
    
    # 计算信号线(DEA)：9日DIF的EMA
    signal_line = np.zeros_like(macd_line)
    alpha_signal = 2.0 / (signal_period + 1)
    
    # 初始信号线使用SMA
    if len(macd_line) >= signal_period:
        signal_line[signal_period-1] = np.mean(macd_line[:signal_period])
        
        # 计算信号线其余值
        for i in range(signal_period, len(macd_line)):
            signal_line[i] = macd_line[i] * alpha_signal + signal_line[i-1] * (1 - alpha_signal)
    
    # 计算柱状图(MACD Histogram)：DIF - DEA
    histogram = macd_line - signal_line
    
    # 返回最新值
    return {
        'macd': macd_line[-1],         # DIF值
        'signal': signal_line[-1],      # DEA值
        'histogram': histogram[-1]      # 柱状图(DIF-DEA)
    }

def khHandlebar(data: Dict) -> List[Dict]:
    """策略主逻辑"""
    signals = []
    
    # 获取当前时间信息
    current_time = data.get("__current_time__", {})
    current_time_str = current_time.get("time", "")
    
    # 检查是否在交易时间内
    if current_time_str and not is_in_trading_time(current_time_str):
        return []
    
    # 遍历所有股票数据
    for code, tick_data in data.items():
        # 跳过特殊键
        if code == "__current_time__":
            continue
        
        # 获取当前价格
        close = tick_data.get("close", 0)
        if close <= 0:
            continue
            
        # 更新价格缓存
        update_price_cache(code, close)
        
        # 如果数据不足，继续下一个股票
        min_data_required = max(params["macd_slow"], params["macd_signal"]) + 10
        if code not in price_cache or len(price_cache[code]) < min_data_required:
            continue
            
        # 检查是否持有该股票
        is_holding = code in position
        
        # 如果持仓中，检查止盈止损
        if is_holding:
            entry_price = position[code]["price"]
            price_change = (close - entry_price) / entry_price
            
            # 止损
            if price_change <= -risk_params["stop_loss"]:
                signal = {
                    "code": code,
                    "action": "sell",
                    "price": close,
                    "volume": position[code]["volume"],
                    "reason": f"止损: 亏损 {abs(price_change)*100:.2f}%",
                    "order_time": current_time_str
                }
                signals.append(signal)
                del position[code]
                continue
                
            # 止盈
            if price_change >= risk_params["take_profit"]:
                signal = {
                    "code": code,
                    "action": "sell",
                    "price": close,
                    "volume": position[code]["volume"],
                    "reason": f"止盈: 盈利 {price_change*100:.2f}%",
                    "order_time": current_time_str
                }
                signals.append(signal)
                del position[code]
                continue
        
        # 转换价格列表为numpy数组以便计算
        prices_array = np.array(price_cache[code])
        
        # 计算当前MACD指标
        current_macd = calculate_macd(prices_array)
        
        # 计算前一周期的MACD指标，用于判断交叉
        if len(prices_array) > 1:
            previous_macd = calculate_macd(prices_array[:-1])
            
            # MACD金叉：DIF上穿DEA(信号线) - 买入信号
            if (current_macd['macd'] > current_macd['signal'] and
                previous_macd['macd'] <= previous_macd['signal'] and
                not is_holding and len(position) < risk_params["max_positions"]):
                
                # 生成买入信号
                signal = {
                    "code": code,
                    "action": "buy",
                    "price": close,
                    "volume": params["volume_per_trade"],
                    "reason": f"MACD金叉: DIF {current_macd['macd']:.4f} > DEA {current_macd['signal']:.4f}",
                    "order_time": current_time_str
                }
                signals.append(signal)
                
                # 更新持仓
                position[code] = {"price": close, "volume": params["volume_per_trade"]}
                print(f"买入信号 - {code}, 价格: {close}, DIF: {current_macd['macd']:.4f}, DEA: {current_macd['signal']:.4f}")
                
            # MACD死叉：DIF下穿DEA(信号线) - 卖出信号
            elif (current_macd['macd'] < current_macd['signal'] and
                  previous_macd['macd'] >= previous_macd['signal'] and
                  is_holding):
                
                # 生成卖出信号
                signal = {
                    "code": code,
                    "action": "sell",
                    "price": close,
                    "volume": position[code]["volume"],
                    "reason": f"MACD死叉: DIF {current_macd['macd']:.4f} < DEA {current_macd['signal']:.4f}",
                    "order_time": current_time_str
                }
                signals.append(signal)
                
                # 更新持仓
                del position[code]
                print(f"卖出信号 - {code}, 价格: {close}, DIF: {current_macd['macd']:.4f}, DEA: {current_macd['signal']:.4f}")
    
    return signals