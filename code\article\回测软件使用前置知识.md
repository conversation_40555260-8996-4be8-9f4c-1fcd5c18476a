# 使用“看海量化”写策略？先看看你需要掌握哪些“内功”！

## 写在前面：给想用“看海”创造策略的你

“看海量化交易系统”是个好帮手，能帮你验证脑海中的交易想法是不是真的靠谱。但如果你不只想跑跑别人写好的策略，还想自己动手，把交易思路变成代码跑起来，那提前“修炼”一些基础知识就非常有必要了。这能让你用起“看海”来更得心应手，少走弯路。

别担心，不是让你成为全才，但了解下面这些，绝对能让你的量化之路更顺畅：

## (一) 编程基础：量化的“画笔”

现在搞量化，Python 语言是主流选择，“看海”也是基于此。所以，掌握 Python 是基本功。写策略就是用代码把你买卖的想法精确地描述出来，让电脑去执行，所以编程能力直接决定了你能否实现你的策略。

你需要掌握啥？

(1) **基础语法：** 这就像学一门外语的单词和语法。你需要知道怎么定义变量来存储价格、数量等信息；了解不同的数据类型，比如整数、小数、字符串；掌握 `if...else...` 这样的条件判断，比如“如果收盘价大于20日均线就买入”；学会用 `for` 或 `while` 循环来重复处理数据，比如计算过去 N 天的平均价；理解怎么把一段常用代码打包成函数，方便重复调用，让代码更整洁。
(2) **数据结构：** 这是组织和存储数据的方式。列表（List）可以存放一系列按顺序排列的数据，比如一只股票每天的收盘价。字典（Dictionary）可以存放带标签的数据，比如用股票代码作为标签，存储它的名称、价格等信息。NumPy 库里的数组（Array）是专门为数值计算设计的，处理大量的金融数据（比如一个包含开盘、收盘、最高、最低价的矩阵）时效率非常高。
(3) **面向对象（OOP）：** 这个概念稍微进阶一点。简单理解就是把数据和处理这些数据的方法打包在一起，形成一个“对象”。很多量化框架（包括一些复杂的策略写法）会用到类（Class）和对象（Object）。比如，可能会有一个 `Strategy` 类，你可以基于它创建自己的策略对象，并填充买卖逻辑。初期不一定需要自己从头写复杂的类，但能看懂别人代码里类和对象的用法会很有帮助。
(4) **核心库（重中之重）：** 这两个库是 Python 数据分析和量化交易的基石，必须熟练掌握。
    *   **Pandas:** 它是处理表格数据（尤其是时间序列数据，比如股票日线）的神器。你需要学会怎么用 Pandas 读取数据（比如从 CSV 文件或者“看海”提供的接口），怎么筛选你需要的数据行或列（比如选出特定时间段或特定股票的数据），怎么添加新的计算列（比如计算每日收益率、移动平均线），怎么合并不同的数据表等等。**绝大部分策略的数据预处理和计算都离不开 Pandas。**
    *   **NumPy:** 它是 Python 科学计算的基础。Pandas 很多功能底层就是基于 NumPy 的。它提供了高效的多维数组对象以及大量的数学函数。当你需要进行复杂的矩阵运算，或者对大量数值进行快速计算时，NumPy 会非常有用。

怎么学？

(1) **找入门教程：** 对于零基础的朋友，找一个清晰易懂的入门教程至关重要。廖雪峰的教程以通俗著称，菜鸟教程则比较简练，B站上有很多免费的视频课程，互动性更强。关键是**动手跟着敲代码**，不要只看。
(2) **看书：** 《Python 编程：从入门到实践》非常适合新手，有很多小项目练手。《流畅的 Python》则适合有一定基础后深入理解 Python 的特性。选择一本，系统地学习一遍。
(3) **专门攻克 Pandas/NumPy：** 这两个库功能强大，值得投入时间。Pandas 官方文档的 “10 Minutes to pandas” 是快速入门的好材料。之后可以找专门的书籍（如《利用 Python 进行数据分析》）或者在线教程深入学习。**最好的学习方式是带着问题去学**，比如“我想计算某只股票的 20 日均线”，然后去查找 Pandas 如何实现。
(4) **边学边用：** 理论结合实践是最好的方式。打开“看海”，尝试调用它提供的数据接口获取数据，用 Pandas 对数据进行简单的处理和计算，比如打印出数据的前几行、计算收盘价的平均值等。把学到的语法和库函数用到实际场景中，印象会更深刻。

## (二) 金融基础知识：量化的“战场规则”

写策略不能闭门造车，得懂点市场的基本规则。不了解你要交易的市场和品种，策略就如同空中楼阁。

你需要了解啥？

(1) **市场类型：** 不同市场的交易规则差异巨大，直接影响策略的设计。比如 A 股股票是 T+1 交易（当天买入，下一个交易日才能卖出），还有 10% 的涨跌停限制，这些都会限制你的策略（比如当天发现信号不好，无法立即卖出止损）。期货市场是 T+0 交易，有保证金制度和合约到期日，策略需要考虑资金利用率和合约换月的问题。了解这些规则是避免策略无法执行或产生意外亏损的前提。
(2) **基本面 vs 技术面：** 基本面分析关注公司的内在价值（看财报、行业地位等），技术分析关注价格和交易量的历史图表模式（看 K 线、指标等）。量化策略很多偏向技术面，利用价格和成交量的规律。但了解基本面能让你理解某些市场大幅波动的原因（比如财报发布、宏观政策变化），有助于把握大方向或避开某些风险。
(3) **常用技术指标：** 这是技术分析派策略的重要工具。比如移动平均线 (MA) 可以帮你判断趋势方向；MACD 可以捕捉趋势的启动和转折；RSI 可以衡量市场的超买超卖状态；布林带可以展示价格的波动范围。**重点不是死记硬背“金叉买入，死叉卖出”的口诀，而是理解每个指标的计算原理，它试图衡量市场的哪个方面，以及它的局限性是什么。** 这样你才能在不同市场情况下灵活运用或组合指标。
(4) **交易成本：** 这是策略盈利的“隐形杀手”。每次买卖股票，都要支付佣金（给券商的手续费）、印花税（卖出时国家收的税）。另外，由于市场报价的快速变动和订单成交的机制，你下单的价格和你最终成交的价格之间可能存在差异，这就是滑点。在回测和实盘中，必须充分考虑这些成本，否则一个看起来很赚钱的策略，可能扣除成本后就亏损了。

怎么学？

(1) **看财经网站/App：** 这是获取市场信息、了解基本术语最便捷的方式。每天花点时间浏览东方财富、同花顺、雪球等平台，看看行情报价、新闻资讯、公司公告，潜移默化地熟悉市场。
(2) **读入门书籍：** 《股市趋势技术分析》和《日本蜡烛图技术》是技术分析的经典著作，可以帮你理解图表分析的逻辑。《金融市场基础知识》（证券从业资格考试教材之一）则可以系统地介绍各种金融工具和市场规则。
(3) **利用网络资源：** Investopedia 是一个非常好的英文金融百科网站，解释很清晰。国内很多券商网站或 App 都有投资者教育栏目，提供基础知识科普。知乎、微信公众号上也有大量个人和机构分享量化交易和金融知识的文章。
(4) **看指标公式：** 大部分行情软件（包括“看海”可能也内置了）都提供了常用技术指标的源码或详细计算公式。尝试自己手动用数据算一遍指标，或者用 Pandas 来实现指标计算，可以加深对指标的理解。

## (三) 量化交易基础：量化的“战术思路”

把编程和金融知识结合起来，形成量化交易的思维方式。知道常见的策略类型和风险控制方法，才能构建出有效的交易系统。

你需要了解啥？

(1) **量化交易是啥：** 核心是用程序化的方式来执行交易决策。这意味着你需要把模糊的交易想法（比如“感觉要涨了就买”）转化为精确的、可执行的规则（比如“当 5 日均线上穿 20 日均线，并且成交量放大到过去 10 日平均成交量的 1.5 倍以上时，买入”）。它的优点是克服人性的贪婪恐惧，严格执行纪律，并且可以回测历史数据进行验证。
(2) **常见策略类型：** 了解不同的策略范式，可以打开你的思路。
    *   **趋势跟踪：** 核心思想是“让利润奔跑，截断亏损”。当市场形成明显趋势时跟随趋势方向操作。比如著名的海龟交易法则就是趋势跟踪策略。
    *   **均值回归：** 认为价格波动总会回归到某个均衡水平。当价格过度偏离均值时，反向操作。比如统计套利就是利用相关资产价格偏离正常关系时进行套利，期望价格回归。
    *   **套利：** 利用市场暂时的定价错误或不同市场间的价差来获取低风险收益。比如期现套利、跨市场套利等。
    *   **因子策略：** 这是目前机构投资者常用的方法。通过研究发现能够持续解释或预测股票收益的因素（比如小市值效应、价值效应、动量效应），然后根据这些因子构建投资组合。
(3) **风险管理：** **这是量化交易的重中之重，甚至比策略本身更重要！** 一个没有风控的策略就像一辆没有刹车的跑车。你需要学习如何设置止损（比如亏损达到 5% 就无条件离场）来限制单笔交易的最大损失；如何进行仓位管理（比如根据市场波动性或策略信号强度来决定投入多少资金），避免在不利情况下损失过大。**活下来，是持续盈利的前提。**
(4) **数据处理：** “数据是量化交易的燃料”。你需要知道从哪里获取可靠的历史数据（“看海”平台会提供一部分，你也可以从第三方数据商购买，或者从网络爬取）。获取数据后，还需要进行清洗，比如处理数据缺失（填补或删除？）、异常值（价格突然变成 0 或极大值怎么办？），保证数据的准确性和一致性。回测用的数据质量直接影响结果的可靠性。

怎么学？

(1) **看专业书籍：** 《打开量化投资的黑箱》介绍了量化交易的整体框架和常见策略。《海龟交易法则》讲述了一个经典的趋势跟踪系统的故事和细节。《量化交易——如何建立自己的算法交易事业》则更侧重实践和系统构建。
(2) **关注量化社区/论坛：** 国内的 JoinQuant（聚宽）、RiceQuant（米筐）、BigQuant、掘金等社区聚集了大量量化爱好者和从业者。多看看别人分享的策略源码、研究报告和讨论，可以学到很多实战经验和技巧。
(3) **阅读研报：** 关注一些知名券商研究所发布的金融工程或量化策略相关的研究报告。这些报告通常会介绍一些前沿的策略思路和模型，虽然不一定能直接用，但可以开拓视野。
(4) **从简单策略入手：** 不要一开始就追求高大上。先尝试在“看海”平台上，用你学到的编程和金融知识，实现一个最简单的策略，比如双均线交叉策略（5日线上穿20日线买入，下穿卖出），跑通回测流程，理解其中的细节。

## (四) 回测理论与实践：量化的“演兵场”

回测就是策略的“模拟考试”，考得好不好，怎么看成绩单，得有讲究。只有正确理解回测，才能客观评估策略的潜力和风险。

你需要了解啥？

(1) **回测怎么跑的：** 理解回测引擎的基本工作原理很重要。它通常是按时间顺序读取历史数据（比如一根 K 线一根 K 线地读），在每个时间点，根据你的策略逻辑判断是否触发交易信号，如果触发，就模拟下单，并根据当时的价格（或下一根 K 线的开盘价等）计算成交，更新持仓和资金，记录交易日志。了解这个过程有助于你理解回测报告中的细节。
(2) **关键评价指标：** 回测报告会给出一堆数字，你需要理解其中最重要的几个指标的含义：
    *   **年化收益率：** 把回测期间的总收益折算成平均每年的收益水平，方便和别的策略或市场基准比较。
    *   **最大回撤：** 这是衡量策略风险最重要的指标之一。它表示在回测期间，你的账户净值从最高点到后续最低点，可能出现的最大亏损幅度。这个数字告诉你策略可能面临的最极端情况。
    *   **夏普比率：** 衡量策略承担每单位风险（通常用净值的波动率表示）能带来多少超过无风险利率（比如银行存款利率）的收益。夏普比率越高，说明策略的风险调整后收益越好。
(3) **过拟合（“陷阱”）：** 这是新手最容易犯的错误。过拟合是指你的策略过多地挖掘了历史数据的巧合或噪音，导致在回测期间表现非常好，但拿到新的、未知的数据（实盘）上就失效了。比如，你不断调整参数，让策略完美避开了历史上某几次大跌，但这可能是运气，未来不一定奏效。**要警惕过于漂亮的回测曲线。**
(4) **成本和滑点设置：** 在回测设置里，必须模拟真实的交易成本。佣金和印花税通常有固定的费率，比较好设置。滑点则更难估计，它跟你交易的品种流动性、下单方式、市场波动性都有关。可以设置一个固定滑点（比如成交价比信号价差千分之一），或者更复杂的滑点模型。**忽略或低估成本和滑点，会让回测结果严重失真。**
(5) **数据坑：** 回测的公平性取决于数据的质量和使用方式。常见的坑包括：
    *   **幸存者偏差：** 回测只用了那些到今天还“活下来”的股票数据，忽略了那些中途退市或被收购的公司，这会高估整体市场的表现，从而可能高估策略收益。
    *   **未来函数：** 这是指在回测的某个时间点，不小心使用了该时间点之后才能知道的信息来做决策。比如在判断今天收盘是否买入时，用了今天的最高价或最低价（这些是收盘后才能完全确定的）。这是绝对要避免的逻辑错误。

怎么学？

(1) **阅读平台文档：** 再次强调，仔细阅读“看海”关于回测模块的官方文档。了解它支持哪些设置项（回测时间、初始资金、手续费滑点模型、复权方式等），各个参数的具体含义和影响。
(2) **学习回测文章/教程：** 在网上搜索关于“量化回测”、“如何避免过拟合”、“回测指标解读”等关键词，阅读相关的文章和教程，学习业界的最佳实践。
(3) **动手做实验：** 这是最好的学习方式。用同一个策略，尝试不同的回测参数设置：改变手续费率、滑点大小、回测时间段（比如用牛市、熊市、震荡市的数据分别回测），观察回测结果（收益、回撤、夏普比率）的变化。这能让你直观感受这些因素对策略表现的影响。
(4) **警惕完美曲线：** 当你看到一个回测净值曲线像一条直线一样几乎没有回撤地上涨时，不要激动，先冷静下来检查：是不是忘了设置手续费和滑点？是不是用了未来函数？是不是参数过度优化了？

## (五) 统计学与数学基础：量化的“度量衡”

别被“数学”吓到，量化交易不一定需要高深的数学，但懂点基础统计能帮你更好地分析策略和数据。很多策略思想本身就源于统计规律。

你需要了解啥？

(1) **基础概率统计：** 了解一些基本的统计概念和指标，能帮助你更科学地分析数据和评估策略。比如：
    *   **描述性统计：** 均值（平均水平）、中位数（中间位置的值）、标准差（数据离散程度或波动性）。
    *   **概率分布：** 了解正态分布等常见分布，有助于理解收益率的分布特征。
    *   **相关性：** 衡量两个变量（比如股票 A 的收益率和股票 B 的收益率）一起变动的程度。相关性分析是构建投资组合和对冲风险的基础。
    *   **假设检验：** 用来判断你观察到的现象（比如策略的超额收益）是真实有效的，还是仅仅是偶然发生的。
(2) **时间序列概念：** 金融数据（如股价、交易量）是典型的时间序列数据，它们在时间上有先后顺序，并且可能存在自相关性（今天的价格可能和昨天的价格有关）。了解时间序列分析的基本概念（如平稳性、自相关、白噪声等）有助于你理解和处理这类数据。
(3) **（加分项）线性代数：** 如果你想深入研究多因子模型、投资组合优化等领域，线性代数（主要是矩阵运算）会是必不可少的工具。但对于入门来说，不是必须的。

怎么学？

(1) **看公开课/在线课程：** 可汗学院、Coursera、edX、网易公开课等平台都有很多优秀的统计学入门课程，通常讲解得比较生动形象。
(2) **找实用教材：** 《统计学（人大版）》是国内经典教材。《深入浅出统计学》则用更轻松有趣的方式讲解。选择一本适合自己的，系统学习一下基础概念。
(3) **结合 Pandas 学习：** 这是最实用的方法。Pandas 库内置了大量统计函数，比如计算均值 `.mean()`、标准差 `.std()`、相关系数 `.corr()`、分位数 `.quantile()` 等等。你可以直接在处理金融数据时调用这些函数，结合实际数据来理解统计概念的含义和应用。

## 最后的叮嘱：怎么“修炼”更高效？

(1) **打好地基：** 万丈高楼平地起。建议先把 Python 基础语法和 Pandas 库的基本操作过一遍，同时了解一些基本的金融术语和市场规则。不需要追求一步到位成为专家，但要达到能看懂、能简单使用的程度。
(2) **动手！动手！动手！** 重要的事情说三遍。量化交易是非常实践导向的。光看书看教程是远远不够的。打开“看海”平台，把教程里的例子敲一遍，尝试修改参数，运行回测，观察结果。遇到报错，尝试自己解决或者搜索答案。**在实践中遇到的问题和解决问题的过程，是进步最快的方式。**
(3) **啃文档 + 逛社区：** “看海”的官方文档通常是最准确、最全面的信息来源，遇到平台相关的问题优先查文档。同时，积极参与量化社区（如聚宽、米筐等）的讨论，看看别人分享的策略、遇到的问题和解决方法，可以少走很多弯路，也能获得启发。
(4) **从“小目标”开始：** 罗马不是一天建成的。不要期望一开始就写出一个年化收益 100%、回撤极低的“完美”策略。先从实现一个简单的、逻辑清晰的策略想法开始，比如“收盘价突破过去 20 天最高价就买入”，把它完整地在“看海”上实现并跑通回测。然后在这个基础上，逐步增加复杂度，比如加入止损、仓位管理、过滤条件等。
(5) **保持耐心和好奇心：** 量化交易是一个需要不断学习、不断试错、不断迭代的过程。你可能会遇到策略回测效果不佳、实盘表现不如预期等各种挫折。保持耐心，持续学习，对市场和策略保持好奇心，享受这个探索和创造的过程，这比追求短期暴利更重要。

掌握这些“内功”，你就能更好地驾驭“看海量化交易系统”，把你的交易智慧转化为实实在在的策略代码。祝你探索顺利，在量化的世界里找到属于你的方法！ 