## 1.1 关于我

在全身心投入这款量化交易系统的研发之前，我混迹于知乎和公众号，专注于信号处理与深度学习算法的分享与探讨。后来在研究算法的过程中，产生了新的想法，即将这些年在信号处理和深度学习领域积累的思维与方法，跨界应用于充满挑战与机遇的股票市场和量化交易中。

当前教程是看海，也就是我搭建的唯一官方教程，后续教程都是在此处最先更新。

为了方便和大家交流，我在以下平台都开设了账号，欢迎关注：

* **微信公众号**：[看海的城堡](https://mp.weixin.qq.com/s/l_06l72N8WFnbM5_4Ns4Dw)
* **知乎**：[Mr.看海](https://www.zhihu.com/people/feng-zhu-38)
* **抖音**：Mr.看海（抖音号：31281929369）
* **快手**：Mr.看海（快手号：4775269996）
* **B站 (哔哩哔哩)**：[Mr看海](https://space.bilibili.com/3546667687086777)
* **头条**：[Mr.看海](https://www.toutiao.com/c/user/token/MS4wLjABAAAAzyc56KI8pUK0LEasGJ88GQ7YOog-eiSLihjiYFUUUh32URE2tUnJpiPITu4uhJCQ/?)

一直以来，我都致力于为大家提供一款免费开源的量化交易系统。可以说，这是用爱发电的产物。目前，系统开发和维护的微薄收入主要来源于通过我推荐渠道开户的朋友们所带来的些许返佣，以及热心用户的慷慨打赏。如果您觉得这款系统对您有所帮助，不妨请作者喝杯咖啡，您的这份支持，是我持续更新与完善的最大动力！

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250606235710-300x300.png" alt="微信打赏" width="300"/>
</p>

---

## 1.2 我为什么要做这样一款量化交易系统？

市面上的量化工具不少，为何还要"重复造轮子"？核心在于对现有工具的些许不满，以及一些亟待实现的功能与理念。

回测系统是量化交易的基石，它允许策略在历史数据中"演练"，评估可行性。然而，不少现有工具，如QMT，虽具备回测功能，但在策略开发的灵活性上（如Python第三方库的调用）存在限制，这束缚了AI等前沿算法的应用。KHQuant的初衷便是**打破这些枷锁，赋予开发者最大的策略实现自由度**，让Python的强大生态充分助力创新。

**关键考量：**

* **策略安全与本地运行**：策略是核心资产。KHQuant基于MiniQMT开发，确保策略代码和数据在本地运行，保障安全与隐私，同时在处理大规模数据或复杂模型时更具性能与成本优势。
* **打造趁手的"兵器"**：工具应保证专业性的同时，追求高效易用。作为一名长期从事信号处理与机器学习算法研究的开发者，深谙此道。KHQuant力求成为一款称手好用的工具，让用户能更专注于策略研究本身。
* **填补MiniQMT生态空白**：目前市场缺乏针对MiniQMT完善易用的回测与模拟平台。KHQuant愿做"第一个吃螃蟹的人"，为MiniQMT用户和量化爱好者提供新选择。

**KHQuant的核心设计原则：**

1. **模块低耦合——提升灵活性与可维护性**：
   借鉴"乐高积木"的理念，KHQuant追求UI、数据、策略与核心框架的分离。如此，各模块可独立升级、替换，避免"牵一发而动全身"，保证系统未来的迭代与扩展能力。
2. **策略高内聚——聚焦策略，提升效率**：
   使用者应专注于策略逻辑。KHQuant致力于提供稳定的底层支撑和清晰的接口规范，目标是实现策略文件在回测、模拟、实盘间无缝切换，并共享统一的图形化配置界面，最大程度减轻用户负担。未来，甚至期望通过标准化的接口，结合大语言模型辅助策略生成。

> 💡 **一言以蔽之**：KHQuant并非追求"大而全"，而是力求在**图形化、本地化、灵活性和实用性**这些核心痛点上做到"精而深"，为个人投资者提供一款免费、强大且称手的量化研究利器。

---

## 1.3 看海量化系统：特点与比较

为了让大家更清晰地理解KHQuant的定位，这里通过一个简明扼要的表格来对比其与市面上其他主流类型量化工具的特点：


| 特性维度         | 看海量化交易系统 (KHQuant)                               | 国内券商平台 (如：QMT自带, 通达信) | 开源回测框架 (如：Backtrader, vn.py)          | 在线量化平台 (如：聚宽, BigQuant) |
| :--------------- | :--------------------------------------------------------- | :--------------------------------- | :---------------------------------------------- | :-------------------------------- |
| **系统设计侧重** | GUI友好，与MiniQMT深度集成，实现数据接口**开箱即用**       | 账户直连，行情交易便捷，低成本     | 高度灵活定制，免费开源，社区支持                | 云端运行，提供数据，学习资源丰富  |
| **系统当前局限** | 生态初期，功能待完善，依赖MiniQMT，个人维护                | 策略自由度低，回测功能较弱         | 上手门槛高，需自行寻找、配置和维护数据/交易接口 | 核心代码/数据不可控，高级功能收费 |
| **目标用户画像** | 注重易用性的个人量化爱好者，希望数据策略本地化             | 普通交易者，编程能力要求不高       | 编程能力强的开发者，需深度定制                  | 量化初学者，偏好云端服务          |
| **策略编程**     | Python                                                     | 平台特定语言或脚本                 | Python                                          | Python为主                        |
| **数据获取**     | **内置MiniQMT接口，开箱即用**                              | 提供多品种行情数据                 | 需用户自行对接和维护数据源                      | 平台提供常用数据                  |
| **界面形态**     | PyQt5构建的桌面GUI                                         | 标准的行情交易软件界面             | 部分框架提供GUI（如vn.py），其他需自行绘图      | Web界面，图表友好                 |
| **使用成本**     | 免费                                                       | 开户后免费使用                     | 免费                                            | 免费入门，增值服务收费            |

> 简单来说，KHQuant致力于为A股个人投资者，提供一个在**图形化、本地化、简单实用**方面表现出色的量化工具。其与MiniQMT的深度整合，让用户免去了寻找和配置数据源的繁琐工作，可以更专注于策略开发本身。它或许并非完美无瑕，但会持续打磨与进化，力求帮助每一位使用者更高效地进行策略研究与交易实践。

---

选择"看海量化交易系统"，将能深入体验到以下几点核心优势所带来的便利与价值：

**🎨 完全开源免费，拥抱社区共建生态**：
KHQuant 不仅仅是一款工具，更是一个开放的平台。系统源代码完全公开透明，允许自由探索其实现细节，根据自身需求进行个性化修改，甚至参与到项目的共建中。这种开放性，确保了对工具的完全掌控，而不必担心任何"黑箱"操作或潜在的隐性成本。这完全是"用爱发电"的产物，旨在为国内量化爱好者提供一个纯粹、强大的免费选项。

**🛡️ 数据与策略本地化部署，安全与隐私尽在掌握**：
在量化交易领域，数据和策略无疑是核心资产。KHQuant 坚持将所有策略代码、历史数据、回测结果以及交易记录等敏感信息完全存储于本地计算机。这意味着使用者对其知识产权和交易活动拥有绝对的控制权，无需担心因依赖第三方云平台而可能带来的数据泄露、策略被窥探或服务中断的风险。智慧成果得以自主守护。

**⚙️ 可视化便捷操作与Python代码灵活驱动，双引擎满足多层次需求**：
系统精心设计了用户友好的图形化界面（GUI），使得许多常规操作，如参数配置、回测设置、股票池管理等，都可以通过简单的鼠标点击完成，极大降低了上手门槛，即使是编程经验较少的用户也能快速入门。同时，对于追求极致灵活性和复杂逻辑实现的专业开发者，KHQuant 提供了纯粹的Python策略编写环境，允许充分利用Python的强大表达能力和丰富的第三方库，构建高度定制化的交易系统。

**🧠 拥抱AI浪潮，为大模型赋能量化策略，拓展智能边界**：
人工智能飞速发展的时代，大语言模型（LLM）的能力令人瞩目。KHQuant 在设计之初便充分考虑了与AI技术的结合潜力。其清晰的模块划分、标准化的策略接口以及开放的Python环境，都为大模型在量化策略中的应用提供了便利。可以尝试使用大模型辅助进行策略逻辑的构思、代码片段的生成，甚至在未来，期望能实现更深度的融合，让AI成为策略研究与开发过程中的得力助手。

**🔗 深度整合MiniQMT，共享成熟稳定的交易执行**：
KHQuant 的行情获取深度依赖于券商的MiniQMT系统。这意味着可以直接受益于券商提供的成熟、稳定、合规的行情服务，从而能够更专注于策略本身的研发与优化。

**🎯 专注A股优化，更懂本土化交易者的实战需求**：
与其他通用型或主要面向海外市场的量化平台不同，KHQuant 在设计和功能实现上，充分考虑了A股市场的独特性。例如，针对A股的交易规则（如T+1制度、涨跌停限制）、常用的技术指标偏好、数据特点等都进行了细致的适配和优化，力求为国内投资者提供一个更接地气、更符合实战需求的量化工具。

**🚀 极致策略自由度，释放Python生态的无限潜能**：
许多量化平台会对可使用的Python第三方库施加诸多限制，这无疑束缚了策略的创新空间。KHQuant 则致力于打破这些"枷锁"，允许在策略中无拘无束地引入和使用Python生态中几乎所有的公开库。无论是用于高级数据分析的Pandas、NumPy、SciPy，还是用于机器学习的Scikit-learn、TensorFlow、PyTorch，亦或是其他专业领域的强大工具，只要认为对策略有益，都可以自由集成，从而将最前沿的技术和算法应用于量化实践中。

---

## 1.4 使用"看海量化交易平台"的背景知识清单

为了帮助不同需求的用户更好地使用"看海量化交易平台"，这里梳理了一份背景知识清单，分为入门、进阶和高级三个层次。您可以根据自己的目标和现有基础，按图索骥，逐步提升。

### 1.4.1 入门：编写开环策略实现回测

此阶段的目标是能够使用已经打包好的"看海量化平台"，编写并运行开环策略（即策略逻辑相对简单，不涉及复杂的模型训练和动态调优），并对策略进行历史回测，分析回测结果。


| 掌握程度 | 技能                               | 说明                                                                                      |
| :------- | :--------------------------------- | :---------------------------------------------------------------------------------------- |
| **必备** | Python编程基础（含Pandas/NumPy库） | 理解Python核心语法、控制流、函数，并掌握Pandas进行数据处理及NumPy进行数值计算的基本操作。 |
| **必备** | 基本的金融市场知识                 | 了解股票、K线、交易规则、常用技术指标（如均线、MACD、布林带等）的基本概念。               |
| **必备** | 理解回测报告中的关键指标           | 如收益率、最大回撤、夏普比率等。                                                          |
| **必备** | 代码编辑器/IDE的使用               | 熟练使用至少一种代码编辑工具（如VS Code, PyCharm等）进行策略脚本的编写与管理。            |

### 1.4.2 进阶：编写需模型训练的闭环策略实现回测

此阶段的目标是能够在入门基础上，进一步编写包含机器学习、深度学习等模型训练的闭环策略。这类策略通常需要根据市场反馈动态调整模型参数或交易逻辑。


| 掌握程度 | 技能                                         | 说明                                                                                    |
| :------- | :------------------------------------------- | :-------------------------------------------------------------------------------------- |
| **必备** | 扎实的Python编程能力                         | 包括面向对象编程（OOP）思想、模块化编程等。                                             |
| **必备** | Pandas/NumPy高级应用                         | 能够进行更复杂的数据转换、特征工程、性能优化等。                                        |
| **必备** | 机器学习/深度学习基础理论                    | 理解常见的监督学习、无监督学习算法原理，如线性回归、逻辑回归、决策树、SVM、神经网络等。 |
| 建议掌握 | TensorFlow/PyTorch等深度学习框架（至少一种） | 如果策略涉及深度学习模型，需要掌握至少一个主流框架的使用。                              |
| 建议掌握 | 特征工程方法                                 | 如何从原始数据中提取、构建对模型有效的特征。                                            |
| 建议掌握 | 模型评估与调优技巧                           | 了解过拟合、欠拟合，掌握交叉验证、网格搜索等模型调优方法。                              |

### 1.4.3 高级：使用开源代码，定制化修改平台

此阶段的目标是具备深入理解并修改"看海量化平台"源代码的能力，根据自身特殊需求进行二次开发和功能定制。


| 掌握程度 | 技能                                | 说明                                                                             |
| :------- | :---------------------------------- | :------------------------------------------------------------------------------- |
| **必备** | 精通Python高级编程                  | 深入理解Python的内部机制，如装饰器、生成器、元类、异步编程等。                   |
| **必备** | PyQt5 GUI编程框架                   | 深入理解PyQt5的事件循环、布局管理、信号与槽机制、自定义控件等。                  |
| **必备** | 深入理解`xtquant` 库 (MiniQMT接口) | 掌握MiniQMT的核心API调用，包括行情订阅、交易指令发送、账户信息查询等。           |
| **必备** | 软件架构设计能力                    | 能够理解和设计模块化、可扩展、可维护的软件系统，理解KHQuant的现有架构。          |
| **必备** | Git版本控制                         | 熟练使用Git进行代码版本管理与协作。                                              |
| **必备** | 量化交易系统核心组件的理解          | 深入理解事件驱动、行情处理、订单管理、风险控制、绩效计算等核心模块的原理与实现。 |
| 建议掌握 | Python多线程/异步编程               | 用于优化GUI响应、处理耗时操作等，提高平台性能和用户体验。                        |
| 建议掌握 | 事件驱动编程模型                    | 深入理解事件驱动架构，有助于更好地理解和修改平台的核心逻辑。                     |

> ✨ **小贴士**：对于绝大多数希望进行策略回测和研究的用户来说，达到"入门"级别并逐步熟悉平台功能，就已经能够满足大部分需求。"看海量化平台"也会持续推出更多策略示例和教程，帮助大家更好地理解和应用。

---

## 1.5 "看海量化交易系统"适合做什么？

"看海量化交易系统"以其灵活性和易用性，能够很好地支持多种类型的中低频量化交易策略的研发与实践。以下是一些典型的适用场景：

* ✅ **各类因子选股与轮动策略**：无论是基于经典的价值、成长、质量、动量等因子，还是自行构建的特色因子，系统都能方便地进行多因子模型的选股、打分、回测与组合轮动。
* ✅ **趋势跟踪与技术指标策略**：对于依赖均线系统、布林带、MACD、RSI等各类技术指标构建的趋势跟踪、突破或震荡策略，系统提供了良好的支持。
* ✅ **统计套利与均值回归策略**：包括但不限于配对交易、期现套利（基于MiniQMT支持的品种）、ETF套利以及其他利用市场短期失衡的均值回归型策略。
* ✅ **事件驱动型策略**：结合外部事件数据（如财报发布、重要行业新闻、政策变动等），构建在特定事件发生前后进行交易决策的策略。
* ✅ **机器学习与AI辅助策略（中低频）**：利用Scikit-learn、TensorFlow、PyTorch等库，训练机器学习或深度学习模型，对股价走势、市场状态等进行预测，并结合系统生成中低频交易信号。
* ✅ **投资组合管理与动态再平衡**：实现基于特定风险偏好或资产配置模型的投资组合构建，并根据市场变化或预设规则进行定期的动态调仓和再平衡。
* ✅ **自定义指数构建与增强**：根据特定的投资理念或行业偏好，自行编制指数并进行跟踪，或者在现有指数基础上进行Alpha增强。
* ✅ **量化知识学习与策略思想验证**：系统友好的界面和开放的特性，使其成为学习量化交易、快速验证策略思路的理想平台。
* ✅ **成熟交易逻辑的自动化执行**：将经过验证的、系统化的手动交易经验和规则，通过代码实现自动化执行，解放人力，提高效率。

> 总而言之，只要策略的执行频率和对延迟的要求不是极端严苛，KHQuant 都能提供一个强大而便捷的本地化解决方案。

---

## 1.6 "看海量化交易系统"不适合做什么？

> ⚠️ **请注意**：虽然"看海量化交易系统"力求强大与灵活，但基于其设计定位和核心依赖（MiniQMT），在以下一些方面可能并非最佳选择，了解这些局限性有助于用户做出更合理的预期和决策。

* ❌ **高频交易（HFT）与超低延迟策略**：
  * **数据层面**：MiniQMT提供的Tick数据通常是3秒快照，而非逐笔成交数据，这对于需要微秒级行情精度的典型高频策略来说，信息颗粒度不足。
  * **执行层面**：系统本身（Python语言特性、多层架构）以及通过MiniQMT的交易链路，都无法满足高频交易所要求的亚毫秒级执行延迟。
  * **技术栈**：专业的高频交易通常需要C++等高性能语言、FPGA硬件加速以及专用的低延迟交易接口和托管服务。
* ❌ **依赖极久远历史数据的细颗粒度回测**：
  * **MiniQMT数据限制**：券商版MiniQMT对历史数据的下载范围有限制。通常情况下，Tick数据可能只能获取最近一个月左右，1分钟和5分钟K线数据可能为最近一年左右，日线数据则相对完整。这意味着，如果策略需要回测数年前的分钟级甚至Tick级行情，系统可能无法直接提供足够的数据支撑。（有实力的可以开通研投版QMT，这样就有全部的数据了）
* ❌ **对多市场、多资产的复杂联动套利（超出MiniQMT范围）**：
  虽然可以通过Python的灵活性尝试对接其他数据源或接口，但KHQuant的核心优化和原生支持是围绕MiniQMT所能覆盖的A股市场（股票、ETF、部分期货期权等）。对于需要复杂跨市场（如全球市场）、跨资产类别（如外汇、加密货币）进行高精度、低延迟联动的套利策略，可能需要更专业的、针对性的平台。
* ❌ **非Windows操作系统的原生流畅运行**：
  由于MiniQMT客户端本身主要运行于Windows环境，KHQuant的主要开发和测试也是在Windows上进行的。虽然技术上用户可能尝试通过Wine等兼容层在Linux或MacOS上运行，但这并非官方支持的路径，可能会遇到稳定性问题或兼容性障碍。
