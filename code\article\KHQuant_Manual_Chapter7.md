# 第七章：中间运行驱动区：定义策略的心跳

中间面板是策略的"运行驱动区"，其核心任务是定义策略逻辑（即 `khHandlebar` 函数）被调用的频率和时机。可以说，这里决定了策略的"心跳"。本章将详细介绍三种不同的触发方式，并说明如何配置账户信息及盘前盘后任务。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/中间面板.png" alt="中间面板" width="50%" />
</p>

---

## 7.1 触发方式设置

触发方式定义了策略的执行频率。KHQuant提供三种模式，以适应不同类型策略的需求。


<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/触发方式列表.png" alt="中间面板" width="50%" />
</p>


### 一、Tick触发：灵活但负担重

Tick触发在理论上最为简单，它监听每一笔市场成交数据(在MiniQMT中实际是3秒一次快照)，并在数据到达时执行策略。这为策略的编写带来了很大的空间，在策略中可以灵活地添加过滤机制，或者说第二层触发判断，当真正达到触发条件时，再执行策略。第二层触发判断用户在编写时就有了极大的发挥空间。

这种方式的缺点是回测数据量大，因为需要下载所有tick数据——即使有第二层触发条件，这使策略执行频率不会太高。

### 二、K线触发：趋势策略的可靠选择

K线触发基于固定时间周期的数据，在回测环境中实现简单，只需补充对应周期的K线数据即可。

在看海回测系统中，支持1分钟和5分钟这两种K线周期。这并非功能限制，而是当前依赖的MiniQMT非投研版仅支持这两种分钟级别的实时数据订阅。

> 💡 **实盘与回测的触发差异**
>
> 值得注意的是，在实盘环境中，即便是订阅`1m`或`5m`的K线数据，行情接口的推送频率通常也是3秒一次，每次推送的都是当前时间点最新的完整K线数据。因此，若想在实盘中严格实现"每1分钟或5分钟K线走完后才触发一次"，反而需要框架层或策略层编写额外的逻辑来支持。回测模式下则严格在K线结束后执行。

K线触发由于数据量较小，系统资源消耗低，回测速度快，特别适合中长期趋势策略和技术分析策略，但这取决于您的具体策略设计。

### 三、自定义时间触发：增加自由度

自定义时间触发是KHQuant框架的关键功能，它允许用户指定精确的触发时间点，系统会在这些时间点到达时执行策略。

自定义时间触发特别适合定时交易策略，如开盘集合竞价策略、收盘前交易策略等，也适用于系统预设K线周期之外的场景(比如每10分钟，每小时等)。它为策略开发者提供了精确控制交易时机的能力。

> ⚠️ **注意：自定义触发的数据处理**
>
> 需要注意，自定义时间触发仅仅是"触发"策略的运行，**它不会像Tick或K线触发那样，自动向策略的 `khHandlebar` 函数中传入当时的数据**。策略需要在函数内部自行调用数据获取接口（如`get_market_data`）来获取所需数据。
>
> 这样设计是因为自定义时间点的前置数据需求多种多样，难以统一输入标准。同时，使用此类型触发的策略，其需求也往往比简单的K线数据更为复杂。

#### 智能数据适配与时间点生成

为了优化性能，系统会自动分析用户设定的时间点特性：

* 当所有触发时间点都是**整分钟**时（如09:30:00, 10:00:00），系统自动使用1分钟K线数据作为基础。
* 当存在**非整分钟**时间点时（如09:30:15, 10:05:45），系统则会切换到底层的Tick数据来确保精度。

这种智能适配在保证策略执行精度的同时，显著优化了系统资源使用和回测效率。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/触发方式.png" />
</p>

为了方便使用，我专门设计了一个**自定义时间生成模块**。通过设定起始、结束时间以及时间间隔，可以一键生成规范的触发时间列表。当然，也可以根据自己的需求在文本框中逐个手动编辑。

> 💡 **时间点精度提示**
>
> 由于MiniQMT的数据快照特性，所有时间点需设置为3的整数秒，以确保触发的稳定性和精确性。时间点生成工具已自动处理此逻辑。

---

## 7.2 账户信息

由于当前版本专注于**回测**功能，此区域的功能也相应简化：

* **初始资金**: 在此设置回测开始时策略所拥有的虚拟资金总额。
* **最小交易量**: 此项设置保留，但在当前的回测逻辑中并未实际启用限制。


<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/账户信息.png" />
</p>


---

## 7.3 盘前盘后触发设置

本功能允许策略在每日的特定时间点执行一些常规的、非核心交易逻辑的任务。


<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/盘前盘后触发设置.png" />
</p>


* **盘前任务 `khPreMarket`**: 勾选并设置时间（如 `09:25:00`），系统会在每个交易日的指定时间，自动调用策略代码中的 `khPreMarket` 函数。这通常用于执行开盘前的准备工作，例如：
  * 获取当日的股票池。
  * 取消所有昨日未成交的挂单。
  * 重置当日的状态变量。
* **盘后任务 `khPostMarket`**: 勾选并设置时间（如 `15:05:00`），系统会在每个交易日的指定时间，自动调用策略代码中的 `khPostMarket` 函数。这通常用于执行收盘后的复盘和清理工作，例如：
  * 统计当日交易情况。
  * 记录当日的持仓和资产快照。
  * 为第二天的交易进行数据预处理。

在下一章，我们将讲解右侧的信息反馈区，学习如何通过日志和回测报告来观察和分析我们的策略。
