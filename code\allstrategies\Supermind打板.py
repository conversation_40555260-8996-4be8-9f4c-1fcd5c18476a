# 涨幅监控策略（中证500版本）
# 当股票当日涨幅超过9%时买入，第二天开盘卖出

# 全局变量
g_today_bought = set()     # 今日买入的股票
g_yesterday_bought = set() # 昨日买入的股票，今日开盘卖出
g_last_log_time = None     # 上次记录日志的时间
g_is_first_bar = True      # 是否是当天第一个bar

# 初始化函数,全局只运行一次
def init(context):
    # 设置基准收益：中证500指数
    set_benchmark('000300.SH')
    
    # 设置要操作的默认股票
    context.security = '000300.SH'
    
    # 打印日志
    log.info('策略开始运行,初始化函数全局只运行一次')
    
    # 设置股票每笔交易的手续费为万分之三(买)/万分之十三(卖)
    set_commission(PerShare(type='stock', cost=0.0001, min_cost=5, tax_rate=0.001))
    
    # 设置股票交易滑点0.1%
    set_slippage(PriceSlippage(0.005))
    
    # 设置日级最大成交比例25%,分钟级最大成交比例50%
    set_volume_limit(0.25, 0.5)
    
    # 获取中证500成分股
    try:
        context.stocks = get_index_stocks('000300.SH')
        log.info('获取中证500成分股 {} 只'.format(len(context.stocks)))
    except Exception as e:
        log.error('获取中证500成分股失败: {}, 使用默认股票池'.format(str(e)))

    
    log.info('设置监控股票数量: {} 只'.format(len(context.stocks)))
    
    # 初始化存储触发价格的字典
    context.trigger_prices = {}
    
    # 设置最大持仓数量
    context.max_positions = 5

# 每日开盘前被调用一次
def before_trading(context):
    global g_today_bought, g_yesterday_bought, g_is_first_bar
    
    # 获取日期
    date = get_datetime().strftime('%Y-%m-%d')
    log.info('=== 交易日 {} 盘前运行 ==='.format(date))
    
    # 清空今日购买记录
    g_today_bought = set()
    
    # 重置第一个bar标志
    g_is_first_bar = True
    
    # 清空并重新计算触发价格
    context.trigger_prices = {}
    
    # 由于股票数量可能很多，输出一下计算进度
    total_stocks = len(context.stocks)
    log.info("开始计算 {} 只股票的触发价格...".format(total_stocks))
    
    # 使用分批处理方式获取开盘价数据
    batch_size = 50  # 每次处理50只股票
    for i in range(0, total_stocks, batch_size):
        batch_stocks = context.stocks[i:i+batch_size]
        log.info("处理第 {}-{}/{} 只股票".format(i+1, min(i+batch_size, total_stocks), total_stocks))
        
        for stock in batch_stocks:
            try:
                # 获取股票信息
                try:
                    stock_info = get_security_info(stock)
                    stock_name = stock_info.display_name if stock_info else stock
                except:
                    stock_name = stock
                
                # 获取前一日收盘价数据
                hist_data = history(stock, ['close'], 1, '1d', False, 'pre', is_panel=1)
                
                if not hist_data.empty:
                    open_price = hist_data['close'].iloc[0]
                    
                    # 计算触发价格（涨幅9%）
                    if open_price > 0:
                        trigger_price = open_price * 1.09
                        context.trigger_prices[stock] = trigger_price
                        
                        # 打印每只股票的开盘价和触发价格
                        log.info("股票 {}({}): 开盘价 {:.2f}, 触发价格 {:.2f}".format(
                            stock, stock_name, open_price, trigger_price))
            except Exception as e:
                log.error("计算 {} 触发价格时出错: {}".format(stock, str(e)))
    
    log.info("已计算 {} 只股票的9%涨幅触发价格".format(len(context.trigger_prices)))
    log.info("盘前处理结束")

# 开盘时运行函数
def handle_bar(context, bar_dict):
    global g_last_log_time, g_today_bought, g_yesterday_bought, g_is_first_bar
    
    # 获取当前时间
    current_dt = get_datetime()
    #log.info("-----------------------------------------------------")
    #log.info("执行 handle_bar @ {}".format(current_dt.strftime('%Y-%m-%d %H:%M:%S')))
    
    # 如果是每天的第一个bar，先卖出昨日买入的股票
    if g_is_first_bar:
        #log.info("执行每日第一个bar的操作，卖出昨日买入的股票")
        # 卖出昨日买入的股票
        for stock in g_yesterday_bought:
            if stock in context.portfolio.stock_account.positions:
                log.info("卖出昨日买入的股票: {}".format(stock))
                order_target(stock, 0)
        
        g_is_first_bar = False
    
    # 使用get_current()获取当前行情数据
    try:
        # 由于股票数量很多，我们可能需要分批获取行情数据
        batch_size = 100  # 每次处理100只股票
        all_current_data = {}
        
        # 筛选尚未持有且未买入的股票，作为待检查股票
        stocks_to_check = [stock for stock in context.trigger_prices.keys() 
                         if stock not in context.portfolio.stock_account.positions 
                         and stock not in g_today_bought]
        
        #log.info("今日待检查的股票: {} 只".format(len(stocks_to_check)))
        
        # 移除随机抽样，直接检查所有股票
        sampled_stocks = stocks_to_check
        
        # 分批获取行情数据
        for i in range(0, len(sampled_stocks), batch_size):
            batch_stocks = sampled_stocks[i:i+batch_size]
            batch_data = get_current(batch_stocks)
            if batch_data:
                all_current_data.update(batch_data)
        
        #log.info("获取到 {} 只股票的当前行情数据".format(len(all_current_data)))
        
        if not all_current_data:
            log.info("未获取到行情数据")
            return
    except Exception as e:
        log.error("获取行情数据出错: {}".format(str(e)))
        return
    
    # 检查当前持仓数量
    current_positions = len(context.portfolio.stock_account.positions)
    if current_positions >= context.max_positions:
        log.info("当前持仓数量 ({}) 已达或超过最大持仓限制 ({}), 不执行买入操作".format(
            current_positions, context.max_positions))
        return
    
    # 计算可用于买入的资金
    available_cash = context.portfolio.cash
    position_value = available_cash / context.max_positions
    #log.info("当前可用资金: {:.2f}, 单股可用资金: {:.2f}".format(available_cash, position_value))
    
    # 记录符合条件的股票
    potential_stocks = []
    
    # 检查每只股票
    for stock in sampled_stocks:
        if stock not in all_current_data:
            continue
            
        try:
            # 获取股票信息
            try:
                stock_info = get_security_info(stock)
                stock_name = stock_info.display_name if stock_info else stock
            except:
                stock_name = stock
            
            # 获取当前价格和触发价格
            current_price = all_current_data[stock].close
            trigger_price = context.trigger_prices[stock]
            
            # 输出比较信息
            #log.info("检查股票 {}({}): 当前价 {:.2f} vs 触发价 {:.2f}, 结果: {}".format(
            #    stock, stock_name, current_price, trigger_price, 
            #    "符合条件" if current_price >= trigger_price else "不符合条件"))
            
            # 如果当前价格超过触发价格，记录下来
            if current_price >= trigger_price:
                potential_stocks.append((stock, stock_name, 0, current_price, trigger_price))
        except Exception as e:
            log.error("处理股票 {} 时出错: {}".format(stock, str(e)))
    
    # 买入股票
    stocks_bought = 0
    for stock, stock_name, _, current_price, trigger_price in potential_stocks:
        # 计算可买股数（整数手）
        shares_to_buy = int(position_value / current_price / 100) * 100
        
        log.info("准备买入 {}({}): 单股资金 {:.2f}, 当前价 {:.2f}, 可买股数 {}".format(
            stock, stock_name, position_value, current_price, shares_to_buy))
        
        if shares_to_buy >= 100:  # 至少买一手
            log.info("执行买入: {}({}), 价格: {:.2f} > 触发价: {:.2f}, 数量: {}股".format(
                stock, stock_name, current_price, trigger_price, shares_to_buy))
            
            try:
                # 使用order函数下单
                order(stock, shares_to_buy)
                log.info("下单成功!")
                g_today_bought.add(stock)
                stocks_bought += 1
            except Exception as e:
                log.error("下单失败: {}".format(str(e)))
            
            # 如果已达到最大持仓数，直接退出
            if stocks_bought + current_positions >= context.max_positions:
                log.info("已达到最大持仓数({}/{})，停止买入".format(
                    stocks_bought + current_positions, context.max_positions))
                break
        else:
            log.info("资金不足买入一手 {}({}), 当前价: {:.2f}, 需要资金: {:.2f}".format(
                stock, stock_name, current_price, current_price*100))
    
    #log.info("handle_bar执行完毕，本次买入: {} 只股票".format(stocks_bought))
    #log.info("-----------------------------------------------------")

# 收盘后运行函数
def after_trading(context):
    global g_yesterday_bought, g_today_bought
    
    # 获取时间
    time = get_datetime().strftime('%Y-%m-%d %H:%M:%S')
    log.info('=== 交易日 {} 盘后运行 ==='.format(time))
    
    # 更新昨日买入记录，用于明天开盘卖出
    g_yesterday_bought = g_today_bought.copy()
    
    if g_today_bought:
        log.info("今日买入股票: {}".format(list(g_today_bought)))
        log.info("明日开盘将卖出股票: {}".format(list(g_yesterday_bought)))
    else:
        log.info("今日未买入股票")
    
    log.info("当前资金: {:.2f}, 总资产: {:.2f}".format(
        context.portfolio.cash, context.portfolio.total_value))
    log.info("====================")
    log.info('一天结束')