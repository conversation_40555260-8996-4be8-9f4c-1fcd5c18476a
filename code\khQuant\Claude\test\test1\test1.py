# 测试单股订阅subscribe_quote
from xtquant import xtdata
import time
import datetime

# 1.定义回调函数
def on_data(datas):
    """数据更新回调函数"""
    # 获取当前时间（精确到毫秒）
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    print(f"\n当前时间: {current_time}")
    
    # datas格式为 { stock_code : [data1, data2, ...] }
    code_list = list(datas.keys())
    
    # 遍历所有股票代码及其数据
    for stock_code in datas:
        print(f"\n股票: {stock_code}")
        for data in datas[stock_code]:
            if 'time' in data:
                # 将数值时间转换为可读时间（假设time是整数时间戳，单位是秒）
                time_value = data['time']
                readable_time = ""
                
                # 根据time的格式进行转换
                if isinstance(time_value, int):
                    if time_value > 10000000000:  # 毫秒级时间戳
                        readable_time = datetime.datetime.fromtimestamp(time_value/1000).strftime('%Y-%m-%d %H:%M:%S')
                    else:  # 秒级时间戳
                        readable_time = datetime.datetime.fromtimestamp(time_value).strftime('%Y-%m-%d %H:%M:%S')
                elif isinstance(time_value, str):
                    # 如果是字符串格式，则直接打印
                    readable_time = time_value
                
                print(f"时间戳: {time_value}, 可读时间: {readable_time}")
            
            # 打印完整数据
            print(data)
    '''
    # 在回调中获取完整K线数据
    klines = xtdata.get_market_data_ex(
        field_list=["time", "open", "high", "low", "close", "volume"],
        stock_list=code_list,
        start_time="20241213",
        period="1m"
    )
    print("\n完整K线数据:")
    print(klines)
    '''
# 2.订阅10只股票数据
stock_codes = [
    "000001.SZ",  # 平安银行
    "000002.SZ",  # 万科A
    "000063.SZ",  # 中兴通讯
    "000333.SZ",  # 美的集团
    "000651.SZ",  # 格力电器
    "000858.SZ",  # 五粮液
    "600036.SH",  # 招商银行
    "600519.SH",  # 贵州茅台
    "601318.SH",  # 中国平安
    "601988.SH"   # 中国银行
]

# 循环订阅每只股票
for stock_code in stock_codes:
    xtdata.subscribe_quote(
        stock_code=stock_code,
        period="tick",            # 日K线
        count=1,                # 获取当天所有数据
        callback=on_data        # 设置回调函数
    )
    print(f"已订阅: {stock_code}")

# 3.保持程序运行
print("开始接收实时数据...")
try:
    xtdata.run()
except KeyboardInterrupt:
    print("程序结束")