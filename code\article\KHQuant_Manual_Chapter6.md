# 第六章：核心配置项：左侧面板精解

左侧面板是"看海量化交易系统"的策略配置中枢。在这里，将定义策略的灵魂（策略文件）、仿真的环境（回测参数）、必需的数据（数据设置）以及执行的范围（股票池）。本章将逐一详解这些配置项，帮助精确地掌控策略的每一个细节。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/左侧面板.png" alt="左侧面板" width="50%" />
</p>

---

## 6.1 "策略配置"组：指定策略的大脑

这是所有设置的起点，需要在这里告诉系统要运行哪个策略。

* **策略文件 (Strategy File)**
  * **功能**: 通过点击 **选择策略文件** 按钮，可以从本地文件夹中加载编写的Python策略脚本（必须是 `.py` 结尾的文件）。这个文件是策略的核心，包含了所有的交易逻辑。
  * **要求**: 框架通过动态加载此模块来执行策略。因此，策略文件需要符合一定的规范，例如包含 `khHandlebar` 等关键的回调函数。关于策略编写的详细规范，请参阅第十二章《核心驱动：策略编写指南》。
* **运行模式 (Run Mode)**
  * **功能**: 当前版本专注于回测功能，此选项固定为 **回测(Backtest)** 模式。
  * **模式说明**: 回测是量化交易的基石。它使用历史数据来模拟策略的过往表现，从而可以在不产生任何真实风险的情况下，检验和评估策略的有效性。

## 6.2 "回测参数"组：精雕细琢的模拟环境

此区域的设置核心目的是尽可能真实地模拟历史交易环境，从而让回测结果更具参考价值。

* **基准合约 (Benchmark)**
  * **功能**: 设定一个业绩比较基准，通常是市场主流指数。系统会根据此基准计算策略的Alpha（超额收益）、Beta（市场相关性）等关键绩效指标。
  * **如何设置**: 直接在文本框中输入想作为基准的合约代码。沪深300指数最为常用，目前系统也仅调试适配了沪深300。
  * **常见示例**: `sh.000300` (沪深300)。
* **交易成本设置 (Transaction Costs)**
  * **功能**: 精确模拟交易中产生的各项费用。忽略交易成本的回测报告是毫无意义的，因为它会系统性地高估策略表现。关于交易成本的详细构成，推荐阅读[这篇文章](https://zhuanlan.zhihu.com/p/29310540747)。
  * **参数详解**:
    * **最低佣金(元)**: 许多券商对单笔交易设有5元的最低佣金收费，即使按比例计算的佣金不足5元，也会按5元收取。
    * **佣金比例**: 根据券商费率设置，例如万分之一，则应填入 `0.0001`。
    * **卖出印花税**: 目前A股为单向收取，仅在卖出时征收，税率为千分之0.5，应填入 `0.0005`。
    * **流量费(元/笔)**: 部分券商可能会对每笔交易收取固定的信息服务费或流量费，具体费率请向您的开户券商确认。
* **滑点设置 (Slippage)**
  * **功能**: 在真实交易中，由于市场流动性、订单执行速度等因素，最终成交价格与策略的理想委托价格之间常存在微小差异，这种差异就是**滑点**。在高频交易或流动性差的品种上，滑点是影响策略盈利的关键因素，因此在回测中必须对其进行模拟。本软件提供以下两种滑点模拟方式：
  * **滑点模拟方式**:
    1.  **按最小变动价位 (Tick模式)**: 这种方式模拟的是因价格跳动导致的滑点，适合对市场微观结构有深入理解的投资者。
        *   **原理**: 对于A股股票，最小变动价位是0.01元。若在"滑点值"中设为 `1`，则系统在计算成交时，会自动将买入价在委托价基础上上浮0.01元，将卖出价下浮0.01元，以此模拟一个"更差"的成交价格。
    2.  **按成交金额比例 (Ratio模式)**: 这种方式模拟的是因冲击成本等因素产生的滑点，更简单直观，适合大多数投资者。
        *   **原理与双边计算**: 系统采用的是**双边滑点**模型。您在界面上设定的数值是一个**百分比**，代表买卖双边的总滑点。在单次交易中，系统会**将这个百分比代表的比例值除以2**后应用到成交价上。
        *   **示例**: 若在"滑点值"中设为 `1` (代表 **1%** 的总滑点)：
            *   系统会将 `1%` 转换为比例 `0.01`。
            *   对于**买入**订单，实际成交价会是 `委托价 * (1 + 0.01 / 2)`，即价格上浮 `0.5%`。
            *   对于**卖出**订单，实际成交价会是 `委托价 * (1 - 0.01 / 2)`，即价格下浮 `0.5%`。
        *   **价格取整**: 计算出的新价格会**四舍五入到小数点后两位**（即精确到分），以模拟真实的报价机制。例如，一个理想买入价为10.00元的订单，在 `1%` 的双边滑点下，计算出的新价格是 `10.00 * (1 + 0.005) = 10.005`，四舍五入后为 `10.01`元。
        *   **使用建议**: 滑点的大小与股票的流动性密切相关。对于大盘蓝筹股，滑点可能很小；而对于小盘股或冷门股，滑点可能远大于0.1%。建议根据交易标的的特性调整滑点参数，以获得更准确的回测结果。
  * **如何设置**: 在"滑点类型"下拉框中选择一种模式，然后在右侧的"滑点值"输入框中设定相应的数值。

## 6.3 回测周期设置

* **功能**: 通过"开始日期"和"结束日期"这两个日历控件，可以精确设定回测的起止时间范围。
* **使用建议**: 为了全面评估策略的稳健性，建议选择足够长的时间段，并确保该时段覆盖了牛市、熊市和震荡市等多种不同的市场环境。

---

## 6.4 "数据设置"组：策略的数据食粮

在回测过程中，系统会读取预先补充好的本地数据文件。本组设置决定了在回测时具体读取哪些数据，以及如何对数据进行加工（如复权）。

* **复权方式**:
  *   复权是指为了消除因分红、送股、配股等除权除息（XD）事件导致股价图上出现的价格"跳空"缺口，而对历史股价进行重新计算的过程。这能让技术指标和股价走势保持连续性，从而更真实地反映股票的长期价值增长。软件提供以下几种复权方式：

    > **1. 前复权**
    >
    > **介绍**：前复权就是以目前股价为基准，保持现有价位不变，缩减以前价格，使图形吻合，保持股价走势的连续性。简单说就是把除权前的价格按现在的价格换算过来，复权后现在价格不变，以前的价格减少。
    >
    > **举例来说**：假设A股票10元/股，因除权等原因变成5元/股，在当日股价没有涨跌的情况下，选择前复权后，当天与前一天的股价都是5元/股，之前的股价都会按照一定比例缩小。
    >
    > **作用**：采用前复权历史股价就可以更加准确地反映出股票的涨跌幅和收益情况。

    > **2. 不复权**
    >
    > **介绍**：不复权是指在股票交易中，不考虑除权、除息等事件对股价的影响，直接以当天的实际交易价格作为收盘价进行计算。这样做会导致历史数据的断层，无法准确反映出股票的真实涨跌幅和收益情况。
    >
    > **例如**：如果一只股票在某个日期发生了除权或除息事件，假设这个事件使得股价下跌10%，那么不复权的情况下，历史数据将会按照该事件当天的实际价格进行计算，而不会考虑到除权除息事件带来的影响。
    >
    > **作用**：采用不复权，K线图能真实反映股价历史的除权信息。

    > **3. 后复权**
    >
    > **介绍**：后复权是指在K线图上以除权前的价格为基准来测算除权后股票的市场成本价。简单说就是把除权后的价格按以前的价格换算过来，复权后以前的价格不变，现在的价格增加。
    >
    > **举例来说**：假设A股票10元/股，因除权等原因变成5元/股，在当日股价没有涨跌的情况下，选择后复权后，当天与前一天的股价都是10元/股，之后的股价都会按照一定比例放大。
    >
    > **作用**：采用后复权能够看出股票真实价值的增加及持股者的真实收益率。
    
    > **4. 等比前复权 / 等比后复权**
    >
    > 与常规的前/后复权算法类似，但在价格调整时采用不同的数学模型。在大多数场景下，其效果与普通复权非常接近。

* **周期类型**:
  *   **定义**: 选择策略运行所依赖的数据更新频率。不同的周期适用于不同类型的策略。例如，高频交易策略可能依赖Tick数据，而日内趋势策略可能使用分钟线。
* **数据字段 (Data Fields)**
  * **功能**: 此区域的复选框列表让可以精确选择策略在运行时需要哪些数据。例如，一个简单的均线策略可能只需要收盘价 `close`，而一个复杂的因子模型可能需要开、高、收、低、成交量、成交额等多个字段。
  * **优化作用**:

    > 💡 **小贴士**：请仅勾选策略中确定会用到的字段。这样做有两个好处：
    >
    > 1. **减少内存占用**: 系统无需加载和存储不需要的数据。
    > 2. **加快数据读取速度**: 在进行大规模数据回测时，效果会非常明显。
    >
  * **动态变化**: 可勾选的字段列表会根据在"周期类型"中选择的周期动态变化。例如，Tick周期和K线周期所能提供的数据字段是不同的。

## 6.5 "股票池设置"组：圈定执行范围

股票池定义了策略将在哪些证券范围内进行观察和交易。系统提供了灵活多样的股票池构建方式。

* **常用指数成分股 (Common Index Constituents)**
  * **功能**: 这是最快捷的股票池构建方式。只需勾选相应的复选框，即可将A股市场主流指数（如上证50、沪深300、中证500、创业板指、科创50、上证A股、沪深A股）的全部成分股一次性加入到股票池中。
* **自选清单 (Custom List)**
  * **功能**: 点击 **"自选清单"** 这几个字，系统会用默认文本编辑器（如记事本）打开一个`csv`文件，勾选此项后，系统会加载该文件中的自定义股票列表。
  * **如何编辑**: 在打开的文本文件中编辑股票列表，每行一只股票，编辑完成后直接保存文件即可。请确保文件格式正确：每行格式为`股票代码,股票名称`，例如：
    ```csv
    600036.SH,招商银行
    000001.SZ,平安银行
    688981.SH,中芯国际
    ```
* **手动管理列表 (Manual Management)**
  * **功能**: 当需要进行临时的、更灵活的股票池管理时，可以使用此功能。下方的表格提供了一个可视化的股票列表。
  * **操作方法**:
    * **添加股票**: 直接在表格的"股票代码"列的空白行中输入股票代码，然后按回车键，系统会自动填充股票名称。
    * **删除股票**: 单击选中想删除的一行或多行（按住`Ctrl`可多选），然后按键盘上的 `Delete` 键即可。
    * **导入列表**: 在表格区域 **单击鼠标右键**，会弹出一个上下文菜单，选择 **导入股票列表**。可以从一个 `.csv` 文件（每行一个代码）批量导入股票。请注意，导入文件中的格式也应为`股票代码,股票名称`。
    * **清空列表**: 在右键菜单中选择 **清空所有**，可以快速删除当前手动管理列表中的所有股票。

> ✨ **组合使用**: 上述几种方式可以组合使用。例如，可以先勾选"沪深300"，然后再手动添加几只特别关注的、不在沪深300内的股票。最终的股票池是所有选定方式的并集。

至此，我们已将左侧核心配置面板的每一个角落都探索完毕。掌握了这些设置，就拥有了为策略量身打造运行环境的能力。在下一章，我们将移步至中间面板，探索策略的"心跳"—触发机制。
