大家好，我是看海。

从今天起，我将陆续发布一系列关于"看海量化交易系统"的详细使用手册。当前，系统正处于紧锣密鼓的内部测试阶段，这些手册旨在帮助参与测试的朋友们，以及对本系统感兴趣的各位，能够更全面、更深入地了解它的各项功能与操作细节。

今天我们来看第三章，这一章将指导您完成运行环境的配置、系统的安装，以及首次启动时的关键设置。这就像战前检查装备，虽显繁琐，却至关重要。

---

## 3.1 运行环境要求

为了让"看海量化交易系统"在您的电脑上流畅运行，请确保满足以下基本环境要求：

### 3.1.1 硬件与操作系统

*   **操作系统**: **Windows 10 或更高版本的64位系统**。

    > 💡 **开发者言**：由于本系统的核心依赖——MiniQMT客户端主要运行于Windows平台，因此KHQuant的主要开发和测试环境也都在Windows上。虽然理论上可能通过虚拟机或兼容层在其他系统（如macOS, Linux）上运行，但这并非官方支持的路径，可能会遇到各种意想不到的兼容性问题。
    >
*   **硬件建议**:

    *   **CPU**: 建议使用现代多核处理器（如 Intel i5 或 AMD R5 及以上）。
    *   **内存 (RAM)**: 建议 **16GB** 或以上。请注意，内存的实际需求在很大程度上取决于您策略的复杂度和数据处理量。对于涉及大规模数据回测或复杂机器学习模型的策略，更大的内存将显著提升运行效率。我们将在后续的更新中，提供更精确的关于基础软件运行的最低硬件要求。
    *   **硬盘**: 建议使用 **固态硬盘 (SSD)**，以加快数据读取和软件启动速度。

### 3.1.2 核心依赖软件

*   **MiniQMT 客户端**: **这是本系统运行的绝对前提**。
    您必须首先从您开户的券商处下载并安装最新版的MiniQMT客户端，并确保您能够正常登录您的账户。本系统所有的数据获取和交易指令（若未来支持）都将通过此客户端完成。[如尚未开通miniQMT，可以点击这里免费开通。](https://khsci.com/khQuant/miniqmt/)

    成功安装后，打开miniQMT客户端将会显示下边的界面：
*   **Microsoft Visual C++ Redistributable**:
    为了确保系统图形界面和部分依赖库的正常工作，您需要安装 `Microsoft Visual C++ 2015-2022 Redistributable (x64)`。

    > 🔗 **官方下载链接**: [https://aka.ms/vs/17/release/vc_redist.x64.exe](https://aka.ms/vs/17/release/vc_redist.x64.exe)
    >
    > **如何检查？** 通常，如果您能正常运行其他大型软件或游戏，这个组件很可能已经安装。如果不确定，直接下载并运行安装程序即可，它会自动判断是否需要安装或修复。
    >

---

## 3.2 看海量化系统安装

我们提供了简单快捷的安装包，让您轻松完成所有部署工作。

**1.获取安装包**: 从[看海量化交易系统官网](https://khsci.com/khQuant/)下载最新的安装文件压缩包到本地（解压密码在压缩包名称里），右键解压缩后，将会得到一个exe文件，双击exe文件安装。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_08245868e6125c1dd285cba688adb2e0.jpg" />
</p>

选择软件安装路径，一直点击下一步，直到安装成功。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_04e92a96668bf3cb9cb0a66bf77ae5e2.jpg" />
</p>

安装完成后桌面上将会有软件的快捷方式，双击可以打开软件。

**2.注意事项**:

*   您可能会看到Windows安全提示，请选择"更多信息"，然后点击"仍要运行"。
*   建议保持默认的安装路径 (`C:\Program Files (x86)\khQuant`)，或选择您喜欢的其他路径。
*   在"附加任务"步骤，建议勾选"创建桌面快捷方式"，方便日后快速启动。
*   点击"安装"，稍等片刻即可完成。

---

## 3.3 第一次启动：关键设置要点

无论您通过哪种方式安装，首次启动软件时，请务必完成以下关键设置，这是连接系统与MiniQMT的"握手"环节。

1.  **打开设置对话框**:
    启动软件后，在主界面顶部工具栏找到并点击 **设置** 按钮，打开"软件设置"对话框。
2.  **配置MiniQMT双路径**:
    在"软件设置"对话框中，找到 **客户端设置** 区域。这里有两个至关重要的路径需要您正确配置，它们共同构成了KHQuant与MiniQMT之间的桥梁。

    <p align="center">
        <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/QQ截图20250609233033.png" />
    </p>

    *   **miniQMT客户端路径**:

        *   **作用**: 指向MiniQMT的**主程序文件**。
        *   **如何设置**: 点击该项旁边的 **浏览...** 按钮，在弹出的文件选择窗口中，找到您的MiniQMT安装目录，并进入 `bin.x64` 文件夹，选择 `XtItClient.exe` 文件。
        *   **示例路径**: `D:\国金证券QMT交易端\bin.x64\XtItClient.exe`
    *   **miniQMT路径**:

        *   **作用**: 指向MiniQMT的**用户数据文件夹**，其中包含了账户信息、日志等关键数据。
        *   **如何设置**: 点击该项旁边的 **浏览...** 按钮，在弹出的文件夹选择窗口中，找到并选择您本地MiniQMT的 **`userdata_mini`** 文件夹。
        *   **示例路径**: `D:\国金证券QMT交易端\userdata_mini`

    > 🎯 **路径在哪找？**
    > 这两个路径通常都在您的MiniQMT安装根目录下。例如，如果您的MiniQMT安装在 `D:\国金证券QMT交易端`，那么按图索骥即可找到。**请务必确保两个路径都已正确设置**，否则系统无法正常工作。
    >
3.  **保存设置**:
    完成两个路径的配置后，点击"软件设置"对话框右下角的 **保存设置** 按钮。软件会保存您的配置。请注意，此设置仅用于数据回测等功能，与主界面上的MiniQMT连接状态指示灯无关。

---

## 3.4 启动与连接

上述步骤讲述了主界面的初始设置，但是需要注意的是，在这个初始设置完成后，后续每次使用看海量化系统，都需要按照以下顺序启动软件（即先启动miniQMT，再启动看海量化交易系统）。

### 3.4.1 系统关系说明：客户端与接口

为了更好地理解为何必须先启动MiniQMT，这里对看海量化系统与MiniQMT的关系做个简要说明：

> *   **看海量化系统**与**MiniQMT**是两个独立的软件。
> *   本软件的角色是一个**接口调用方（客户端）**。它本身不处理与券商服务器的直接通讯。
> *   MiniQMT登录后，会在后台提供一个编程接口（API），所有的数据获取和交易指令都是通过这个接口完成的。
>
> **简而言之，看海量化系统的工作依赖于MiniQMT提供的接口服务。如果MiniQMT没有启动，本软件就无法连接和工作。**

### 3.4.2 正确的启动顺序

为了确保系统能够顺利连接到MiniQMT，请务必遵循以下两步启动流程：

1.  **第一步：启动并登录MiniQMT**

    *   首先，打开您的券商MiniQMT客户端。
    *   在登录界面，请务必勾选 **"极简模式"**（部分券商版本可能称之为"独立交易"）。
    *   成功登录后，MiniQMT将自动在后台提供数据和交易接口服务。

    > ⚠️ **重要提示**：必须先成功登录MiniQMT，再进行下一步。看海量化系统本身无法启动MiniQMT。
    >
2.  **第二步：启动看海量化系统**

    *   在MiniQMT运行的情况下，双击桌面上的看海量化系统快捷方式来启动软件。

### 3.4.3 检查连接状态

软件启动后，主界面右上角会有一个 **MiniQMT状态指示灯**，它直观地反映了当前的连接情况。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/QQ截图20250609233411.png" />
</p>

*   **🟢 绿色**: **连接成功**。恭喜！系统已成功连接到MiniQMT，您可以开始进行回测或其他操作了。
*   **🔴 红色**: **连接失败**。警报！这表示系统未能找到正在运行的MiniQMT。请按照以下步骤进行排查：
    1.  **检查启动顺序**：确认您是**先启动并登录了MiniQMT**，然后再打开的本软件。
    2.  **检查登录状态**：确认您的MiniQMT客户端已经**成功登录**账户，而不仅仅是停留在登录界面。
    3.  **检查路径配置**：回到 `设置` -> `客户端设置`，再次检查您在3.3节中设置的 **两个MiniQMT路径** 是否完全正确。
    4.  **重启**：尝试依次关闭本软件和MiniQMT客户端，然后重新按照正确的顺序启动它们。

完成以上步骤，您的"看海量化交易系统"就已整装待发。下一章，我们将带领您完成第一个回测流程！

---

## 如何加入内测，快人一步？

看到这里，相信您已经对如何让系统跑起来有了清晰的认识。如果您渴望更早地体验新功能，与量化同好深度交流，欢迎您加入我们的内部测试。

**内测资格目前只对通过我推荐渠道开户的朋友开放**。这既是对我个人开发工作的一种支持，也是我们回馈核心朋友的一种方式。

**加入内测，您将获得：**

*   **版本领先体验**：您将永远比公开发布版领先一个大功能版本，提前使用到正在开发中的新功能。
*   **专属交流群**：受邀加入内部交流群，与作者及众多量化爱好者直接交流，获取及时的帮助和策略思路分享。
*   **未来核心福利**：后续如果我开通知识星球等高阶社群，**内测群成员将免费获赠一年的会员资格**，共享更深度的研究成果。

如果您对此感兴趣，请关注我的公众号"看海的城堡"，在公众号页面下方点击相应标签即可获取开户方式，开通后联系我即可加入。

感谢您的阅读，我们下一章再见！ 