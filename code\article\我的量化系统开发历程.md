> 我是Mr.看海，我在尝试用信号处理的知识积累和思考方式做量化交易，应用深度学习和AI实现股票自动交易，目的是实现财务自由\~ 目前我正在开发基于miniQMT的量化交易系统——看海量化交易系统（KhQuant 是其核心框架）。

经过了大约半年的密集开发和持续迭代，看海量化交易系统从一个最初的想法，一步步成长为现在这个功能相对完备的版本，内心充满了感慨。这期间，经历了136次大大小小的代码更新，每一次提交都见证了系统的演进。所以，在此编写代码使用手册的间隙，简单分享一下开发心得，也为有意向做类似开发工作的朋友提供一些参考。

![动图封面](https://pic1.zhimg.com/v2-61dd84c000a716fcc49197cb62c801f8_720w.jpg?source=d16d100b)

## 一、开发方式

整个看海量化交易系统的开发，到目前为止，团队成员只有我一个人。在具体的编码实现过程中，主要依赖Cursor作为主力开发工具，并深度借助大语言模型（主要是Claude系列）进行辅助编程。这种模式极大地提升了开发效率，尤其是在快速构建功能原型和处理一些繁琐编码任务时。然而，随着项目代码量的不断膨胀，即便是当前最先进的大模型，在理解和处理复杂上下文关系时也逐渐显现出瓶颈，响应速度和准确性都有所下降。在这个过程中，我的角色更像一个具备技术背景的产品经理，既要能够清晰、准确地向大模型描述功能需求和逻辑流程，又需要能够快速识别出模型生成的代码中可能存在的错误、缺陷或潜在的逻辑漏洞，并引导其修正。

在我自用的内控Git版本管理中，经过盘点，从项目启动至今，大大小小的提交总共有136次。虽然系统的大框架没有经历颠覆性的调整，但在功能细节、用户体验、运行效率等方面则是经过了反复的推敲与打磨。整个开发历程大致可以划分为三个主要阶段。

![](https://picx.zhimg.com/80/v2-0adc31f2804637752b2b2f6f013ce16c_720w.webp?source=d16d100b)

![](https://pic1.zhimg.com/80/v2-6d3e92549725828669953b959ad2b356_720w.webp?source=d16d100b)

![](https://pic1.zhimg.com/80/v2-be50c849b30b9de3b0c53ee79b7f7233_720w.webp?source=d16d100b)

![](https://picx.zhimg.com/80/v2-1732a31ca327e55a82148c5e51b58cbc_720w.webp?source=d16d100b)

## 二、主要开发阶段

### 第一阶段：夯实地基——从数据下载与清洗切入

做量化交易，数据是最底层的基石。所以在项目刚开始的时候，就把主要精力放在了数据下载和数据清洗这两个基础任务上。毕竟，在量化这个精密的世界里，要是数据不靠谱、乱七八糟的，再牛的策略也白搭。最初的一个核心想法是，将获取到的数据显式地保存为CSV格式。这样做的好处在于，CSV作为一种通用的文本格式，可以方便地在后续进行数据处理时，跨越不同的分析平台或机器学习框架进行数据导入、分析乃至模型训练，这种开放性的思路在后续的开发中也得到了保留和贯彻。

这个阶段，主要解决了这么几个关键问题：

1. **搞定数据来源**：在数据源的选择上，最终确定采用MiniQMT的接口来获取行情数据。这主要是考虑到MiniQMT不仅提供了数据获取的API，更重要的是它还集成了交易接口。这种“数据+交易”一体化的接口提供方式，为后续完整量化交易流程的实现提供了极大的便利性和前瞻性。
2. **统一数据存储格式**：为了确保数据的规范性和易用性，明确了所有下载的原始数据和清洗后的数据统一以CSV格式进行存储。不仅如此，对存储的文件名也制定了标准的命名规范，确保了数据文件管理的有序性和可追溯性。
3. **给数据“洗澡”和“体检”**：原始数据里经常混着些错的、漏的，或者一看就不对劲的数字（比如股价是0，或者成交量突然变得特别大）。所以，编写了一套数据清洗程序，能够有效地去除重复数据，通过合理的方式（如前值填充、均值插补等）填充缺失值，并识别和处理那些明显的异常数据点，从而保证最终用于分析和回测的数据是准确和干净的。

目前在看海量化网站上能够下载到的公开发布的软件版本，其核心功能就大致截止到以上所述的数据处理部分。这部分工作大约是在今年（2025年）一月份基本完成的，其后，项目的重心便全面转向了更为复杂和核心的量化回测系统的设计与编码阶段。

![](https://pic1.zhimg.com/80/v2-3641f26a949d42303f361112c08449c7_720w.webp?source=d16d100b)

数据下载、补充和清洗模块

### 第二阶段：核心构建——打造回测引擎

当数据这块地基打得差不多扎实了，注意力就自然转到了系统的“发动机”——一个强大又好用的回测引擎上。毕竟，一个策略到底行不行，得拉到历史数据里跑一跑才知道。所以，研发的重心就转到了回测系统的搭建上。

在这个核心阶段，有几项关键的进展和转变：

1. **系统架构的转变与框架的确立**：一个重要的转变是将系统的设计核心从先前“以数据为中心”转变为“以策略为中心”。原先独立的数据下载与处理模块，被重构成整个量化系统的一个基础服务组件。在回测系统的开发过程中，逐步确定并完善了一套能够同时兼顾历史回测、模拟交易以及未来实盘交易需求的统一框架（khQuant核心框架）。这个框架的设计目标是提供一个稳定、高效且易于扩展的底层支持，让策略开发者能更专注于策略逻辑本身。
2. **回测系统核心功能的全面搭建**：此阶段投入了大量精力进行回测系统的核心功能建设。这包括但不限于：明确和实现了策略与框架的交互接口（即策略框架），定义了盘前（khPreMarket）和盘后（khPostMarket）的回调机制以满足策略在不同交易时段的特定需求，开发了一套结构化的日志系统用于记录回测过程中的关键信息和潜在问题，并且初步构建了回测结果的可视化展示模块以及关键指标的计算功能，为策略的评估提供直观的数据支持。
3. **数据模块的再升级——引入数据补充机制**：在回测功能逐步完善的过程中，意识到仅仅依赖初次下载的数据是不够的。为了进一步提升数据的完整性和准确性，对原有的数据模块进行了功能升级，在基础的数据下载功能之外，增加了更为智能的**数据补充功能**。该功能能够从数据源进行针对性的补充，确保了回测时使用的是尽可能完整和高质量的数据集，从而提升了回测结果的可靠性。

至此，大约是在今年（2024年）三月份，看海量化交易系统的主体功能，特别是回测部分，已经基本开发完成。为了全面检验系统的准确性、稳定性以及运行效率，紧接着便进入了下一个关键环节——跨平台的横向测评与对比分析。

![](https://picx.zhimg.com/80/v2-16dbc18d2eb71f6a8615a70c30a09cff_720w.webp?source=d16d100b)

### 第三阶段：实战检验——与成熟平台横向测评

当量化系统的核心功能都做得差不多，内部也反复测试过之后，一项很重要的工作就是：把它和市面上一些成熟的量化平台拉到一起，做个全方位的比较。这么做的目的，不光是为了看看自己做的系统在功能全不全、跑得快不快、回测结果准不准这些方面达没达到预期。更重要的是，通过和这些高手过招，能学习到别人好的设计思路、好的算法、好的用户体验，也能更清楚地看到自己系统哪里还有不足，以后往哪个方向改进才对。

这次“比武”主要关注这么几点：

1. **回测结果能不能对得上**：用同样的策略和数据，在自己的系统和别人的平台上分别跑回测，仔细比较那些关键的评价指标（比如一年能赚多少、风险多大、夏普比率怎么样）是不是差不多。
2. **系统跑得怎么样**：比比看在同样的电脑上，跑回测的速度、处理数据的效率，还有占内存多不多这些硬指标。
3. **好不好用，活不活泛**：站在写策略的人的角度，看看写代码方不方便，调参数麻不麻烦，系统以后想加新功能容不容易。

经过这一番深入细致的比对和验证，结果反而有些出乎意料。在对比过程中，陆续发现一些被广泛使用的大型平台，在某些细节处理上竟然也存在不尽如人意之处，例如超透支、交易价格的精确位数处理不当、甚至出现该下单的逻辑未能正确触发等情况。这让人不禁感叹，所谓“完美”的系统或许真的不存在，即便是成熟的商业平台也并非无懈可击，某种程度上印证了“世界果然是个草台班子”的调侃。通过这次横向测评，基本确认了看海量化回测系统在核心功能上的可用性、结果的准确性以及运行效率方面都达到了一个令人满意的水平。至此，现阶段的主要开发工作告一段落，系统正式转入内部测试阶段，开始接受更广泛和更贴近实战的检验。

![](https://picx.zhimg.com/80/v2-c7b87d172910683bfb6bfaba94beb3ad_720w.webp?source=d16d100b)

## 三、近期更新：聚焦易用性，迎接回测版发布

最近这两天，主要精力放在了提升软件的好用程度上。毕竟，一个工具就算功能再强大，用起来别扭不顺手，也会让人用着不爽，效率也上不去。

近期的易用性升级主要体现在这几个方面：

1. **适应各种大小的屏幕**：大家的电脑屏幕分辨率五花八门，为了让每个人看着都舒服，对界面做了优化，保证在不同大小的屏幕上，布局都合理，字也看得清楚，不会乱糟糟的。
2. **数据补充情况及时知道**：在补充数据的时候，如果有的股票怎么补数据都是空的（比如那段时间它确实没交易），系统会立刻告诉你，让你明明白白，不用瞎猜或者傻等。
3. **防止鼠标滚轮误操作**：有时候用下拉菜单或者选日期，鼠标滚轮不小心一滚，选项就变了，挺烦人的。所以，把这些地方的鼠标滚轮功能给关了，这样操作起来更准，不容易出错。

经过这一轮细致的打磨，现在可以说，系统的回测功能在核心逻辑和用户体验上，都基本让人满意了。各项功能跑得挺稳，用起来也顺手多了。这么一来，回测版本也快准备好了，这些天键盘快敲冒烟了，为了让它能早点和大家见面！

## **四、下一步工作**

软件内测发布前的测试工作已经基本完成了，剩下的是一些收尾工作，包括使用手册撰写，软件封装等等。不会让大家等太久了，内测很快就要开始！

**内测计划说明**:

在完成上述更充分的验证测试之后，我计划启动"看海量化交易系统 (KhQuant)"的 Beta 内测阶段。

* **优先体验**: 为了感谢大家的支持，通过**我推荐的渠道开通****MiniQMT****账户的朋友**，在内测开始后将获得优先体验 Beta 版本软件，可以加入内部讨论群第一时间得到作者的问题解答，后续的一些策略也在内部讨论群小范围分享。
* **公开与开源**: 请暂时不方便通过推荐渠道开户的朋友放心，**内测结束后，软件将会公开发布，核心代码也计划进行开源**，届时所有人都可以使用和参与改进。

## **五、 关于开通 MiniQMT**

**什么是 MiniQMT？**

MiniQMT 是迅投（QMT）系统提供的一个程序化交易接口（API）。QMT 是目前国内许多券商采用的主流柜台系统之一，而 MiniQMT 允许用户通过编程方式（主要是 Python）连接到证券公司的交易服务器，进行行情获取、策略计算、下单交易等操作。它通常由支持 QMT 的券商**免费**提供给客户使用（可能需要满足一定的资产要求），以其稳定性和执行效率受到不少量化交易者的青睐。

**看海量化交易系统 (KhQuant) 与 MiniQMT**

我正在开发的"看海量化交易系统 (KhQuant)"正是**基于 MiniQMT 接口**进行构建的。这意味着，使用该软件需要一个 MiniQMT 账户作为底层支持。

**推荐开通渠道**

如果您还没有 MiniQMT 账户，并希望未来能够顺利使用"看海量化交易系统 (KhQuant)"进行策略回测，或者希望支持我的开发工作，请大家关注一下我的公众号“看海的城堡”，在公众号页面下方点击相应标签即可获取开通方式。

![](https://pic1.zhimg.com/80/v2-378ba0b1c698dadf1c104659046809c8_720w.webp?source=d16d100b)

选择推荐渠道并非强制要求，但这样做一方面能确保您开通的账户类型与 KhQuant 兼容，另一方面也能对我正在进行的开发工作提供支持和肯定。再次感谢大家的关注！

## **六、 免责声明**

本文所有内容仅供学习和技术交流使用，不构成任何投资建议。所述策略及回测结果仅为历史数据模拟，不代表未来实际表现。投资者据此操作，风险自担。

## **相关文章**
