我是Mr.看海，我在尝试用信号处理的知识积累和思考方式做量化交易，应用深度学习和AI实现股票自动交易，目的是实现财务自由~
目前我正在开发基于miniQMT的量化交易系统——看海量化交易系统。
MiniQMT是一种轻量级的量化交易解决方案，使用MiniQMT可实现对交易过程的完全控制，借此优势实现最灵活的系统构建和最前沿的算法应用。然而，MiniQMT也有其挑战，它要求用户具有较强的编程能力，因为很多功能需要自己开发。
在之前的工作中，我介绍了"看海量化交易系统"的开发进展，即目前已经开发完成数据下载、数据清洗和可视化的工作。（如下图演示）
添加图片注释，不超过 140 字（可选）
近期主要做了两项工作：1.我根据大家的使用反馈，将当前系统进行了完善。感谢提供反馈的朋友们，系统在大家共同的建议下将会越来越完善和易用。2.同时我为数据下载模块添加了一个重要功能——数据补充。
1.关于数据补充
之前我们提到过，在大QMT中有"数据补充"功能，但是它下载下来的是二进制的dat数据，无法直接读取处理，所以我开发了数据下载模块，将数据保存为显式可阅读的csv数据文件，方便后续各种场景的使用。
使用"看海量化交易系统"可以将数据显式地保存下来
1.1 为何要添加"数据补充"功能
"数据补充"功能也自有其独特价值。在量化交易策略中，我们通常使用get_market_data函数来获取数据，它既可以获取实时行情，也可以获取历史数据。当我们运行策略时，如果历史数据已经提前通过数据补充功能下载完成，get_market_data函数就能直接从本地读取数据，这样可以大大提高策略程序的运行效率，避免了每次都需要从服务器重新获取历史数据的时间开销。
此外，数据补充功能还为不同类型的用户提供了更多选择。有些用户可能不需要将数据显式下载保存为文件，他们更习惯直接在策略中调用接口获取数据；而有些用户则希望对数据进行深入的分析和处理，需要将数据保存到本地。通过数据补充功能，这两类用户都能得到很好的支持：前者可以享受到更快的数据访问速度，后者则可以选择使用数据下载功能将数据保存为CSV文件进行后续处理。
1.2 为何不直接用大QMT的数据补充功能？
之所以不直接使用大QMT的数据补充功能，是因为其下载的dat数据存储路径与MiniQMT不同，这种路径的差异会导致在MiniQMT中无法直接使用大QMT补充的数据。我开发的数据补充功能不仅保持了增量更新的高效性，还确保了数据存储路径与MiniQMT保持一致，让数据能够被系统直接调用，为用户后续的策略开发和回测提供了极大便利。
1.3 看海系统中"数据补充"的使用方式
为了保持软件使用的连贯性，"数据补充"功能仅仅是增加了一个按钮。其余对于股票列表、周期类型、字段选择、日期范围等设置都与数据下载功能保持一致，用户无需重新学习新的操作方式。当用户选择好所需的股票和参数后，只需点击"补充数据"按钮，系统就会自动检查并补充缺失的数据部分。

添加图片注释，不超过 140 字（可选）
在补充过程中，系统会实时显示当前处理的股票代码和进度情况，让用户清楚地了解补充的进展。同时，为了确保数据的完整性和可靠性，系统会自动记录每个股票的补充状态，如果某只股票在补充过程中出现异常，系统会在日志中详细记录，方便用户后续进行针对性的处理。
值得一提的是，看海量化交易系统的数据下载模块支持tick级别的数据获取。这意味着用户可以获取到最细粒度的交易数据，包括每一笔交易的价格、成交量、买卖盘口等详细信息。同时，系统还支持1分钟、5分钟和日线等多个周期的数据补充，可以满足不同层面的分析需求。无论是高频交易策略的研究，还是中长期趋势的分析，都能找到合适的数据支持。
2.其他更新

## 功能优化

在看海系统公开使用的这段时间以来，很多朋友提出了宝贵的修改建议。为了提升用户体验，我对系统进行了一系列优化和完善。

## 操作便利性提升

首先是对操作便利性的提升。系统现在会保存用户的操作路径，当下次打开软件时，会默认打开上次选择的路径，避免用户重复选择。同时，在清洗数据时，系统会明确提示"清洗后数据将覆盖原始数据"，防止用户误操作导致数据丢失。

在数据选择方面，我们优化了周期切换的逻辑。现在切换周期时不会重置已选择的字段，这样用户在不同周期间切换时可以保持字段选择的连续性，大大提高了操作效率。

## 数据处理能力增强

在数据处理方面，我们添加了复权方式的选择功能，用户可以根据自己的需求选择不同的复权方式来处理数据。同时，我们也优化了日期时间范围的验证功能，确保用户输入的时间范围是有效且合理的。

## 可视化体验优化

对于可视化界面，我们优化了数据悬停时的显示配色，使得数据展示更加清晰直观。这个改进源于用户反馈中提到的"数据查看不够清晰"的问题，现在用户可以更轻松地识别和分析数据。

## 数据支持范围扩展

在数据支持方面，系统新增了对ETF数据的下载功能。这是一个重要的扩展，因为ETF作为重要的投资品种，其数据对于很多量化策略的研究和实现都具有重要价值。

这些更新都是基于用户的实际使用反馈进行的改进，目的是让系统变得更加易用和实用。在未来，我们将继续倾听用户的声音，不断优化和完善系统功能。

