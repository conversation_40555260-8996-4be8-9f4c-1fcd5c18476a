# 第八章：洞察运行：右侧信息反馈区

右侧面板是您观察策略思想与市场现实碰撞过程的"黑匣子"。它忠实地记录了从软件启动到策略运行的每一个关键步骤。学会解读这里的日志信息，是您调试策略、理解行为、发现问题的核心技能。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/右侧面板.png" alt="右侧面板" width="50%" />
</p>

---

## 8.1 日志系统的核心构成

本软件的日志系统设计围绕两大核心组件展开，两者相辅相成，共同构成了完整的日志监控体系：

* **后台文件日志 (`app.log`)**：作为最详尽的记录载体，它捕捉软件运行期间的各类事件，为深度问题排查提供依据。
* **前台界面日志**：一个可视化的窗口，实时展示关键的运行状态、策略输出和交易活动，提供直观的监控体验。

### 8.1.1 后台文件日志：完整记录运行轨迹

软件在启动时，会自动在程序根目录下的文件夹内创建或覆盖一个名为 `app.log` 的文本文件。这个文件是系统运行状态最详尽的记录载体。它会捕捉软件从启动到退出的整个生命周期内的各类事件，包括但不限于配置文件的加载与保存、策略的启动指令、运行状态变化以及最终的停止信号。

更重要的是，当软件遭遇预期之外的异常时，详细的错误信息和程序执行堆栈（Traceback）会被完整地记录下来，为问题排查提供了关键线索。

> 💡 **日志级别与覆盖规则**
>
> 为了确保信息的全面性，文件日志默认记录 `DEBUG` 及以上所有级别的信息。需要注意的是，该日志文件在**每次软件重新启动时会被重写**，因此只保留当前运行周期的日志。若需长期存档，建议在关闭软件前手动备份该文件。

### 8.1.2 前台界面日志：实时状态的可视化窗口

为了让用户能够直观、实时地掌握软件的运行状态和策略执行情况，后台记录的关键日志信息会被同步呈现在软件主界面的右侧"系统日志"面板中。这个面板是用户观察软件内部活动的主要窗口，它会实时显示核心的运行状态更新、策略通过 `print` 或日志模块输出的信息、发生的交易行为以及重要的系统警告与错误提示。

---

## 8.2 界面日志的特性与交互功能

用户界面的日志显示区域并非后台日志的简单复刻，它融入了多项旨在提升用户体验和信息获取效率的设计。

### 8.2.1 日志级别与颜色区分

为了帮助用户在信息流中快速抓住重点，不同重要程度的日志被赋予了不同的颜色标识，使得用户能够迅速定位到关键的错误警报或重点关注的交易活动。

* `[DEBUG]` (调试): **浅紫色**，主要用于开发者调试，信息较为冗余。
* `[INFO]` (信息): **白色**，常规运行信息，如启动/停止、配置更改等。
* `[WARNING]` (警告): **橙色**，潜在问题或需要用户注意的情况。
* `[ERROR]` (错误): **红色**，发生了错误，可能影响正常运行。
* `[TRADE]` (交易): **蓝色**，专门用于记录交易相关的委托和成交信息。

### 8.2.2 日志过滤与管理工具

随着软件运行时间的增长，日志信息可能会变得非常庞杂。界面日志区域下方提供了一组复选框和管理按钮，允许用户聚焦核心信息并进行便捷操作。

<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/日志按钮.png" />
</p>

* **日志过滤**: 通过勾选 `DEBUG`, `INFO`, `WARNING`, `ERROR`, `TRADE` 等复选框，可以动态过滤日志。每次勾选状态的改变都会即时生效，让关键信息一目了然。
* **清空日志**: 点击可以迅速清除当前界面上显示的所有日志内容（此操作不影响后台的 `app.log` 文件）。
* **保存日志**: 点击后，系统会将当前界面显示的（已应用过滤规则的）日志内容导出到一个用户指定的文本文件中，方便存档或分享。
* **测试日志**: 点击会生成几条不同级别的模拟日志，用于快速验证日志系统的显示和颜色功能是否正常。
* **打开回测指标**: 这是通往策略绩效复盘的**核心入口**。此按钮在平时是灰色不可用状态，**只有在一次回测成功运行结束后**才会被激活。点击后将打开详细的回测报告窗口。

### 8.2.3 特殊日志的差异化处理

系统对特定类型的日志进行了特殊处理以优化信息呈现。例如，对于回测过程中的进度反馈（如 "回测进度: 55.3%"），这类信息虽然也会在后台日志中记录，但在界面上并不会直接逐条打印，而是被用来驱动主窗口底部状态栏中的**进度条**进行实时更新。这种处理方式避免了大量进度信息刷屏，同时提供了更为直观、集中的进度展示。

---

## 8.3 在策略中使用日志

为了方便调试和监控策略的内部状态，您可以在策略代码中直接调用日志输出功能，将信息发送到看海量化交易系统的日志系统。

您可以根据需要输出不同级别的日志信息，这些级别与界面上显示的颜色直接对应：

* **普通信息 (INFO)**: 使用 `self.log("进入长仓条件判断", "INFO")` 或类似语句输出常规的流程信息或变量状态。这对应界面上的 **白色** 文本。
* **调试信息 (DEBUG)**: 如果需要输出更详细的、仅在调试时关心的变量值，可以使用 `self.log("当前ATR值", "DEBUG")`。这对应界面上的 **浅紫色** 文本。
* **警告信息 (WARNING)**: 当策略遇到一些非致命但需要注意的情况时，比如某个数据获取失败但有备用方案，可以使用 `self.log("无法获取最新行情，使用上一周期数据代替", "WARNING")`。这对应界面上的 **橙色** 文本。
* **错误信息 (ERROR)**: 当策略发生严重错误，可能导致后续逻辑无法正常执行时，应使用 `self.log("计算指标时出现除零错误", "ERROR")`。这对应界面上最醒目的 **红色** 文本。
* **交易信息 (TRADE)**: 虽然系统会自动记录委托和成交回报，但如果您想在策略逻辑中额外标记关键的交易决策点，也可以使用 `self.log(f"信号触发，准备买入", "TRADE")`。这对应界面上的 **蓝色** 文本。

> **如何输出醒目的内容？**
>
> 如果您希望某条日志信息在界面上特别突出，最直接的方式是使用 `WARNING` 或 `ERROR` 级别。`ERROR` 级别（红色）最为醒目，通常用于指示发生了必须处理的问题。`WARNING` 级别（橙色）也比较突出，适合用于提示潜在风险或需要关注的状态。请根据信息的重要性和紧急程度，审慎选择合适的级别进行输出。

在下一章，我们将深入那个最激动人心的部分——详细解读回测报告，真正开始对您的策略进行定量评估。

