### 看海量化回测平台介绍 - 视频分镜脚本 (逐句版)

**【开场】**

**旁白：** 普通散户如何零门槛研究量化？
**画面：** 漆黑背景中，问题"普通散户如何零门槛研究量化？"以打字机效果逐字出现，字体简洁有力，背景音乐起，带有悬念和科技感。

**旁白：** 核心在于两点：一是稳定、免费的数据和交易接口，二就是一个真正属于我们自己、开源且强大的回测系统。
**画面：** 画面中央出现"核心在于两点："，随后，左侧滑入代表"数据/接口"的云端和API符号，右侧滑入代表"回测系统"的图表和齿轮符号，下方分别显示对应的文字说明。

---

**【引入正题】**

**旁白：** 大家好，我是Mr.看海。
**画面：** 屏幕中央出现简洁的Logo动画："看海量化"，下方是 "Mr.看海 出品"。

**旁白：** 上一期视频，我们介绍了使用miniQMT作为免费数据和交易接口的方式，并基于此构建了底层回测框架khQuant。
**画面：** 【上期回顾】快速剪辑上一期视频的几个核心画面：khQuant的架构图、MiniQMT的Logo、滚动的代码片段。画面叠加标题："上期回顾：底层框架 khQuant"。

**旁白：** 今天，我将介绍我基于自研khQuant搭建的看海量化回测平台，看一看它是如何将复杂的量化回测过程，变得简单直观、触手可及的。
**画面：** 画面平滑过渡到"看海量化回测平台"的软件主界面，窗口由小变大，充满整个屏幕。软件名称的艺术字标题浮现在画面上方。

---

**【Part 1: 什么是回测？】**

**旁白：** 那回测到底是什么呢？
**画面：** 画面上出现一个大大的问号，旁边配有"回测？"的字样。

**旁白：** 简单来说，就是让你的交易策略，在过去的历史数据中完整地跑一遍，看看它在当时的市场环境下，究竟会有怎样的表现。
**画面：** 动画演示：一个代表"交易想法"的灯泡图标出现，一条箭头指向一个代表"历史数据"的K线图数据库，最终箭头指向一份带有绿色增长曲线的"绩效报告"。

**旁白：** 回测的关键作用，就是在投入真金白银之前，对策略进行一次严格的压力测试。
**画面：** 动画演示：一个代表"真金白银"的钱袋图标出现，一个坚固的"盾牌"图标从天而降，挡在钱袋前面，盾牌上出现"压力测试"字样。

**旁白：** 它能帮我们量化收益风险，发现策略优劣，是迭代优化的必经之路。
**画面：** "量化收益风险"、"发现策略优劣"、"迭代优化"等关键词从盾牌两侧依次弹出，并高亮显示。

---

**【Part 2: 系统四大核心模块】**

**旁白：** 整个系统，主要由四个精心设计的核心部分组成：
**画面：** 动态信息图开场：屏幕中央是"看海量化回测平台"的Logo，准备向四周伸出分支。

**旁白：** 包括进行所有操作的主界面，
**画面：** Logo伸出一个分支，点亮"主界面"图标（显示器）。

**旁白：** 负责准备历史数据的数据工具，
**画面：** Logo伸出第二个分支，点亮"数据工具"图标（数据库）。

**旁白：** 用于分析结果的回测报告，
**画面：** Logo伸出第三个分支，点亮"回测报告"图标（图表）。

**旁白：** 以及进行全局配置的设置中心。
**画面：** Logo伸出第四个分支，点亮"设置中心"图标（齿轮）。

---

**【模块一：主界面】**

**旁白：** 首先是主界面，我们所有操作的指挥中心。
**画面：** 屏幕录制，特写主界面。鼠标指针依次扫过并高亮显示【回测参数设置区】、【股票池列表】、【触发方式下拉菜单】、【盘前/盘后任务勾选框】。

**旁白：** 它的设计高度集成且流程化，你可以在这里完成从回测参数、股票池到策略触发方式的全部配置，可以定义盘前盘后的触发任务。
**画面：** 动画：左侧屏幕出现一团乱麻般的线条，标注着"脚本混乱"、"参数不明"、"结果难复现"。随着旁白，这团乱麻被吸入右侧一个清晰的".kh"文件图标中，线条变得井然有序。

**旁白：** 这背后是我"以配置为中心"的设计思路。复杂的量化研究常会陷入脚本混乱、参数不明、结果无法复现的困境。为此，我将每一次回测都定义为一个严肃的工程，其核心便是 .kh 配置文件。它将策略、参数、触发规则、本金等所有回测要素固化于一处，确保每一次实验都有序、严谨且精确可复现。
**画面：** 屏幕录制，打开一个实际的".kh"配置文件。用高光效果突出显示里面的关键配置项，如`strategy = MACD.py`, `params = (12, 26, 9)`, `trigger = on_bar`, `capital = 1000000`。

---

**【模块二：数据工具】**

**旁白：** 第二个是数据工具，我们回测所需数据的来源。
**画面：** 屏幕录制：点击主界面的"数据工具"按钮，弹出数据工具窗口。

**旁白：** 它的特点是双重机制，兼顾效率与灵活。
**画面：** 鼠标点击"数据补充"模式。动画演示：数据从云端流入MiniQMT的Logo，然后注入到软件后台，旁边一个时钟的指针飞速旋转，象征"高效"。屏幕底部文字："模式一：数据补充（为内部回测提速）"。

**旁白：** 我把它设计成了"数据补充"和"数据下载"两种模式。
**画面：** 鼠标点击"数据下载"模式。动画演示：数据从云端下载到一个文件夹图标中，并生成一个CSV文件图标。该图标随后被一个外部程序（如Excel或Jupyter Notebook）的Logo打开。屏幕底部文字："模式二：数据下载（为外部研究提供灵活性）"。

**旁白：** 数据补充，是专门为本平台内部回测服务的，它将数据写入MiniQMT的本地数据库，追求的是极致的回测验证效率。
**画面：** 左侧动画：数据从云端流入MiniQMT的Logo，然后注入到软件后台，旁边一个时钟的指针飞速旋转，象征"高效"。

**旁白：** 而数据下载，则是将数据保存为通用的CSV文件。
**画面：** 右侧动画：数据从云端下载到一个文件夹图标中，并生成一个CSV文件图标。

**旁白：** 这种方式，为你进行跨平台研究，或者开展需要外部模型训练的策略，提供了最大的灵活性。
**画面：** 右侧动画：CSV图标随后被一个外部程序（如Excel或Jupyter Notebook）的Logo打开并展示。

---

**【模块三 & 四：回测报告 & 设置】**

**旁白：** 第三个是回测报告，我们策略表现的量化总结。
**画面：** 屏幕录制：展示一份已经生成的回测报告网页。镜头平滑地从上到下扫过，重点展示资金曲线图和密集的绩效指标表格。鼠标在图表上移动，交互式地显示数据点详情。

**旁白：** 它的特点是信息全面且高度交互。
**画面：** 它不仅提供超过二十项专业绩效指标，更把资金曲线、回撤、每日盈亏和买卖点都整合到一张图里。你可以深入探索图表，从宏观到微观，全方位地审视策略的表现。

**旁白：** 最后，是设置界面，负责系统的全局配置。
**画面：** 快速切到软件的"设置"界面。用高光标出"券商选择"、"数据源路径"等全局配置项，画面简洁明了。

**旁白：** 它的特点是全局掌控。这里管理着那些不常变动但至关重要的参数，比如连接MiniQMT的关键路径，确保了整个系统的稳定和专业。
**画面：** 用高光依次标出"券商选择"、"数据源路径"、"MiniQMT路径"等全局配置项。

---

**【Part 3: 实战演练】**

**旁白：** 好了，对整体架构有了了解之后，我们直接进入实战演练，通过一个MACD策略，完整地走一遍回测流程。
**画面：** 过渡转场，画面中央出现大标题："实战演练：MACD策略回测"。背景音乐节奏加快。

**旁白：** 点击加载配置按钮，选择我们准备好的名为demoMACD的kh工程文件。
**画面：** 屏幕录制：鼠标点击"加载配置"按钮，在文件浏览器中选中`demoMACD.kh`并打开。

**旁白：** 大家看，一瞬间，界面上所有的参数，从策略文件、回测周期到股票池，再到触发时机、初始资金，都自动填充好了。
**画面：** 画面特写：随着文件加载，主界面的【策略文件】、【回测周期】、【股票池】、【初始资金】等输入框被瞬间自动填充。用动态高光依次突出这些被填充的区域。

**旁白：** 这种kh工程文件，就是我们整个工作台的一键存档，方便随时调用和分享。
**画面：** 动画：一个`.kh`文件图标飞入，然后分裂出多个副本，飞向不同的方向，象征"分享"和"复用"。

**旁白：** 有了策略，还需要准备历史数据。
**画面：** 屏幕录制：点击"数据工具"按钮，勾选"1分钟"和"1日"K线，点击"补充数据"，展示进度条快速走完的过程，最后弹出"数据准备完成"的提示。

**旁白：** 回测时，我们用的是数据补充功能...
**画面：** 屏幕录制：鼠标点击并选中"数据补充"选项卡。

**旁白：** 我们的MACD策略，既需要分钟线来判断买卖点，也需要日线来计算长期参数。
**画面：** 屏幕录制：在数据工具中，分别展示"分钟"和"日"两种K线周期选项。

**旁白：** 所以，我们分别勾选1分钟和1日周期，设置好股票和时间，点击补充数据。
**画面：** 屏幕录制：鼠标依次勾选"1分钟"和"1日"周期，然后点击"补充数据"按钮。

**旁白：** 等进度条走完，数据就准备好了。
**画面：** 屏幕录制：展示数据下载的进度条从0快速走到100%，最后弹出"数据准备完成"的提示。

**旁白：** 这个过程，只需要做一次...大大提升效率。
**画面：** 动画：一个日历图标出现，只有第一天是高亮的，后续的日子都是灰色的，象征"一次准备，多次使用"。

**旁白：** 好，万事俱备。现在，让我们按下开始运行按钮。
**画面：** 屏幕录制：鼠标光标移动到"开始运行"按钮并有力地点击下去。画面一分为二，左侧是主界面，右侧是日志区域，日志飞速滚动。底部进度条从0%增长到100%。

**旁白：** 看右边的日志区，系统开始工作了...所有细节都清清楚楚。
**画面：** 画面右侧的日志区域特写，日志条目飞速向上滚动。

**旁白：** 同时，看底部的进度条，它告诉我们回测完成了百分之几，整个过程，尽在掌握。
**画面：** 画面底部的进度条特写，百分比数字持续增加。

**旁白：** 进度条走到百分之百，回测结束，一份详尽的报告会自动弹出。
**画面：** 进度条达到100%，浏览器窗口自动弹出，全屏展示生成的回测报告。

**旁白：** 这就是对我们策略表现的最终检验。
**画面：** 报告的标题"策略回测报告"被放大并加粗特写。

---

**【报告分析 & 总结】**

**旁白：** 这份报告，浓缩了策略的全部信息。
**画面：** 镜头快速拉远，展示整个回测报告的全貌。

**旁白：** 最上方，是核心的资金曲线...策略有没有跑赢大盘，一目了然。
**画面：** 屏幕录制：特写回测报告的资金曲线图，用醒目的箭头分别标出"策略曲线"和"基准指数"。

**旁白：** 鼠标悬浮在上面，还能看到每一天的详细收益和回撤数据。
**画面：** 鼠标在图表上平滑移动，交互式地显示不同日期的数据点详情。

**旁白：** 下面是更详细的分析。
**画面：** 镜头从资金曲线图向下平滑滚动。

**旁白：** 在基本信息里...都是评价一个策略好坏的专业量尺。
**画面：** 依次高亮【年化收益】、【最大回撤】、【夏普比率】等关键绩效指标。

**旁白：** 交易记录...检查策略的行为是否符合预期。
**画面：** 高亮展示【交易记录】列表区域。

**旁白：** 日收益表...记录了每天的资产变化。
**画面：** 高亮展示【日收益表】区域。

**旁白：** 还有绩效分析...能帮助我们深挖策略的内在性格。
**画面：** 特写展示报告中的【月度热力图】，用颜色深浅表现不同月份的收益情况。

**旁白：** 从加载配置、到准备数据，再到执行回测、分析报告，整个流程下来，大家可以看到，我努力的方向，就是把复杂的东西留在幕后，把简洁的操作呈现在台前。
**画面：** 快速回顾蒙太奇：加载配置 -> 准备数据 -> 运行回测 -> 分析报告。四个核心操作界面依次闪现，最后定格在清爽的主界面上，并叠加文字："把复杂留在幕后，把简洁呈现在台前"。

**旁白：** 你不需要成为一个编程高手，通过这样一套图形化的工具，就可以把自己的交易想法，快速地进行量化和系统性的验证。
**画面：** 动画：一个普通的人物剪影图标，通过点击软件界面，最终变身为一个专业的交易员剪影。

---

**【结尾 & 下期预告】**

**旁白：** 当然，一个好的回测系统，只是量化交易的第一步。
**画面：** 画面从软件UI模糊过渡到一个深色背景，文字浮现："好的回测系统 = 量化交易的第一步"。

**旁白：** 如何写出一个真正有效的策略，才是我们最终的追求。
**画面：** "第一步"文字淡出，"最终追求"文字浮现并放大。

**旁白：** 在下一期视频中，我将带领大家深入策略文件的内部，详细讲解策略编写的规范和API，真正开始我们自己的创造之旅。
**画面：** 【下期预告】转场。屏幕上出现一个策略代码文件（`strategy.py`）的局部特写，镜头聚焦在`def on_bar(context):`函数上，并打上"下期预告：深入策略编写核心"的标题。

**旁白：** 我开发的这套看海量化交易系统将免费开源。
**画面：** 最后的结束卡。画面中央是"看海量化"的Logo，下方是文字："本系统将免费开源，现已进入内测阶段"。屏幕两侧缓缓浮现"点赞"、"关注"、"投币"的图标。背景音乐推向高潮并收尾。

**旁白：** 系统已进入内测，感兴趣的朋友请一键三连，点个关注，第一时间获取项目进展。
**画面：** 屏幕两侧缓缓浮现"点赞"、"关注"、"投币"的动态图标。

**旁白：** 你的支持是我最大的动力！ 