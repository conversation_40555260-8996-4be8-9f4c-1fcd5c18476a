> 我是Mr.看海，我正在开发基于miniQMT的"看海量化交易系统"
> 我的最终目标是实现能够支持AI深度学习等先进策略的自由的量化交易系统。

# 当前开发进度

2024.10.11 完成历史数据下载模块初步版本。

2024.10.12 GUI界面更新了打开QMT终端和指示灯功能 数据可视化界面解决了部分bug

2024.11.08 1.将数据下载和数据清洗模块合并为GUI.py文件 2.加入了报错日志保存的功能 3.读取股票列表的函数文件，加入了支持各种编码模式。

2024.11.15 1.基本完成数据下载和数据清洗模块 2.完成软件界面可根据显示器分辨率自动调整大小，并保持界面居中

2024.11.16 完善重复数据清理的逻辑，需进行时间戳与数据双重验证，以判定是否为重复数据。

2024.11.17 1.添加了数据可视化模块 2.在平台主界面新增了工具栏，可通过工具栏打开可视化模块。3.重新整理了data文件夹，使其更具结构化 4.修正了1d数据下载可能存在的bug 5.修正底层下载数据的函数，对于下载1d数据，不再下载time列

2024.11.18 美化了界面，优化了软件界面布局，丰富了文件信息内容（增加了市场分部、周期类型、日期范围），图例解析为中文显示，日内数据休市时间使用灰色区域显示。

2024.11.20 在可视化模块中加入了重载文件夹数据功能。

2024.11.22 增加了设置界面，添加了icon图标。

2024.11.26 添加splash加载界面，显示程序加载进度。

2024.11.28 实现程序打包为exe安装包，并支持中文安装界面。

2024.11.29 发布第一个稳定版本V1.0.0

2024.12.01 发布V1.1.6，完善日志管理。

2024.12.02 发布V1.1.8，优化激活管理和界面日志记录，修复激活提示重复显示问题。改进状态指示器逻辑，避免重复记录相同状态。

2024.12.02 发布V1.2.0，更新股票列表获取和保存功能，添加成分股支持；优化日志记录，增强错误处理机制；修复界面关闭时的线程管理问题；改进设置对话框，添加股票列表管理功能。此提交提升了用户体验和系统稳定性。

2024.12.02 发布V1.2.1，内置了对沪深A股、深证A股、上证A股、创业板、科创板、中证500成分股、沪深300成分股、上证50成分股的股票列表，以及常用指数的列表。设置界面新增了对上述股票列表的更新功能。

2024.12.05 发布稳定版本V1.2.3，修复了多个界面和功能问题，提升了用户体验和系统稳定性。

2024.12.10 发布V1.2.5新增风险提示和文章管理功能，更新多个数据文件以修正股票名称。此提交提升了系统稳定性和用户体验。

2024.12.18 发布V1.2.6，去掉了登录的激活限制。

2024.12.30 发布V1.2.9 修改如下：1.完全去除激活验证。2.保存操作的存储路径，下次打开软件时，默认打开上次选择的路径。3.清洗数据时提醒"清洗后数据将覆盖原始数据" 4.优化可视化界面的数据悬停显示配色 5.切换周期时，不重置字段。

2025.01.02 发布V1.3.1 修改如下：1.添加复权方式选择。2.优化日期时间范围验证功能。3.支持ETF数据下载。

2025.01.08 优化量化交易系统文档，更新服务器页面样式，添加打赏功能及设置，增强用户体验。

2025.01.13 更新量化交易框架，增加图形界面，实现图形界面中的回测数据设置、股票池设置，优化配置管理和交易逻辑。

2025.01.17 更新量化交易系统，新增股票名称查询功能，优化界面布局，添加状态栏和日志处理器，增强用户交互体验。

2025.01.20 添加深色主题和交易成本设置功能，实现深色模式支持，新增基准合约和交易成本设置，提升用户体验和界面美观性。

2025.01.26 回测模式可以运行，重构滑点计算和交易成本管理逻辑。

2025.01.31 重构日志系统，增强交易回调和错误处理，实现运行日志+交易日志模式。

2025.02.01 重构策略运行机制，引入多线程支持，优化交易回调和成本处理，增强交易信息精度和日志记录。

2025.02.16 优化基准指数数据处理和回测数据管理，增强回测结果记录和保存机制。

2025.02.17 添加回测结果窗口显示功能，修复策略线程启动方法并优化回测结果目录管理。

2025.02.20 优化回测结果窗口的界面和交互体验，增强回测结果窗口交互性和可视化细节。

2025.03.13 增强交易框架的时间处理功能，完善四类触发形式，增强回测框架的触发机制和数据处理。

2025.03.22 新增实盘数据获取模块，支持全推行情和单股订阅选择，优化界面布局，移除自定义时间点验证功能。

2025.03.23 初步撰写了北京炒家的策略，完善数据下载功能-解决了程序阻塞问题、可以停止下载数据。

2025.03.26 将设置窗口独立设置成了一个文件，优化下载按钮文本，增强交易回调功能，提升系统稳定性和用户体验。

2025.03.30 修复了基准收益显示不全的问题，增加了MACD策略，优化基准数据处理逻辑。

2025.04.02 将持仓和账户信息也作为策略的导入信息，修复滑点设置功能，优化回测结果窗口。

2025.04.06 重命名khQuTools模块为khQTTools，解决手续费中没包含印花税的问题，优化手续费计算逻辑。

2025.04.08 在回测结果窗口中添加无风险收益率设置功能，优化年化收益率和夏普比率计算逻辑。

2025.04.10 优化回测结果窗口的界面布局，在回测结果窗口中重构图表布局，提升图表的可读性和交互性。

2025.04.13 在KhQuantGUI中添加进度条功能，在GUI中添加在线教程链接功能，优化帮助对话框的实现。

2025.04.18 大幅提升代码运行效率，在KhQuantGUI中添加配置文件加载和保存功能，优化khQTTools.py中的最大买入量计算逻辑。

2025.04.23 修复下单函数计算最大下单量可能存在的bug，更新KhQuant工程化管理文档。

2025.05.15 修复了调用GUI的方式，优化回测结果窗口中的成交价格显示精度，禁用更新管理器及相关功能。

2025.05.27 优化策略初始化和历史数据加载逻辑，简化代码结构，提升可维护性和性能。

2025.05.29 解决第一次回测不显示进度条的问题，删除多个不再使用的策略文件和文档，优化KhQuantGUI中的进度条和状态标签逻辑。

2025.05.31 更新周期类型下拉框，增强补充数据线程的统计功能，优化GUI界面布局。

2025.06.11 优化日志系统配置，添加日志刷新功能，更新版本号至1.9.1，添加holidays和convertdate相关模块支持假期计算功能。

2025.06.11 添加日志刷新功能，设置定时器每5秒刷新一次日志缓冲区，确保日志及时写入文件。同时优化关闭事件处理，确保在程序退出时强制刷新日志并记录关闭时间，提升日志管理的可靠性。

2025.06.11 更新版本号至1.9.9，固定运行模式为回测，移除相关关选择和信号连接逻辑，简化账户信息设置，调整构建日期，确保应用功能一致性和用户体验提升。

2025.06.11 修复打包环境下的路径问题，确保日志目录正确创建并添加异常处理。同时记录程序运行环境和日志文件路径，提升日志管理的可靠性。

2025.06.12 更新版本号至1.9.10，优化回测模式设置，固定运行模式为回测，移除模拟和实盘选项，简化账户信息设置，提升用户体验和界面一致性。

2025.06.12 发布V1.9.11，优化回测模式，固定运行模式为回测，移除不必要的信号连接和界面元素，简化账户信息设置，提升用户体验和界面一致性。同时更新回测结果中的实际开始时间。

2025.06.16 更新版本号至1.9.12，调整构建日期为2025-06-16，确保版本信息与实际一致。

2025.06.16 确保仅在源码模式下有效，优化代码结构，简化图标路径和日志目录获取设置，同时更新自选清单文件路径获取方式，提升代码可读性和维护性。

2025.06.18 更新版本号至2.0.1，优化下载统计功能，添加下载类型统计和趋势分析，提升用户体验和数据可视化。同时，确保jQuery加载的可靠性，添加备用加载方案。

2025.06.22 发布V2.0.4，优化Windows系统下的深色模式和标题栏颜色设置，增强Windows 10与Windows 11的兼容性。同时更新股票列表管理逻辑，移除过时的文件路径，直接从配置中读取和更新股票列表。

2025.06.22 更新版本号至2.0.7，重构股票列表管理逻辑，移除生成单独CSV文件的功能，改为直接保存至配置文件中。更新相关方法以支持从配置中加载股票列表，增强兼容性和用户体验。

2025.06.22 系统清理和优化：删除多个测试文件，包括清源日志记录、打包日志缓存、股票列表配置和Windows标题栏设置的测试脚本，以简化代码结构和提升可维护性。

2025.06.22 优化文件路径获取逻辑，确保在打包和开发环境下正确加载数据。

# 开发文章

[【深度学习量化交易1】一个金融小白尝试量化交易的设想、畅享和遐想](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484609&idx=1&sn=7ec0b44a90e3a213332fa7e53ed514a8&scene=21#wechat_redirect)

[【深度学习量化交易2】财务自由第一步，三个多月的尝试，找到了最合适我的量化交易路径](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484748&idx=1&sn=df5365c8d7ba1890ccb69984f9063b07&scene=21#wechat_redirect)

[【深度学习量化交易3】为了轻松免费地下载股票历史数据，我开发完成了可视化的数据下载模块](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484773&idx=1&sn=2df74f66523a7e3be4d1f60bd5f03322&scene=21#wechat_redirect)

[【深度学习量化交易4】 量化交易历史数据清洗——为后续分析扫清障碍](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484790&idx=1&sn=8e9f08d57a4f4fc298b153cee699b09a&scene=21#wechat_redirect)

[【深度学习量化交易5】 量化交易历史数据可视化模块](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484808&idx=1&sn=b22fbc5b0349324832d10a5cb97fbfd6&scene=21#wechat_redirect)

[【深度学习量化交易6】优化改造基于miniQMT的量化交易软件，已开放下载~（已完成数据下载、数据清洗、可视化模块）](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484854&idx=1&sn=38c07a49e9826b2f82bb23baacb709bf&scene=21#wechat_redirect)

[【深度学习量化交易7】miniQMT快速上手教程案例集——使用xtQuant进行历史数据下载篇](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484875&idx=1&sn=53942f6ee87b43e8a8a362243421d340&scene=21#wechat_redirect)

[【深度学习量化交易8】miniQMT快速上手教程案例集——使用xtQuant进行获取实时行情数据篇](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484893&idx=1&sn=072481343fdcfbc478a95af42fdbc7f1&scene=21#wechat_redirect)

[【深度学习量化交易9】miniQMT快速上手教程案例集——使用xtQuant获取基本面数据篇](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484894&idx=1&sn=be58c5985965b4d960fcc0982c83a1a2&scene=21#wechat_redirect)

[【深度学习量化交易10】miniQMT快速上手教程案例集——使用xtQuant获取板块及成分股数据篇](https://mp.weixin.qq.com/s/WY7IcX_A1f41JzYLghjF8g?token=1530224493&lang=zh_CN)

[【深度学习量化交易11】miniQMT快速上手教程——使用XtQuant进行实盘交易篇（八千字超详细版本）](https://mp.weixin.qq.com/s/Y3l_aFNQm_iflknSqccHbg?token=1530224493&lang=zh_CN)

[【深度学习量化交易12】基于miniQMT的量化交易框架总体构建思路——回测、模拟、实盘通吃的系统架构](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484932&idx=1&sn=2fad21355d5840603c1b76176cc452e3&scene=21#wechat_redirect)

[【深度学习量化交易13】继续优化改造基于miniQMT的量化交易软件，增加补充数据功能，优化免费下载数据模块体验！](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484962&idx=1&sn=a1c7f637fc9fcae050847facd9334cb1&scene=21#wechat_redirect)

[【深度学习量化交易14】正式开源！看海量化交易系统——基于miniQMT的量化交易软件](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247484981&idx=1&sn=ce2136c1a4d6d9997e5208f8764af299&scene=21#wechat_redirect)

[【深度学习量化交易15】基于miniQMT的量化交易回测系统已基本构建完成！AI炒股的框架初步实现](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485007&idx=1&sn=fcb6909fe43878e2946dd64b6931cc03&scene=21#wechat_redirect)

[【深度学习量化交易16】韭菜进阶指南：A股交易成本全解析](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485018&idx=1&sn=9256196d393c74ebc2b54945c7ba8a3e&scene=21#wechat_redirect)

[【深度学习量化交易17】触发机制设置——基于miniQMT的量化交易回测系统开发实记](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485029&idx=1&sn=ec62136d7dbcb413c96b2a75151cd15e&scene=21#wechat_redirect)

[【深度学习量化交易18】盘前盘后回调机制设计与实现——基于miniQMT的量化交易回测系统开发实记](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485038&idx=1&sn=94221391bb1510e68aca514fbf4fe1d0&scene=21#wechat_redirect)

[【深度学习量化交易19】行情数据获取方式比测（1）——基于miniQMT的量化交易回测系统开发实记](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485052&idx=1&sn=c0e2b25e2e8b1ae3d364bd5f0fa2aaf2&scene=21#wechat_redirect)

[【深度学习量化交易20】量化交易策略评价指标全解析——基于miniQMT的量化交易回测系统开发实记](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485061&idx=1&sn=f1f5613446c77d0b17f5ecd9091ffa22&scene=21&token=169173272&lang=zh_CN#wechat_redirect)

[【深度学习量化交易21】行情数据获取方式比测（2）——基于miniQMT的量化交易回测系统开发实记](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485068&idx=1&sn=448e85217a470050f2afaa5d8603b211&scene=21#wechat_redirect)

[【AI量化第22篇】如何轻松看懂回测结果——基于miniQMT的量化交易回测系统开发实记](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485099&idx=1&sn=736e9f9f6eff79622b7ebceb2e3df74b&scene=21#wechat_redirect)

[【AI量化第23篇】数据下载/补充模块升级，并与回测系统正式集成——基于miniQMT的量化交易回测系统开发实记](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485121&idx=1&sn=ad84f08f4af7d345b2a94eabd50a0421&scene=21#wechat_redirect)

[【AI量化第24篇】KhQuant 策略框架深度解析：让策略开发回归本质——基于miniQMT的量化交易回测系统开发实记](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485134&idx=1&sn=99ba21880ef572621e7229cf5b97eebb&scene=21#wechat_redirect)

[【AI量化第25篇】看海量化交易系统日志系统详解](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485148&idx=1&sn=021f21b65f01a455a88b17db8a584786&scene=21#wechat_redirect)

[【AI量化第26篇】以配置为核心的工程化研究管理——基于miniQMT的量化交易回测系统开发实记](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485161&idx=1&sn=602408772d2c2ddee3d815372d90f209&scene=21#wechat_redirect)

[【AI量化第27篇】看海量化 vs. 同花顺 回测横评！以"双移动均线"策略为例的量化回测结果深度对比分析](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485193&idx=1&sn=393e2e34c6ff3b97ea77ce98c8aa1545&scene=21#wechat_redirect)

[大量化平台也有坑？khQuant回测横评第二弹，一次"排雷"实录【AI量化第28篇】](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485217&idx=1&sn=9e1fa258849cc1246b96cfec57bf02bc&scene=21#wechat_redirect)

[打板策略实战对比，khQuant回测横评第三弹【AI量化第29篇】](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485285&idx=1&sn=210915a7d6d32604ce1ac0894f1986d1&scene=21#wechat_redirect)

[AI炒股初衷，通向财务自由第一步](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485294&idx=1&sn=71cc47e2a20016abc0a2373af4130027&scene=21#wechat_redirect)

[回测效率提升500%！khQuant打板策略回测性能深度剖析——基于miniQMT的回测系统深度优化](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485310&idx=1&sn=9ccb881b5f16c0c74f8cedd61edf5c92&scene=21#wechat_redirect)

[6个月，136次更新！看海量化交易系统khQuant内测版本固化——基于miniQMT的量化回测系统【AI量化第31篇】](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485326&idx=1&sn=f2a589814d384b8725e882aa080fdef7&scene=21#wechat_redirect)

[你的下一把量化"瑞士军刀"？khQuant适用场景全解析【AI量化第32篇】](https://mp.weixin.qq.com/s?__biz=MzUzNDk1NjcyNg==&mid=2247485338&idx=1&sn=199c8f29591e96b323de7d69ae7972ce&scene=21#wechat_redirect)
