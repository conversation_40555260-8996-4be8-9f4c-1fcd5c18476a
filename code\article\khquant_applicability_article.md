> 我是Mr.看海，我在尝试用信号处理的知识积累和思考方式做量化交易，应用深度学习和AI实现股票自动交易，目的是实现财务自由\~
> 目前我正在开发基于miniQMT的量化交易系统——看海量化交易系统（KhQuant 是其核心框架）。

最近正在加紧写看海量化交易系统的使用手册，为的是软件开放使用后能让大家更容易上手开始研究策略。所以还请耐心等待\~

在写手册过程中，我想到一个需要向大家说明的一个问题：就是khQuant的能力边界在哪里，或者说**它擅长和不擅长的回测策略分别是哪些？**

![动图封面](https://picx.zhimg.com/v2-61dd84c000a716fcc49197cb62c801f8_720w.jpg?source=d16d100b)

## 一、KHQuant的核心优势：它能做什么？

在设计之初，KHQuant的核心思想就不是构建一个针对特定策略、高度定制化的固化框架。恰恰相反，我希望它成为一个**具备高度灵活性和开放性的通用型策略研发平台**。这种设计的初衷，是为了能普适于各种类型的中低频策略，极大地丰富用户在策略实现上的自由度。

当然，这种开放性也意味着，它对使用者的编程能力有一定要求。简单来说，KHQuant像一个高自由度的"乐高"平台，而不是一个按部就班的"模型套件"。它能兼容大部分你能想到的策略类型，因为它的架构足够开放。

下面列出的一些典型策略，目前我还没有全部编写详尽的实例（因为近期的主要精力仍聚焦于框架本身的开发和完善上），但后续我会逐步补充和讲解这些策略的实现案例。

![](https://picx.zhimg.com/80/v2-e941710832abf97f9ca41e2eb055d36d_720w.webp?source=d16d100b)

✅ **各类因子选股与轮动策略** 这是量化投资中最主流的应用之一。KHQuant为此提供了完整的解决方案：你可以轻松地整合财务、行情等多种数据源，利用内置函数或Pandas等强大工具进行计算，构建出自定义的alpha因子。系统支持你对全市场或特定股票池的股票进行多因子打分、排序，并根据排名自动生成调仓信号。无论是定期的月度轮动，还是基于动态阈值的非周期性调整，回测引擎都能精确模拟，帮助你检验因子有效性。

✅ **趋势跟踪与技术指标策略** 对于经典的趋势跟踪或摆动择时策略，KHQuant同样得心应手。需要说明的是，**目前系统本身并未内置集成具体的指标函数**，但这正是其开放性的体现。你可以非常方便地引入`TA-Lib`等成熟、强大的第三方技术指标库，计算如均线、MACD、RSI等上百种指标。在未来，我也会根据大家的需求，逐步在框架中补充一些最常用或前沿的指标，作为内置工具函数提供给大家。

✅ **事件驱动型策略** 市场的波动往往由特定事件催化。KHQuant的开放式设计允许你将外部事件数据（例如，财报公布日、股东增持公告、行业政策变动等）轻松整合进回测流程。你可以将这些事件信息整理成CSV等格式文件，在策略中读取并与行情数据对齐，从而构建出在关键事件发生前后进行布局或退出的交易模型，让你的策略能更好地捕捉信息驱动的行情。

✅ **机器学习与AI辅助策略（中低频）** 将AI技术融入量化交易是未来的大势所趋。KHQuant为你铺平了道路：你可以在系统之外，使用`Scikit-learn`、`PyTorch`等熟悉的框架训练你的预测模型。值得一提的是，**KHQuant的数据下载模块支持将所有数据显式地保存为通用的CSV格式**，这为模型训练提供了干净、易于读取的数据基础。训练完成后，你可以在策略中轻松加载训练好的模型，并调用它进行实时预测，将AI的分析结果无缝转化为交易信号。

![](https://picx.zhimg.com/80/v2-66e41938f633944ca96e5ec2677b6c69_720w.webp?source=d16d100b)

✅ **时间序列预测与高级AI模型** 除了经典的机器学习分类或回归模型，KHQuant同样支持更前沿的时间序列预测模型。你可以利用`ARIMA`、`Prophet`等统计模型，或者`LSTM`、`Transformer`等深度学习模型，对股价序列的未来走势进行预测。通过框架的灵活性，你可以自定义数据预处理、模型加载和预测的整个流程，将复杂的AI预测逻辑集成到你的交易策略中。

✅ **自定义指数构建与增强** 如果你不满足于跟踪市场上的主流指数，完全可以创建属于自己的"自定义指数"。在KHQuant中，你可以根据特定的投资理念（如高股息、硬科技）或行业偏好，筛选出一篮子股票，定义它们的权重分配方式（如等权、市值加权），从而构建一个自定义指数。然后，你可以回测这个指数的历史表现，甚至在此基础上进行Alpha增强，开发超越基准的策略。

✅ **量化知识学习与策略思想验证** 对于量化初学者或希望快速验证交易想法的朋友来说，KHQuant是一个理想的"实验室"。它提供了友好的图形用户界面，回测结果的可视化图表让你能直观地看到策略净值、持仓变化和关键绩效指标。策略逻辑则使用简洁的Python编写，大大降低了入门门槛。一个新想法，可能只需要几十行代码就能得到初步验证，极大地加速了从学习到实践的过程。

总而言之，只要策略的执行频率和对延迟的要求不是极端严苛，KHQuant 都能提供一个强大而便捷的本地化解决方案。

![](https://pic1.zhimg.com/80/v2-0caa029d63319324cdfd3ace5770b83e_720w.webp?source=d16d100b)

## 二、KHQuant的能力边界：它不适合做什么？

⚠️ **请注意**：任何工具都有其专注的领域和能力边界。"看海量化交易系统"也不例外。为了帮助大家做出更合理的预期和决策，我必须坦诚地告诉大家，在以下一些方面，KHQuant可能并非最佳选择。清晰地了解这些局限性，是合理运用一个工具的前提。

❌ **高频交易（HFT）与超低延迟策略** 这是KHQuant最明确的能力边界。原因有三：

* **数据层面**：MiniQMT提供的Tick数据通常是3秒快照，而非逐笔成交数据，这对于需要微秒级行情精度的典型高频策略来说，信息颗粒度不足。
* **执行层面**：系统本身（Python语言特性、多层架构）以及通过MiniQMT的交易链路，都无法满足高频交易所要求的亚毫秒级执行延迟。
* **技术栈**：专业的高频交易通常需要C++等高性能语言、FPGA硬件加速以及专用的低延迟交易接口和托管服务。

❌ **依赖极久远历史数据的细颗粒度回测**

* **MiniQMT数据限制**：券商版MiniQMT对历史数据的下载范围有限制。通常情况下，Tick数据可能只能获取最近一个月左右，1分钟和5分钟K线数据可能为最近一年左右，日线数据则相对完整。这意味着，如果策略需要回测数年前的分钟级甚至Tick级行情，系统可能无法直接提供足够的数据支撑。（有实力的可以开通研投版QMT，这样就有全部的数据了）

❌ **对多市场、多资产的复杂联动套利（超出MiniQMT范围）**

* 虽然可以通过Python的灵活性尝试对接其他数据源或接口，但KHQuant的核心优化和原生支持是围绕MiniQMT所能覆盖的A股市场（股票、ETF、部分期货期权等）。对于需要复杂跨市场（如全球市场）、跨资产类别（如外汇、加密货币）进行高精度、低延迟联动的套利策略，可能需要更专业的、针对性的平台，**或者基于本系统的开源代码自行改造，引入更多数据源**。

❌ **非Windows操作系统的原生流畅运行**

* **关于Linux**：虽然迅投官方的`xtquant`库支持在Linux上运行，但这仅限于收费的"研投版"账户。这与KHQuant"免费、开源"的初衷相悖，因此系统没有特别针对Linux进行适配和测试。
* **关于macOS**：目前完全不支持。
* 因此，KHQuant的主要开发、测试和稳定运行环境是**Windows**。在其他操作系统上尝试运行（如通过Wine）可能会遇到兼容性障碍。

## **三、下一步工作**

软件内测发布前的测试工作已经基本完成了，剩下的是一些收尾工作，包括使用手册撰写，软件封装等等。不会让大家等太久了，内测很快就要开始！

**内测计划说明**:

在完成上述更充分的验证测试之后，我计划启动"看海量化交易系统 (KhQuant)"的 Beta 内测阶段。

* **优先体验**: 为了感谢大家的支持，通过**我推荐的渠道开通MiniQMT账户的朋友**，在内测开始后将获得优先体验 Beta 版本软件，可以加入内部讨论群第一时间得到作者的问题解答，后续的一些策略也在内部讨论群小范围分享。
* **公开与开源**: 请暂时不方便通过推荐渠道开户的朋友放心，**内测结束后，软件将会公开发布，核心代码也计划进行开源**，届时所有人都可以使用和参与改进。

## **四、 关于开通 MiniQMT**

**什么是 MiniQMT？**

MiniQMT 是迅投（QMT）系统提供的一个程序化交易接口（API）。QMT 是目前国内许多券商采用的主流柜台系统之一，而 MiniQMT 允许用户通过编程方式（主要是 Python）连接到证券公司的交易服务器，进行行情获取、策略计算、下单交易等操作。它通常由支持 QMT 的券商**免费**提供给客户使用（可能需要满足一定的资产要求），以其稳定性和执行效率受到不少量化交易者的青睐。

**看海量化交易系统 (KhQuant) 与 MiniQMT**

我正在开发的"看海量化交易系统 (KhQuant)"正是**基于 MiniQMT 接口**进行构建的。这意味着，使用该软件需要一个 MiniQMT 账户作为底层支持。

**推荐开通渠道**

如果您还没有 MiniQMT 账户，并希望未来能够顺利使用"看海量化交易系统 (KhQuant)"进行策略回测，或者希望支持我的开发工作，请大家关注一下我的公众号“看海的城堡”，在公众号页面下方点击相应标签即可获取开通方式。

![](https://pica.zhimg.com/80/v2-378ba0b1c698dadf1c104659046809c8_720w.webp?source=d16d100b)

选择推荐渠道并非强制要求，但这样做一方面能确保您开通的账户类型与 KhQuant 兼容，另一方面也能对我正在进行的开发工作提供支持和肯定。再次感谢大家的关注！

## **五、 免责声明**

本文所有内容仅供学习和技术交流使用，不构成任何投资建议。所述策略及回测结果仅为历史数据模拟，不代表未来实际表现。投资者据此操作，风险自担。

## **相关文章**

[【深度学习量化交易1】一个金融小白尝试量化交易的设想、畅享和遐想](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484609%26idx%3D1%26sn%3D7ec0b44a90e3a213332fa7e53ed514a8%26scene%3D21%23wechat_redirect)

[【深度学习量化交易2】财务自由第一步，三个多月的尝试，找到了最合适我的量化交易路径](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484748%26idx%3D1%26sn%3Ddf5365c8d7ba1890ccb69984f9063b07%26scene%3D21%23wechat_redirect)

[【深度学习量化交易3】为了轻松免费地下载股票历史数据，我开发完成了可视化的数据下载模块](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484773%26idx%3D1%26sn%3D2df74f66523a7e3be4d1f60bd5f03322%26scene%3D21%23wechat_redirect)

[【深度学习量化交易4】 量化交易历史数据清洗——为后续分析扫清障碍](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484790%26idx%3D1%26sn%3D8e9f08d57a4f4fc298b153cee699b09a%26scene%3D21%23wechat_redirect)

[【深度学习量化交易5】 量化交易历史数据可视化模块](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484808%26idx%3D1%26sn%3Db22fbc5b0349324832d10a5cb97fbfd6%26scene%3D21%23wechat_redirect)

[【深度学习量化交易6】优化改造基于miniQMT的量化交易软件，已开放下载\~（已完成数据下载、数据清洗、可视化模块）](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484854%26idx%3D1%26sn%3D38c07a49e9826b2f82bb23baacb709bf%26scene%3D21%23wechat_redirect)

[【深度学习量化交易7】miniQMT快速上手教程案例集——使用xtQuant进行历史数据下载篇](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484875%26idx%3D1%26sn%3D53942f6ee87b43e8a8a362243421d340%26scene%3D21%23wechat_redirect)

[【深度学习量化交易8】miniQMT快速上手教程案例集——使用xtQuant进行获取实时行情数据篇](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484893%26idx%3D1%26sn%3D072481343fdcfbc478a95af42fdbc7f1%26scene%3D21%23wechat_redirect)

[【深度学习量化交易9】miniQMT快速上手教程案例集——使用xtQuant获取基本面数据篇](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484894%26idx%3D1%26sn%3Dbe58c5985965b4d960fcc0982c83a1a2%26scene%3D21%23wechat_redirect)

[【深度学习量化交易10】miniQMT快速上手教程案例集——使用xtQuant获取板块及成分股数据篇](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484904%26idx%3D1%26sn%3Db91f208db8b3ad84ddc7d03822e3b80b%26scene%3D21%23wechat_redirect)

[【深度学习量化交易11】miniQMT快速上手教程——使用XtQuant进行实盘交易篇（八千字超详细版本）](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484912%26idx%3D1%26sn%3D611350909bb70f422699dbcb55348479%26scene%3D21%23wechat_redirect)

[【深度学习量化交易12】基于miniQMT的量化交易框架总体构建思路——回测、模拟、实盘通吃的系统架构](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484932%26idx%3D1%26sn%3D2fad21355d5840603c1b76176cc452e3%26scene%3D21%23wechat_redirect)

[【深度学习量化交易13】继续优化改造基于miniQMT的量化交易软件，增加补充数据功能，优化免费下载数据模块体验！](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484962%26idx%3D1%26sn%3Da1c7f637fc9fcae050847facd9334cb1%26scene%3D21%23wechat_redirect)

[【深度学习量化交易14】正式开源！看海量化交易系统——基于miniQMT的量化交易软件](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247484981%26idx%3D1%26sn%3Dce2136c1a4d6d9997e5208f8764af299%26scene%3D21%23wechat_redirect)

[【深度学习量化交易15】基于miniQMT的量化交易回测系统已基本构建完成！AI炒股的框架初步实现](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485007%26idx%3D1%26sn%3Dfcb6909fe43878e2946dd64b6931cc03%26scene%3D21%23wechat_redirect)

[【深度学习量化交易16】韭菜进阶指南：A股交易成本全解析](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485018%26idx%3D1%26sn%3D9256196d393c74ebc2b54945c7ba8a3e%26scene%3D21%23wechat_redirect)

[【深度学习量化交易17】触发机制设置——基于miniQMT的量化交易回测系统开发实记](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485029%26idx%3D1%26sn%3Dec62136d7dbcb413c96b2a75151cd15e%26scene%3D21%23wechat_redirect)

[【深度学习量化交易18】盘前盘后回调机制设计与实现——基于miniQMT的量化交易回测系统开发实记](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485038%26idx%3D1%26sn%3D94221391bb1510e68aca514fbf4fe1d0%26scene%3D21%23wechat_redirect)

[【深度学习量化交易19】行情数据获取方式比测（1）——基于miniQMT的量化交易回测系统开发实记](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485052%26idx%3D1%26sn%3Dc0e2b25e2e8b1ae3d364bd5f0fa2aaf2%26scene%3D21%23wechat_redirect)

[【深度学习量化交易20】量化交易策略评价指标全解析——基于miniQMT的量化交易回测系统开发实记](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485061%26idx%3D1%26sn%3Df1f5613446c77d0b17f5ecd9091ffa22%26scene%3D21%26token%3D169173272%26lang%3Dzh_CN%23wechat_redirect)

[【深度学习量化交易21】行情数据获取方式比测（2）——基于miniQMT的量化交易回测系统开发实记](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485068%26idx%3D1%26sn%3D448e85217a470050f2afaa5d8603b211%26scene%3D21%23wechat_redirect)

[【AI量化第22篇】如何轻松看懂回测结果——基于miniQMT的量化交易回测系统开发实记](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485099%26idx%3D1%26sn%3D736e9f9f6eff79622b7ebceb2e3df74b%26scene%3D21%23wechat_redirect)

[【AI量化第23篇】数据下载/补充模块升级，并与回测系统正式集成——基于miniQMT的量化交易回测系统开发实记](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485121%26idx%3D1%26sn%3Dad84f08f4af7d345b2a94eabd50a0421%26scene%3D21%23wechat_redirect)

[【AI量化第24篇】KhQuant 策略框架深度解析：让策略开发回归本质——基于miniQMT的量化交易回测系统开发实记](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485134%26idx%3D1%26sn%3D99ba21880ef572621e7229cf5b97eebb%26scene%3D21%23wechat_redirect)

[【AI量化第25篇】看海量化交易系统日志系统详解](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485148%26idx%3D1%26sn%3D021f21b65f01a455a88b17db8a584786%26scene%3D21%23wechat_redirect)

[【AI量化第26篇】以配置为核心的工程化研究管理——基于miniQMT的量化交易回测系统开发实记](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485161%26idx%3D1%26sn%3D602408772d2c2ddee3d815372d90f209%26scene%3D21%23wechat_redirect)

[【AI量化第27篇】看海量化 vs. 同花顺 回测横评！以“双移动均线”策略为例的量化回测结果深度对比分析](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485193%26idx%3D1%26sn%3D393e2e34c6ff3b97ea77ce98c8aa1545%26scene%3D21%23wechat_redirect)

[大量化平台也有坑？khQuant回测横评第二弹，一次“排雷”实录【AI量化第28篇】](http://link.zhihu.com/?target=https%3A//mp.weixin.qq.com/s%3F__biz%3DMzUzNDk1NjcyNg%3D%3D%26mid%3D2247485217%26idx%3D1%26sn%3D9e1fa258849cc1246b96cfec57bf02bc%26scene%3D21%23wechat_redirect)

[打板策略实战对比，khQuant回测横评第三弹【AI量化第29篇】](https://zhuanlan.zhihu.com/p/1908781937616741159)

[回测效率提升500%！khQuant打板策略回测性能深度剖析——基于miniQMT的回测系统深度优化【AI量化第30篇】](https://zhuanlan.zhihu.com/p/1913011873504273543)

[6个月，136次更新！看海量化交易系统khQuant内测版本固化——基于miniQMT的量化回测系统](https://zhuanlan.zhihu.com/p/1913010366109192953)
