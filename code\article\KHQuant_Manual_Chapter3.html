<!DOCTYPE html>
<html>
<head>
<title>KHQuant_Manual_Chapter3.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<p>在正式开启您的量化之旅前，我们需要确保一切准备就绪。本章将指导您完成运行环境的配置、系统的安装，以及首次启动时的关键设置。这就像战前检查装备，虽显繁琐，却至关重要。</p>
<hr>
<h2 id="31-%E8%BF%90%E8%A1%8C%E7%8E%AF%E5%A2%83%E8%A6%81%E6%B1%82">3.1 运行环境要求</h2>
<p>为了让&quot;看海量化交易系统&quot;在您的电脑上流畅运行，请确保满足以下基本环境要求：</p>
<h3 id="311-%E7%A1%AC%E4%BB%B6%E4%B8%8E%E6%93%8D%E4%BD%9C%E7%B3%BB%E7%BB%9F">3.1.1 硬件与操作系统</h3>
<ul>
<li>
<p><strong>操作系统</strong>: <strong>Windows 10 或更高版本的64位系统</strong>。</p>
<blockquote>
<p>💡 <strong>开发者言</strong>：由于本系统的核心依赖——MiniQMT客户端主要运行于Windows平台，因此KHQuant的主要开发和测试环境也都在Windows上。虽然理论上可能通过虚拟机或兼容层在其他系统（如macOS, Linux）上运行，但这并非官方支持的路径，可能会遇到各种意想不到的兼容性问题。</p>
</blockquote>
</li>
<li>
<p><strong>硬件建议</strong>:</p>
<ul>
<li><strong>CPU</strong>: 建议使用现代多核处理器（如 Intel i5 或 AMD R5 及以上）。</li>
<li><strong>内存 (RAM)</strong>: 建议 <strong>16GB</strong> 或以上。请注意，内存的实际需求在很大程度上取决于您策略的复杂度和数据处理量。对于涉及大规模数据回测或复杂机器学习模型的策略，更大的内存将显著提升运行效率。我们将在后续的更新中，提供更精确的关于基础软件运行的最低硬件要求。</li>
<li><strong>硬盘</strong>: 建议使用 <strong>固态硬盘 (SSD)</strong>，以加快数据读取和软件启动速度。</li>
</ul>
</li>
</ul>
<h3 id="312-%E6%A0%B8%E5%BF%83%E4%BE%9D%E8%B5%96%E8%BD%AF%E4%BB%B6">3.1.2 核心依赖软件</h3>
<ul>
<li>
<p><strong>MiniQMT 客户端</strong>: <strong>这是本系统运行的绝对前提</strong>。
您必须首先从您开户的券商处下载并安装最新版的MiniQMT客户端，并确保您能够正常登录您的账户。本系统所有的数据获取和交易指令（若未来支持）都将通过此客户端完成。<a href="https://khsci.com/khQuant/miniqmt/">如尚未开通miniQMT，可以点击这里免费开通。</a></p>
<p>成功安装后，打开miniQMT客户端将会显示下边的界面：</p>
</li>
<li>
<p><strong>Microsoft Visual C++ Redistributable</strong>:
为了确保系统图形界面和部分依赖库的正常工作，您需要安装 <code>Microsoft Visual C++ 2015-2022 Redistributable (x64)</code>。</p>
<blockquote>
<p>🔗 <strong>官方下载链接</strong>: <a href="https://aka.ms/vs/17/release/vc_redist.x64.exe">https://aka.ms/vs/17/release/vc_redist.x64.exe</a></p>
<p><strong>如何检查？</strong> 通常，如果您能正常运行其他大型软件或游戏，这个组件很可能已经安装。如果不确定，直接下载并运行安装程序即可，它会自动判断是否需要安装或修复。</p>
</blockquote>
</li>
</ul>
<hr>
<h2 id="32-%E7%9C%8B%E6%B5%B7%E9%87%8F%E5%8C%96%E7%B3%BB%E7%BB%9F%E5%AE%89%E8%A3%85">3.2 看海量化系统安装</h2>
<p>我们提供了简单快捷的安装包，让您轻松完成所有部署工作。</p>
<p><strong>1.获取安装包</strong>: 从<a href="https://khsci.com/khQuant/">看海量化交易系统官网</a>下载最新的安装文件压缩包到本地（解压密码在压缩包名称里），右键解压缩后，将会得到一个exe文件，双击exe文件安装。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_08245868e6125c1dd285cba688adb2e0.jpg" />
</p>
<p>选择软件安装路径，一直点击下一步，直到安装成功。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2024/12/wp_editor_md_04e92a96668bf3cb9cb0a66bf77ae5e2.jpg" />
</p>
<p>安装完成后桌面上将会有软件的快捷方式，双击可以打开软件。</p>
<p><strong>2.注意事项</strong>:</p>
<ul>
<li>您可能会看到Windows安全提示，请选择&quot;更多信息&quot;，然后点击&quot;仍要运行&quot;。</li>
<li>建议保持默认的安装路径 (<code>C:\Program Files (x86)\khQuant</code>)，或选择您喜欢的其他路径。</li>
<li>在&quot;附加任务&quot;步骤，建议勾选&quot;创建桌面快捷方式&quot;，方便日后快速启动。</li>
<li>点击&quot;安装&quot;，稍等片刻即可完成。</li>
</ul>
<hr>
<h2 id="33-%E7%AC%AC%E4%B8%80%E6%AC%A1%E5%90%AF%E5%8A%A8%E5%85%B3%E9%94%AE%E8%AE%BE%E7%BD%AE%E8%A6%81%E7%82%B9">3.3 第一次启动：关键设置要点</h2>
<p>无论您通过哪种方式安装，首次启动软件时，请务必完成以下关键设置，这是连接系统与MiniQMT的&quot;握手&quot;环节。</p>
<ol>
<li>
<p><strong>打开设置对话框</strong>:
启动软件后，在主界面顶部工具栏找到并点击 <strong>设置</strong> 按钮，打开&quot;软件设置&quot;对话框。</p>
</li>
<li>
<p><strong>配置MiniQMT双路径</strong>:
在&quot;软件设置&quot;对话框中，找到 <strong>客户端设置</strong> 区域。这里有两个至关重要的路径需要您正确配置，它们共同构成了KHQuant与MiniQMT之间的桥梁。</p>
<p align="center">
    <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/QQ截图20250609233033.png" />
</p>
<ul>
<li>
<p><strong>miniQMT客户端路径</strong>:</p>
<ul>
<li><strong>作用</strong>: 指向MiniQMT的<strong>主程序文件</strong>。</li>
<li><strong>如何设置</strong>: 点击该项旁边的 <strong>浏览...</strong> 按钮，在弹出的文件选择窗口中，找到您的MiniQMT安装目录，并进入 <code>bin.x64</code> 文件夹，选择 <code>XtItClient.exe</code> 文件。</li>
<li><strong>示例路径</strong>: <code>D:\国金证券QMT交易端\bin.x64\XtItClient.exe</code></li>
</ul>
</li>
<li>
<p><strong>miniQMT路径</strong>:</p>
<ul>
<li><strong>作用</strong>: 指向MiniQMT的<strong>用户数据文件夹</strong>，其中包含了账户信息、日志等关键数据。</li>
<li><strong>如何设置</strong>: 点击该项旁边的 <strong>浏览...</strong> 按钮，在弹出的文件夹选择窗口中，找到并选择您本地MiniQMT的 <strong><code>userdata_mini</code></strong> 文件夹。</li>
<li><strong>示例路径</strong>: <code>D:\国金证券QMT交易端\userdata_mini</code></li>
</ul>
</li>
</ul>
<blockquote>
<p>🎯 <strong>路径在哪找？</strong>
这两个路径通常都在您的MiniQMT安装根目录下。例如，如果您的MiniQMT安装在 <code>D:\国金证券QMT交易端</code>，那么按图索骥即可找到。<strong>请务必确保两个路径都已正确设置</strong>，否则系统无法正常工作。</p>
</blockquote>
</li>
<li>
<p><strong>保存设置</strong>:
完成两个路径的配置后，点击&quot;软件设置&quot;对话框右下角的 <strong>保存设置</strong> 按钮。软件会保存您的配置。请注意，此设置仅用于数据回测等功能，与主界面上的MiniQMT连接状态指示灯无关。</p>
</li>
</ol>
<hr>
<h2 id="34-%E5%90%AF%E5%8A%A8%E4%B8%8E%E8%BF%9E%E6%8E%A5">3.4 启动与连接</h2>
<p>上述步骤讲述了主界面的初始设置，但是需要注意的是，在这个初始设置完成后，后续每次使用看海量化系统，都需要按照以下顺序启动软件（即先启动miniQMT，再启动看海量化交易系统）。</p>
<h3 id="341-%E7%B3%BB%E7%BB%9F%E5%85%B3%E7%B3%BB%E8%AF%B4%E6%98%8E%E5%AE%A2%E6%88%B7%E7%AB%AF%E4%B8%8E%E6%8E%A5%E5%8F%A3">3.4.1 系统关系说明：客户端与接口</h3>
<p>为了更好地理解为何必须先启动MiniQMT，这里对看海量化系统与MiniQMT的关系做个简要说明：</p>
<blockquote>
<ul>
<li><strong>看海量化系统</strong>与<strong>MiniQMT</strong>是两个独立的软件。</li>
<li>本软件的角色是一个<strong>接口调用方（客户端）</strong>。它本身不处理与券商服务器的直接通讯。</li>
<li>MiniQMT登录后，会在后台提供一个编程接口（API），所有的数据获取和交易指令都是通过这个接口完成的。</li>
</ul>
<p><strong>简而言之，看海量化系统的工作依赖于MiniQMT提供的接口服务。如果MiniQMT没有启动，本软件就无法连接和工作。</strong></p>
</blockquote>
<h3 id="342-%E6%AD%A3%E7%A1%AE%E7%9A%84%E5%90%AF%E5%8A%A8%E9%A1%BA%E5%BA%8F">3.4.2 正确的启动顺序</h3>
<p>为了确保系统能够顺利连接到MiniQMT，请务必遵循以下两步启动流程：</p>
<ol>
<li>
<p><strong>第一步：启动并登录MiniQMT</strong></p>
<ul>
<li>首先，打开您的券商MiniQMT客户端。</li>
<li>在登录界面，请务必勾选 <strong>&quot;极简模式&quot;</strong>（部分券商版本可能称之为&quot;独立交易&quot;）。</li>
<li>成功登录后，MiniQMT将自动在后台提供数据和交易接口服务。</li>
</ul>
<blockquote>
<p>⚠️ <strong>重要提示</strong>：必须先成功登录MiniQMT，再进行下一步。看海量化系统本身无法启动MiniQMT。</p>
</blockquote>
</li>
<li>
<p><strong>第二步：启动看海量化系统</strong></p>
<ul>
<li>在MiniQMT运行的情况下，双击桌面上的看海量化系统快捷方式来启动软件。</li>
</ul>
</li>
</ol>
<h3 id="343-%E6%A3%80%E6%9F%A5%E8%BF%9E%E6%8E%A5%E7%8A%B6%E6%80%81">3.4.3 检查连接状态</h3>
<p>软件启动后，主界面右上角会有一个 <strong>MiniQMT状态指示灯</strong>，它直观地反映了当前的连接情况。</p>
   <p align="center">
       <img src="https://khsci.com/khQuant/wp-content/uploads/2025/06/QQ截图20250609233411.png" />
   </p>
<ul>
<li><strong>🟢 绿色</strong>: <strong>连接成功</strong>。恭喜！系统已成功连接到MiniQMT，您可以开始进行回测或其他操作了。</li>
<li><strong>🔴 红色</strong>: <strong>连接失败</strong>。警报！这表示系统未能找到正在运行的MiniQMT。请按照以下步骤进行排查：
<ol>
<li><strong>检查启动顺序</strong>：确认您是<strong>先启动并登录了MiniQMT</strong>，然后再打开的本软件。</li>
<li><strong>检查登录状态</strong>：确认您的MiniQMT客户端已经<strong>成功登录</strong>账户，而不仅仅是停留在登录界面。</li>
<li><strong>检查路径配置</strong>：回到 <code>设置</code> -&gt; <code>客户端设置</code>，再次检查您在3.3节中设置的 <strong>两个MiniQMT路径</strong> 是否完全正确。</li>
<li><strong>重启</strong>：尝试依次关闭本软件和MiniQMT客户端，然后重新按照正确的顺序启动它们。</li>
</ol>
</li>
</ul>
<p>完成以上步骤，您的&quot;看海量化交易系统&quot;就已整装待发。下一章，我们将带领您完成第一个回测流程！</p>

</body>
</html>
