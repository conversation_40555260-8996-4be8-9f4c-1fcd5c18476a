from xtquant import xtdata
from typing import Dict, List
import numpy as np
import datetime
# 从 khQTTools 导入信号生成等辅助函数
from khQTTools import generate_signal

# 全局变量
max_holding_prices = {} # 记录每只股票持仓期间的最高价格
buy_dates = {}          # 记录每只股票持仓期间的买入时间
selected_stocks = []    # 记录每日盘前选出的股票


# 初始化函数
def init(stocks=None, data=None):
    pass
    # set_option('use_real_price', True)
    # 设置多个定时任务（修改点1）
    # run_daily(trade, time='09:00')  # 买入时间保持不变
    # run_daily(trade, time='10:10')  # 新增卖出时间
    # run_daily(trade, time='11:00')  # 新增卖出时间
    # run_daily(trade, time='13:00')  # 新增卖出时间
    # run_daily(trade, time='14:00')  # 新增卖出时间
    # run_daily(trade, time='14:50')  # 新增卖出时间
    # 定义全局变量记录每只股票持仓期间的最高价格和买入时间


# 选股函数
def select_stocks(context):
    # 获取股票列表（假设为中证 500 成分股）
    stock_list = xtdata.get_index_weight('000016.SH')   # 上证50 000016.SH  # 中证500  000905.SH
    print(f"股票列表长度: {len(stock_list)}")
    # 剔除科创板和北交所股票
    stock_list = [stock for stock in stock_list if not stock.startswith('688') and not stock.startswith('43') and not stock.startswith('83')]
    print(f"股票列表: {stock_list}")

    # 获取历史数据
    end_date_dt = datetime.datetime.strptime(context['__current_time__']['datetime'], '%Y-%m-%d %H:%M:%S')
    start_date_dt = end_date_dt - datetime.timedelta(days=100)  # 获取100天的数据
    dict_data = xtdata.get_local_data(      # get_market_data_ex
        field_list=['close', 'high', 'low', 'volume'],
        stock_list=stock_list,
        period='1d',
        start_time=start_date_dt.strftime('%Y%m%d'),
        end_time=end_date_dt.strftime('%Y%m%d'),
        count=-1,
        dividend_type='none',
        fill_data=True)   # , data_dir=data_dir

    selected_stocks = []
    for stock, df in dict_data.items():
        # 获取单只股票的数据
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']

        # 计算 WR1 和 WR2
        hhv_21 = high.rolling(window=21).max()  # 21日最高价
        llv_21 = low.rolling(window=21).min()  # 21日最低价
        wr1 = 100 * (hhv_21 - close) / (hhv_21 - llv_21)
        wr1 = 100 - wr1  # REVERSE操作

        hhv_48 = high.rolling(window=48).max()  # 48日最高价
        llv_48 = low.rolling(window=48).min()  # 48日最低价
        wr2 = 100 * (hhv_48 - close) / (hhv_48 - llv_48)
        wr2 = 100 - wr2  # REVERSE操作

        # 计算 X_1V 到 A5
        x_1v = close * volume
        ema_x_1v_3 = x_1v.ewm(span=3, adjust=False).mean()
        ema_vol_3 = volume.ewm(span=3, adjust=False).mean()
        ema_x_1v_6 = x_1v.ewm(span=6, adjust=False).mean()
        ema_vol_6 = volume.ewm(span=6, adjust=False).mean()
        ema_x_1v_9 = x_1v.ewm(span=9, adjust=False).mean()
        ema_vol_9 = volume.ewm(span=9, adjust=False).mean()
        ema_x_1v_12 = x_1v.ewm(span=12, adjust=False).mean()
        ema_vol_12 = volume.ewm(span=12, adjust=False).mean()

        x_2v = (ema_x_1v_3 / ema_vol_3 + ema_x_1v_6 / ema_vol_6 + ema_x_1v_9 / ema_vol_9 + ema_x_1v_12 / ema_vol_12) / 4
        x_2v = x_2v.ewm(span=20, adjust=False).mean()
        x_3v = 1.06 * x_2v
        a4 = (x_3v - (close.ewm(span=1, adjust=False).mean() - x_3v)).ewm(span=3, adjust=False).mean()
        x_4v = np.where(a4 <= x_3v, a4, x_3v)
        a5 = 2 * x_3v - x_4v

        # 计算 VAR1V 和 VAR2A
        var1v = close.shift(1)  # REF(CLOSE, 1)
        var2a = (close - var1v).clip(lower=0).rolling(window=7).mean() / abs(close - var1v).rolling(window=7).mean() * 100

        # 买入信号
        buy_signal = (var2a[-1] > 70) and (a5[-1] > a4[-1]) and (wr1[-1] == wr2[-1])
        print(f"股票: {stock}, WR1: {wr1[-1]}, WR2: {wr2[-1]}, VAR2A: {var2a[-1]}, A5: {a5[-1]}, A4: {a4[-1]}, 买入信号: {buy_signal}")

        if buy_signal:
            selected_stocks.append(stock)

    # 最多选择5只股票
    selected_stocks = selected_stocks[:5]
    print(f"选中的股票: {selected_stocks}")
    return


# 每天盘前选股
def khPreMarket(context: Dict) -> List[Dict]:
    select_stocks(context)
    return


# 交易函数
def khHandlebar(context, data: Dict) -> List[Dict]:
    current_time = context.current_dt.time()

    # 仅在09:30执行买入
    if current_time == datetime.time(9, 30):
        if len(selected_stocks) > 0:     # 买入逻辑
            for stock in selected_stocks:
                current_price = xtdata.get_local_data(      # get_market_data_ex
                    field_list=['open'],   # 当天开盘就用开盘价直接买
                    stock_list=stock,
                    period='1d',
                    # start_time=start_date,
                    end_time=context.current_dt,
                    count=1,
                    dividend_type='none',
                    fill_data=True)   # , data_dir=data_dir
                buy_reason = f"{current_time} | 股票: {stock} | 当前价格: {current_price}"
                print(buy_reason)
                signals = generate_signal(data, stock, current_price, 1/len(selected_stocks), 'buy', buy_reason)
                # 初始化最高价格为买入成本价
                max_holding_prices[stock] = context.portfolio.positions[stock].avg_cost
                # 记录买入时间
                buy_dates[stock] = context.current_dt.date()

    # 卖出逻辑（6个时间点都会执行）（修改点3）
    for stock in list(context.portfolio.positions.keys()):
        position = context.portfolio.positions[stock]
        # 检查持仓股是否可用
        if position.total_amount > 0:
            # current_price = get_price(stock, end_date=context.current_dt, frequency='1d', fields=['close'], count=1)['close'][-1]
            current_price = xtdata.get_local_data(      # get_market_data_ex
                field_list=['close'],   # 卖出时应取当时分钟线的最新价
                stock_list=stock,
                period='1m',
                # start_time=start_date,
                end_time=context.current_dt,
                count=1,
                dividend_type='none',
                fill_data=True)   # , data_dir=data_dir
            sell_reason = f"{current_time} | 股票: {stock} | 当前价格: {current_price}"

            # 更新最高价格
            if stock not in max_holding_prices:
                max_holding_prices[stock] = current_price
            else:
                max_holding_prices[stock] = max(max_holding_prices[stock], current_price)

            # 以持仓期间的股价最高价作为计算基数，回落5%卖出
            if current_price < max_holding_prices[stock] * 0.95:
                print(f"{current_time} | 股票: {stock} | 触发盈利回落5%卖出")
                # order_target(stock, 0)
                signals = generate_signal(data, stock, current_price, 1.0, 'sell', sell_reason)
                print(sell_reason)

                if stock in max_holding_prices:
                    del max_holding_prices[stock]
                if stock in buy_dates:
                    del buy_dates[stock]
                continue