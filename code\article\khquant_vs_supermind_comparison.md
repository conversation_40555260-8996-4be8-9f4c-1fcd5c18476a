> 我是Mr.看海，我在尝试用信号处理的知识积累和思考方式做量化交易，应用深度学习和AI实现股票自动交易，目的是实现财务自由~
> 目前我正在开发基于miniQMT的量化交易系统——看海量化交易系统（KhQuant 是其核心框架）。

## 一、 引言

大家好，我是 Mr.看海。在开发我的"看海量化交易系统"（KhQuant）的过程中，有一个必须证明的问题：**这个我自己构建的回测引擎，它的准确性到底如何？** 尤其是在模拟真实的交易成本（如滑点、手续费）时，它能否与成熟的商业平台保持一致？

为了解答这个问题，并测试 KhQuant 的设计理念和能力，我进行了一系列对比实验：选择国内成熟平台，用它和我开发的 KhQuant 运行完全相同的双均线策略，并尽可能对齐所有回测参数。

本文是系列测试的第一篇，目的不仅在于验证 KhQuant 在核心指标和交易细节上能否复现成熟平台的结果，更希望借此机会，阐述我开发 KhQuant 的一些思考：**为何选择本地化？为何强调 Python 生态集成？为何追求框架的灵活性和透明度？** 希望通过这次对比，能让大家更深入地了解 KhQuant，并为正在选择或开发量化工具的朋友提供一些参考。

本篇选择的对比评测的平台是同花顺的Supermind（下一篇对比聚宽）。

## 二、 平台简介

### 2.1. 同花顺 Supermind (参照系)

同花顺 Supermind 是我们本次对比的参照对象。作为国内知名的金融信息服务商推出的云策略平台，它拥有庞大的用户基础和丰富的数据资源。其主要特点是：

* **云端集成**: 用户无需担心本地环境，一切在线完成，降低了入门门槛。
* **标准化**: 提供统一的开发界面、函数库和回测报告，适合快速验证和标准化流程。
* **广泛用户**: 拥有较大规模的社区，便于交流和学习。


### 2.2. KhQuant (我的探索与实践)

KhQuant 是我开发的"看海量化交易系统"的核心回测与交易引擎。开发它的初衷，源于我对现有平台在某些方面（如灵活性、透明度、与 Python AI 生态的结合）的思考和探索。我希望构建一个这样的系统：

* **深度整合 Python 生态**: 量化的未来离不开数据科学和 AI。KhQuant 被设计为纯 Python 实现，能无缝利用 Pandas, NumPy 进行数据处理，方便集成 Scikit-learn, PyTorch/TensorFlow 等进行复杂的模型训练和策略实现，这对于我探索 AI 驱动的交易至关重要。
* **本地化运行，掌控在我**: 与云平台不同，KhQuant 在开发者本地运行。这意味着我可以完全掌控运行环境，利用本地强大的计算资源（CPU/GPU），并且方便地使用各种熟悉的 Python IDE 和调试工具进行开发，提升迭代效率。
* **追求透明与灵活**: 我认为理解回测引擎的内部机制很重要。KhQuant 旨在做到核心逻辑的透明化，并提供足够的灵活性让用户可以根据自己的理解去定制或扩展，例如修改框架结构，或者接入不同的数据源或交易接口。

简而言之，KhQuant 是我作为一个有信号处理和 AI 背景的开发者，为满足自己及同好对于**灵活性、透明度、Python 集成和本地化开发**的需求而构建的工具。

## 三、 测试硬件平台

本次 KhQuant 回测运行在我的本地开发机器上，配置如下（注意以下配置并非运行看海量化系统的最低要求，最低配置还需后续测试后明确，会在后续文章中明确）：

* **CPU**: Intel Core i7-12700K
* **内存**: 64GB
* **操作系统**: Windows 11
* **硬盘**: SSD
* **显示器分辨率**: 3840*2160

（注：同花顺 Supermind 为云平台，其硬件由服务商提供，我们无法直接控制或了解其细节。）

## 四、 策略简介与实现

我选择了一个非常经典、简单且容易理解的策略：**双移动平均线（Dual Moving Average, DMA）策略**，或者叫金叉死叉策略。这是一种典型的**趋势跟踪**策略，它的核心思想是通过比较两条不同周期的价格平均线来判断市场趋势，并据此产生交易信号。

**什么是移动平均线 (Moving Average, MA)？**

想象一下股票每天的价格像心电图一样上下波动，有时候很难看清大的方向。移动平均线就像给这个波动的心电图加了一个"平滑滤镜"。

* **5日均线 (MA5)**: 就是把最近5天的收盘价加起来，再除以5，得到一个平均价。每天都这么算，连起来就是一条相对平滑的线。因为它只看最近5天，所以对价格的短期变化比较敏感，反应快。
* **20日均线 (MA20)**: 同理，是最近20天收盘价的平均值。因为它看的时间更长，所以更能反映价格的中长期趋势，反应相对慢一些，更"稳重"。

![1745412731227](images/khquant_vs_supermind_comparison/1745412731227.png)

**双均线策略如何运作？**

这个策略就是观察这两条快慢不同的均线的相对位置和交叉情况：

1. **"金叉" (Golden Cross) - 买入信号**: 当反应快的 **MA5**（短期均线）从下方**上穿**反应慢的 **MA20**（长期均线）时，就像短跑选手超过了长跑选手。这通常被解读为：短期价格上涨势头强劲，可能预示着一波上涨趋势的开始。如果这时候我们手里没有这只股票（空仓），策略就会发出**买入**信号。
2. **"死叉" (Death Cross) - 卖出信号**: 当反应快的 **MA5** 从上方**下穿**反应慢的 **MA20** 时，就像短跑选手掉头落后了。这通常被解读为：短期价格下跌势头明显，可能预示着一波下跌趋势的开始，或者之前的上涨趋势结束了。如果这时候我们手里正好持有这只股票，策略就会发出**卖出**信号，清空持仓，规避风险。

**总结一下策略规则：**

* **交易标的**: 我们这次只针对一只股票——华林证券 (002945.SZ)。
* **计算指标**: 计算它的 5 日简单移动平均线 (MA5) 和 20 日简单移动平均线 (MA20)。
* **交易信号**:
  * **买入**: MA5 上穿 MA20（金叉），并且当前没有持仓时，全仓买入。
  * **卖出**: MA5 下穿 MA20（死叉），并且当前持有仓位时，全部卖出。
* **运行频率**: 日级 (Daily)。

这个策略简单直观，旨在抓住市场的主要趋势波动。因为它逻辑清晰、易于实现，是检验回测平台功能和准确性的一个很好的"试金石"。

### 4.1. 同花顺 Supermind 策略实现

```python
# 股票策略模版
# 初始化函数,全局只运行一次
def init(context):
    # 设置基准收益：沪深300指数
    set_benchmark('000300.SH')
    # 打印日志
    log.info('策略开始运行,初始化函数全局只运行一次')
    # 设置股票每笔交易的手续费为万分之一(手续费在买卖成交后扣除,不包括税费,税费在卖出成交后扣除)
    set_commission(PerShare(type='stock',cost=0.0001)) # 默认最低佣金5元，默认卖出时扣千分之一印花税
    # 设置股票交易滑点1%,表示买入价为实际价格乘1.01,卖出价为实际价格乘0.99
    set_slippage(PriceSlippage(0.01))
    # 设置日级最大成交比例25%,分钟级最大成交比例50%
    set_volume_limit(0.25,0.5)
    # 设置要操作的股票：华林证券
    context.security = '002945.SZ'
    # 回测区间、初始资金、运行频率请在右上方设置

#每日开盘前9:00被调用一次,用于储存自定义参数、全局变量,执行盘前选股等
def before_trading(context):
    # 获取日期
    date = get_datetime().strftime('%Y-%m-%d %H:%M:%S')
    # 打印日期
    log.info('{} 盘前运行'.format(date))

## 开盘时运行函数
def handle_bar(context, bar_dict):
    # 获取时间
    time = get_datetime().strftime('%Y-%m-%d %H:%M:%S')
    # 打印时间
    log.info('{} 盘中运行'.format(time))

    # 获取股票过去20天的收盘价数据
    closeprice = history(context.security, ['close'], 20, '1d', False, 'pre', is_panel=1)
    # 计算20日均线
    MA20 = closeprice['close'].mean()
    # 计算5日均线
    MA5 = closeprice['close'].iloc[-5:].mean()
  
    log.info("5日均线: {:.2f}, 20日均线: {:.2f}".format(MA5, MA20))
    # 获取当前账户当前持仓市值
    market_value = context.portfolio.stock_account.market_value
    # 获取账户持仓股票列表
    stocklist = list(context.portfolio.stock_account.positions)

    # 如果5日均线大于20日均线,且账户当前无持仓,则全仓买入股票
    if MA5 > MA20 and len(stocklist) == 0:
        # 记录这次买入
        log.info("5日均线大于20日均线, 买入 %s" % (context.security))
        # 按目标市值占比下单
        order_target_percent(context.security, 1)

    # 如果5日均线小于20日均线,且账户当前有股票市值,则清仓股票
    elif MA20 > MA5 and market_value > 0:
        # 记录这次卖出
        log.info("5日均线小于20日均线, 卖出 %s" % (context.security))
        # 卖出所有股票,使这只股票的最终持有量为0
        order_target(context.security, 0)

## 收盘后运行函数,用于储存自定义参数、全局变量,执行盘后选股等
def after_trading(context):
    # 获取时间
    time = get_datetime().strftime('%Y-%m-%d %H:%M:%S')
    # 打印时间
    log.info('{} 盘后运行'.format(time))
    log.info('一天结束')
```

### 4.2. KhQuant 策略实现 (`ths.py`)

```python
# coding: utf-8
from typing import Dict, List
import numpy as np
from xtquant import xtdata
import datetime
import logging
import os
import json
# 从 khQTTools 导入信号生成等辅助函数
from khQTTools import generate_signal, calculate_max_buy_volume

# 全局变量
position = {}  # 仅用于记录操作
params = {}
risk_params = {}
stock_list = []

def init(stocks=None, data=None):
    """策略初始化"""
    global position, params, risk_params
  
    # 初始化持仓记录
    position = {}
    stock_code = stocks[0] if stocks else '002945.SZ'
  
    # 初始化策略参数
    params = {
        "short_ma_period": 5,  # 短期均线周期
        "long_ma_period": 20,  # 长期均线周期
        "stock_code": stock_code  # 交易标的
    }
  
    # 初始化风控参数
    risk_params = {
        "max_position": 1.0  # 最大持仓比例（全仓）
    }
  
    print(f"策略初始化完成，交易标的：{stock_code}")

def calculate_ma(code: str, short_period: int, long_period: int, current_date: str = None) -> tuple:
    """计算移动平均线"""
    try:
        # 获取历史收盘价数据，以当前日期为结束日期
        history_data = xtdata.get_market_data(
            field_list=["close"],
            stock_list=[code],
            period="1d",
            start_time="20240101",  # 使用较早的起始日期
            end_time=current_date,  # 使用当前回测日期作为结束日期
            dividend_type='front'
        )
      
        # 计算均线
        closes = history_data['close'].values[0]
  
        # 排除最后一个数据点（当日数据），只使用之前的数据
        closes = closes[:-1]
  
        # 计算均线，使用最后需要的几个数据点
        ma_long = round(np.mean(closes[-long_period:]), 2)
        ma_short = round(np.mean(closes[-short_period:]), 2)
          
        return ma_short, ma_long
  
    except Exception as e:
        logging.error(f"计算均线时发生错误: {str(e)}")
        return None, None

def khHandlebar(data: Dict) -> List[Dict]:
    """策略主逻辑，在每个K线或Tick数据到来时执行"""
    signals = []
  
    # 获取当前时间信息
    current_time = data.get("__current_time__", {})
    current_date_str = current_time.get("date", "")  
  
    # 获取股票代码
    stock_code = params["stock_code"]
  
    # 获取当前价格
    stock_data = data.get(stock_code, {})
    current_price = stock_data.get("close", 0)
  
    # 格式化日期用于计算均线
    date_parts = current_date_str.split("-")
    current_date_formatted = f"{date_parts[0]}{date_parts[1]}{date_parts[2]}"
  
    # 计算均线
    ma_short, ma_long = calculate_ma(
        stock_code, 
        params["short_ma_period"], 
        params["long_ma_period"],
        current_date_formatted
    )
  
    logging.info(f"计算结果 - 短期均线: {ma_short:.2f}, 长期均线: {ma_long:.2f}")
  
    # 获取持仓信息
    positions_info = data.get("__positions__", {})
    has_position = stock_code in positions_info and positions_info[stock_code].get("volume", 0) > 0
  
    # 交易逻辑: 使用 generate_signal 生成信号
    if ma_short > ma_long and not has_position:
        # 金叉且无持仓，生成全仓买入信号
        buy_reason = f"5日线({ma_short:.2f}) 上穿 20日线({ma_long:.2f})，全仓买入"
        signals = generate_signal(data, stock_code, current_price, 1.0, 'buy', buy_reason)

    elif ma_short < ma_long and has_position:
        # 死叉且有持仓，生成全仓卖出信号
        sell_reason = f"5日线({ma_short:.2f}) 下穿 20日线({ma_long:.2f})，全仓卖出"
        signals = generate_signal(data, stock_code, current_price, 1.0, 'sell', sell_reason)

    return signals

# khPreMarket 和 khPostMarket 函数省略，本次策略未使用
```

### 4.3. 策略编写差异分析

两个平台的策略实现思路基本是相同的，不过从代码也可以看出一些差异点：

* **数据获取**: Supermind 使用平台封装的 `history` 函数；KhQuant 则可以直接调用底层数据接口（如 `xtdata.get_market_data`），这提供了更多控制权但也需要开发者自行处理可能的异常。
* **交易执行**: Supermind 使用 `order_target` 系列函数；KhQuant 中我将其封装为 `generate_signal`，目的是将"产生意图"和"执行下单"分离，提高模块化程度。

## 五、 回测环境与参数设置

确保回测参数设置的同步至关重要。我仔细检查并设置了两个平台的回测参数，力求完全一致：


| 参数项       | 同花顺 Supermind         | KhQuant                          | 说明                          |
| ------------ | ------------------------ | -------------------------------- | ----------------------------- |
| **回测区间** | 2024-04-03 至 2024-11-01 | 2024-04-03 至 2024-11-01         | ✅**一致**                    |
| **初始资金** | 1,000,000.00 CNY         | 1,000,000.00 CNY                 | ✅**一致**                    |
| **交易标的** | 002945.SZ (华林证券)     | 002945.SZ (华林证券)             | ✅**一致**                    |
| **运行频率** | 日级 (Daily)             | 日级 (自定义定时 09:30:00)       | ✅**一致**                    |
| **基准指数** | 000300.SH (沪深300)      | sh.000300 (沪深300)              | ✅**一致** (代码格式略有差异) |
| **佣金比例** | 0.0001 (万分之一)        | 0.0001 (万分之一)                | ✅**一致**                    |
| **最低佣金** | 默认 5.0 元              | 5.0 元                           | ✅**一致**                    |
| **印花税**   | 默认 0.001 (卖出时扣除)  | 0.001 (卖出时计算)               | ✅**一致**                    |
| **滑点类型** | 价格比例滑点             | 按成交金额比例                   | ✅**一致**  |
| **滑点值**   | 0.01 (1%)                | 0.01 (1%)                        | ✅**一致**                    |
| **复权方式** | 前复权 (`pre`)           | 前复权 (`dividend_type='front'`) | ✅**一致**                    |

![1745411703553](images/khquant_vs_supermind_comparison/1745411703553.png)
在看海回测系统中，很多参数是通过前台页面设置的（**也可以通过配置文件的方式实现纯代码设置**，这个后续再补充演示），在Supermind中则是纯代码设置。

### 一些说明

在深入对比之前，基于我对两个平台机制的理解，理论上存在一些潜在差异点。

1. **滑点实现机制**: 在Supermind中，所设置的滑点比例实际为“双边滑点”，在实际计算成交价时，需要将此滑点设置值除以2，比如滑点设置的为1%，那么在买入时成交价实际计算公式为
**委托价×(1+0.001/2)**
。KhQuant 中我将滑点模型改为与 Supermind 完全一致。
2. **平台架构**: **云 vs 本地**。Supermind 作为云平台，优点是免维护、标准化。KhQuant 作为本地框架，优势在于**灵活性、可控性、与本地 Python 环境的深度集成**，以及不受网络延迟影响的稳定性。
3. **指标体系**: 两个平台提供的分析指标各有侧重。

## 六、 运行效率对比

效率是衡量框架性能的一个方面，但并非唯一标准。

* **同花顺 Supermind**: 完成回测在网页显示耗时约 **1 秒**，（实际观感不止1s，可能是受网页加载的影响，就不过多讨论了）。
* **KhQuant**: 在我的本地机器上，总耗时 **2.0662 秒**。我在软件日志输出中我特意加入了耗时统计，结果如下：
    *   构造数据: 0.0168秒 (0.82%)
    *   构造时间信息: 0.0016秒 (0.08%)
    *   检查新日期: 0.0000秒 (0.00%)
    *   盘后回调: 0.0000秒 (0.00%)
    *   盘前回调: 0.0000秒 (0.00%)
    *   触发器检查: 0.0000秒 (0.00%)
    *   风控检查: 0.0000秒 (0.00%)
    *   **策略处理**: 1.3133秒 (63.56%)
    *   处理信号: 0.0010秒 (0.05%)
    *   交易指令: 0.0110秒 (0.53%)
    *   **记录结果**: 0.7174秒 (34.72%)

**我的解读**:

* Supermind 作为成熟的云平台，针对回测场景做了深度优化，其效率确实很高。
* KhQuant 的耗时主要在"策略处理"（63%）和"记录结果"（34%）。提供这个 breakdown 的目的，正是**赋能 KhQuant 用户**：如果你的策略运行缓慢，可以清晰地看到瓶颈在哪里。是策略逻辑（如 `calculate_ma`）太复杂？还是记录了太多自定义数据导致 I/O 变慢？用户可以据此进行针对性优化。这是本地框架透明化带来的好处。
* 虽然 KhQuant 此次稍慢，但对于复杂策略的开发，**本地调试的便利性、不受网络影响的稳定性**可能比单纯的回测速度更重要。而且，我知道我的优化方向在哪里。

## 七、 回测结果对比：KhQuant 准确性验证

现在到了最关键的部分：在参数严格对齐后，KhQuant 的回测结果与 Supermind 是否一致？

### 7.1. 主要绩效指标


| 指标                   | 同花顺 Supermind | KhQuant 结果 | 对比结果 & 说明                            |
| ---------------------- | ---------------- | ------------ | ------------------------------------------ |
| **总收益率 (%)**       | +15.48           | +15.48       | ✅**完全一致**                             |
| **年化收益率 (%)**     | +29.31           | +29.31       | ✅**完全一致**                             |
| **基准收益率 (%)**     | +8.64            | +8.64        | ✅**完全一致**                             |
| **基准年化收益率 (%)** | +15.95           | +15.95       | ✅**完全一致**                             |
| **最大回撤 (%)**       | 19.68            | 19.68        | ✅**完全一致**                             |
| **夏普比率 (年化)**    | 0.82             | 0.82         | ✅**完全一致**                             |
| **索提诺比率 (年化)**  | 2.06             | 2.05         | ✅**基本一致**                             |
| **信息比率 (年化)**    | 0.70             | N/A          | Supermind 提供, KhQuant 未计算             |
| **贝塔 (年化)**        | 1.18             | 1.19         | ✅**基本一致**(KhQuant ~1.1873)            |
| **阿尔法 (年化)**      | 0.11             | 0.11         | ✅**完全一致**            |
| **年化波动率 (%)**     | 34.00            | 33.83        | ✅**基本一致，差异是保留小数位不同导致的** |
| **胜率 (%)**           | 33.33            | 33.33        | ✅**完全一致**                             |
| **盈亏比**             | N/A              | 3.01         | KhQuant提供，Supermind未提供                                    |


**分析**:

* **一致性**: 结果一致性还是很高的。除了个别衍生指标（如索提诺比率、贝塔）因计算精度或取整方式导致可以忽略不计的微小差异外，所有核心指标，包括总收益率、年化收益率、最大回撤、夏普比率、胜率等，**完全一致**。
* 这有力地证明了 KhQuant 的核心回测引擎在**处理行情数据、执行交易指令、计算资金变化、应用手续费和滑点**等方面，达到了与成熟商业平台相当的准确度。

需要补充说明一点，在很多指标计算的时候（比如阿尔法），需要用到**无风险收益率**这个数据，这个数据在国内通常使用十年期国债收益率，由于该数据在MiniQMT中获取需要研投版（也就是收费的），所以我在看海回测系统中的设置界面里提供了手动输入的方式，无风险收益率的数可以在下边这个网址查看

https://cn.investing.com/rates-bonds/china-10-year-bond-yield

![1745416265309](images/khquant_vs_supermind_comparison/1745416265309.png)

![1745416347287](images/khquant_vs_supermind_comparison/1745416347287.png)

### 7.2. 收益曲线与交易记录

* **收益曲线**: 两者的收益曲线在视觉上几乎完美重合，再次印证了整体表现的一致性。


* **交易记录**: 对比两份详细的交易流水：

**同花顺 Supermind 交易记录**:


| 日期       | 时间     | 代码/名称          | 买卖操作 | 成交价 | 成交数量 | 成交金额      | 费率     | 平仓盈亏   |
| ---------- | -------- | ------------------ | -------- | ------ | -------- | ------------- | -------- | ---------- |
| 2024-10-30 | 09:31:00 | 002945.SZ 华林证券 | 卖出     | 12.139 | -95200   | -1,155,632.80 | 1,271.20 | 237,051.35 |
| 2024-09-11 | 09:31:00 | 002945.SZ 华林证券 | 买入     | 9.648  | 95200    | 918,489.60    | 91.85    | --         |
| 2024-08-13 | 09:31:00 | 002945.SZ 华林证券 | 卖出     | 9.970  | -92200   | -919,224.78   | 1,011.15 | -34,352.26 |
| 2024-07-22 | 09:31:00 | 002945.SZ 华林证券 | 买入     | 10.341 | 92200    | 953,481.69    | 95.35    | --         |
| 2024-05-22 | 09:31:00 | 002945.SZ 华林证券 | 卖出     | 11.283 | -84600   | -954,567.18   | 1,050.02 | -44,552.97 |
| 2024-04-30 | 09:31:00 | 002945.SZ 华林证券 | 买入     | 11.809 | 84600    | 999,020.25    | 99.90    | --         |

**KhQuant 交易记录**:


| 交易时间            | 证券代码  | 交易方向 | 成交价格 | 成交数量 | 成交金额     | 手续费  |
| ------------------- | --------- | -------- | -------- | -------- | ------------ | ------- |
| 2024-04-30 09:30:00 | 002945.SZ | 买入     | 11.809   | 84600    | 999,020.25   | 99.90   |
| 2024-05-22 09:30:00 | 002945.SZ | 卖出     | 11.283   | 84600    | 954,567.18   | 1050.02 |
| 2024-07-22 09:30:00 | 002945.SZ | 买入     | 10.341   | 92200    | 953,481.69   | 95.35   |
| 2024-08-13 09:30:00 | 002945.SZ | 卖出     | 9.970    | 92200    | 919,224.78   | 1011.15 |
| 2024-09-11 09:30:00 | 002945.SZ | 买入     | 9.648    | 95200    | 918,489.60   | 91.85   |
| 2024-10-30 09:30:00 | 002945.SZ | 卖出     | 12.139   | 95200    | 1,155,632.80 | 1271.20 |

**交易记录对比分析**:

* **一致性强**: 所有的交易细节，包括**日期、时间、方向、成交价格、成交数量、成交金额、手续费，全部完全一致**。
* **关于成交价小数位数的说明与疑问**: 细心的读者可能注意到，两个平台的成交价都显示到了小数点后三位。Supermind 本身就显示三位小数。在我当前的 KhQuant 实现中，为了在本次对比中精确匹配 Supermind 的滑点影响下的价格，我也将成交价处理并展示为三位小数。 **然而，根据国内 A 股市场的实际交易规则，股票的最小价格变动单位是 0.01 元，即成交价格通常只精确到小数点后两位。** 目前为了对比而保留三位小数的做法，虽然有助于验证滑点计算逻辑本身，但与真实成交的最小变动单位存在偏差。这一点我已注意到，**在 KhQuant 后续的版本中，我很可能会将成交价的最终处理调整为更贴近实际的两位小数**，以进一步提升回测的仿真度。这也是本地框架灵活性的体现，我们可以根据对真实交易规则的理解来不断迭代优化。
* **结论**: 交易层面的完全一致，是对 KhQuant 回测引擎准确性的最终确认。这表明，至少在这个策略和参数下，KhQuant 能够精确模拟交易过程。

## 八、 下一步计划与内测说明

这次与同花顺 Supermind 的对比验证，初步证明了 KhQuant 在参数对齐的情况下，其回测结果的准确性是可靠的。这对我来说是一个重要的里程碑。

然而，要充分证明一个量化系统的稳定性和有效性，仅靠单一策略、单一平台的对比是远远不够的。因此，我的下一步计划包括：

1.  **更多策略类型的验证**: 我将使用更多不同类型的策略在 KhQuant 和其他平台上进行回测对比，以确保 KhQuant 在处理不同策略逻辑时的准确性和鲁棒性。
2.  **与其他平台的交叉对比**: 除了 Supermind，我还会将 KhQuant 与国内另一主流平台——**聚宽 (JoinQuant)** 进行类似的对比测试。通过与多个成熟平台的比较，可以更全面地评估 KhQuant。
3.  **持续优化与迭代**: 根据测试中发现的问题（例如之前提到的效率优化空间、成交价小数位数处理等），持续对 KhQuant 进行迭代和改进。

**内测计划说明**: 

在完成上述更充分的验证测试之后，我计划启动"看海量化交易系统 (KhQuant)"的 Beta 内测阶段。

*   **优先体验**: 为了感谢大家的支持，通过**我推荐的渠道开通 MiniQMT 账户的朋友**，在内测开始后将获得优先体验 Beta 版本软件的资格。
*   **公开与开源**: 请已经开通账户或暂时不方便通过推荐渠道开户的朋友放心，**内测结束后，软件将会公开发布，核心代码也计划进行开源**，届时所有人都可以使用和参与改进。

我的目标是打造一个真正可靠、灵活且开放的量化工具，严格的测试是必经之路。

## 九、 关于开通 MiniQMT 

**什么是 MiniQMT？**

MiniQMT 是迅投（QMT）系统提供的一个程序化交易接口（API）。QMT 是目前国内许多券商采用的主流柜台系统之一，而 MiniQMT 允许用户通过编程方式（主要是 Python）连接到证券公司的交易服务器，进行行情获取、策略计算、下单交易等操作。它通常由支持 QMT 的券商**免费**提供给客户使用（可能需要满足一定的资产要求），以其稳定性和执行效率受到不少量化交易者的青睐。

**看海量化交易系统 (KhQuant) 与 MiniQMT**

我正在开发的"看海量化交易系统 (KhQuant)"正是**基于 MiniQMT 接口**进行构建的。这意味着，使用该软件需要一个 MiniQMT 账户作为底层支持。

**推荐开通渠道**

如果您还没有 MiniQMT 账户，并希望未来能够顺利使用"看海量化交易系统 (KhQuant)"进行策略回测，或者希望支持我的开发工作，请大家关注一下我的公众号“看海的城堡”，在公众号页面下方点击相应标签即可获取开通方式。

选择推荐渠道并非强制要求，但这样做一方面能确保您开通的账户类型与 KhQuant 兼容，另一方面也能对我正在进行的开发工作提供支持和肯定。再次感谢大家的关注！

## 十、 免责声明

本文所有内容仅供学习和技术交流使用，不构成任何投资建议。所述策略及回测结果仅为历史数据模拟，不代表未来实际表现。投资者据此操作，风险自担。